import React from 'react';
import LoginForm from "../../components/auth/LoginForm";
import Footer from "../../components/common/Footer";
import ScrollToTop from "../../components/common/ScrollToTop";
import "../../styles/pages/LoginPage.scss";

import GuestNavbar from '../../components/guest/GuestNavbar';

export default function LoginPage() {

    return (
        <>
            <GuestNavbar />
            <div className="auth-page__container">
                <div className="auth-page__content">
                    <div className="auth-page__left">
                        <LoginForm />
                    </div>
                    <div className="auth-page__right">
                        <img className="auth-page__image-real" src={"https://www.freevector.com/uploads/vector/preview/33126/donateilust02.jpg"} alt="Blood drop on hand" />
                    </div>
                </div>
            </div>
            <Footer />
            <ScrollToTop />
        </>
    );
}