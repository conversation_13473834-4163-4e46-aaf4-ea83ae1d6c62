@use "../base/variables" as vars;
@use "../base/mixin" as mix;

.blood-request-form-page {
  @include mix.body-base;
  min-height: 100vh;
  padding: 0;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 50%, #cbd5e1 100%);

  // Hero Section - Similar to BloodDonation
  .hero-section {
    position: relative;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
    padding: 60px 0 40px;
    text-align: center;
    overflow: hidden;

    // Background decorations
    .hero-decoration-1 {
      position: absolute;
      top: -50px;
      right: -50px;
      width: 200px;
      height: 200px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      filter: blur(60px);
    }

    .hero-decoration-2 {
      position: absolute;
      bottom: -30px;
      left: -30px;
      width: 150px;
      height: 150px;
      background: rgba(255, 255, 255, 0.08);
      border-radius: 50%;
      filter: blur(40px);
    }

    .hero-content {
      position: relative;
      z-index: 2;
      max-width: 800px;
      margin: 0 auto;
      padding: 0 20px;

      .hero-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: white;
        margin: 0 0 16px;
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        letter-spacing: 0.5px;

        @media (max-width: 768px) {
          font-size: 2rem;
        }
      }

      .hero-subtitle {
        font-size: 1.25rem;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
        font-weight: 400;
        line-height: 1.5;

        @media (max-width: 768px) {
          font-size: 1.1rem;
        }
      }
    }
  }

  // Request Content
  .request-content {
    min-height: calc(100vh - 80px);
    padding-bottom: 40px;
  }

  // Steps Navigation
  .steps-navigation {
    padding: 40px 20px;
    max-width: 1200px;
    margin: 0 auto;

    .steps-container {
      background: white;
      border-radius: 16px;
      padding: 32px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
      border: 1px solid #e5e7eb;

      .steps-wrapper {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 24px;
        position: relative;

        @media (max-width: 768px) {
          flex-direction: column;
          gap: 20px;
        }

        .step-item {
          flex: 1;
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;

          @media (max-width: 768px) {
            flex-direction: row;
            align-items: center;
            text-align: left;
            width: 100%;
          }

          .step-indicator {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;

            @media (max-width: 768px) {
              flex-direction: row;
              text-align: left;
              gap: 16px;
            }

            .step-circle {
              width: 56px;
              height: 56px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #f8fafc;
              color: #64748b;
              font-size: 20px;
              margin-bottom: 12px;
              border: 3px solid #e2e8f0;
              transition: all 0.3s ease;
              position: relative;
              z-index: 2;

              @media (max-width: 768px) {
                width: 48px;
                height: 48px;
                font-size: 18px;
                margin-bottom: 0;
                flex-shrink: 0;
              }

              &.active {
                background: #3b82f6;
                color: white;
                border-color: #3b82f6;
                box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
                transform: scale(1.05);
              }

              &.completed {
                background: #10b981;
                color: white;
                border-color: #10b981;
                box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.2);
              }
            }

            .step-info {
              @media (max-width: 768px) {
                flex: 1;
              }

              .step-title {
                font-size: 16px;
                font-weight: 600;
                color: #64748b;
                margin-bottom: 4px;
                transition: color 0.3s ease;

                &.active {
                  color: #3b82f6;
                }

                &.completed {
                  color: #10b981;
                }
              }

              .step-description {
                font-size: 14px;
                color: #94a3b8;
                line-height: 1.4;
                transition: color 0.3s ease;

                &.active {
                  color: #64748b;
                }
              }
            }
          }

          .step-connector {
            position: absolute;
            top: 28px;
            left: calc(50% + 28px);
            width: calc(100% - 56px);
            height: 3px;
            background: #e2e8f0;
            transition: background 0.3s ease;
            z-index: 1;

            @media (max-width: 768px) {
              display: none;
            }

            &.completed {
              background: #10b981;
            }
          }
        }
      }

      .progress-bar-container {
        .custom-progress {
          height: 8px;
          border-radius: 8px;
          background: #f1f5f9;

          .progress-bar {
            border-radius: 8px;
            transition: width 0.5s ease;
          }
        }
      }
    }
  }

  // Form Container
  .form-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;

    .main-form-card {
      border: none;
      border-radius: 20px;
      box-shadow: 0 12px 48px rgba(0, 0, 0, 0.12);
      overflow: hidden;
      background: white;

      .form-header {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        border: none;
        padding: 32px;
        position: relative;
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          top: -50%;
          right: -20%;
          width: 200px;
          height: 200px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 50%;
          filter: blur(60px);
        }

        .header-content {
          display: flex;
          align-items: center;
          gap: 20px;
          position: relative;
          z-index: 2;

          @media (max-width: 768px) {
            gap: 16px;
          }

          .header-icon {
            width: 56px;
            height: 56px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            flex-shrink: 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);

            @media (max-width: 768px) {
              width: 48px;
              height: 48px;
              font-size: 20px;
            }
          }

          .header-text {
            flex: 1;

            .header-title {
              font-size: 24px;
              font-weight: 700;
              color: white;
              margin: 0 0 4px;
              text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

              @media (max-width: 768px) {
                font-size: 20px;
              }
            }

            .header-description {
              font-size: 16px;
              color: rgba(255, 255, 255, 0.9);
              margin: 0;
              font-weight: 400;

              @media (max-width: 768px) {
                font-size: 14px;
              }
            }
          }
        }

        .step-counter {
          position: absolute;
          top: 24px;
          right: 24px;

          .badge {
            background: rgba(255, 255, 255, 0.9) !important;
            color: #1e40af !important;
            font-weight: 700;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
          }

          @media (max-width: 768px) {
            position: static;
            align-self: flex-start;
            margin-top: 12px;
          }
        }
      }

      .form-body {
        padding: 40px;

        @media (max-width: 768px) {
          padding: 24px;
        }

        .step-content {
          .section-title {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 32px;
            padding: 20px 24px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 12px;
            border: 2px solid #e2e8f0;

            .section-icon {
              color: #3b82f6;
              font-size: 20px;
            }

            h5 {
              color: #1e293b;
              font-weight: 700;
              margin: 0;
              font-size: 18px;
            }
          }

          .form-grid {
            .form-group-row {
              display: grid;
              gap: 20px;
              margin-bottom: 24px;

              &:not(.quantity-row) {
                grid-template-columns: 1fr 1fr;

                @media (max-width: 768px) {
                  grid-template-columns: 1fr;
                }
              }

              &.quantity-row {
                grid-template-columns: 2fr 1fr;

                @media (max-width: 768px) {
                  grid-template-columns: 1fr;
                }
              }

              .form-group {
                &.flex-1 {
                  grid-column: span 1;
                }
                &.flex-2 {
                  grid-column: span 2;
                  @media (max-width: 768px) {
                    grid-column: span 1;
                  }
                }
              }
            }

            .form-group {
              margin-bottom: 24px;

              .form-label {
                display: block;
                font-weight: 700;
                color: #1f2937;
                font-size: 15px;
                margin-bottom: 8px;
                letter-spacing: 0.3px;

                .required {
                  color: #ef4444;
                  font-weight: 600;
                }
              }

              .form-input,
              .form-select,
              .form-textarea {
                width: 100%;
                padding: 12px 16px;
                border: 2px solid #e2e8f0;
                border-radius: 12px;
                font-size: 15px;
                transition: all 0.3s ease;
                background: white;

                &:focus {
                  outline: none;
                  border-color: #3b82f6;
                  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
                }

                &.is-invalid {
                  border-color: #ef4444;
                  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
                }

                &::placeholder {
                  color: #9ca3af;
                }
              }

              .form-textarea {
                min-height: 120px;
                resize: vertical;
                line-height: 1.6;
              }

              .unit-input {
                background: #f8fafc !important;
                color: #64748b;
                font-weight: 600;
                text-align: center;
              }

              .error-message {
                color: #ef4444;
                font-size: 14px;
                margin-top: 6px;
                font-weight: 500;
              }
            }
          }
        }

        .form-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 40px;
          padding-top: 32px;
          border-top: 2px solid #f1f5f9;

          @media (max-width: 768px) {
            flex-direction: column;
            gap: 16px;
          }

          .actions-left,
          .actions-right {
            @media (max-width: 768px) {
              width: 100%;
            }
          }

          .btn-back {
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            border: 2px solid #e2e8f0;
            background: white;
            color: #64748b;
            transition: all 0.3s ease;

            &:hover {
              border-color: #cbd5e1;
              background: #f8fafc;
              transform: translateY(-2px);
            }

            @media (max-width: 768px) {
              width: 100%;
            }
          }

          .btn-next,
          .btn-submit {
            padding: 12px 32px;
            border-radius: 12px;
            font-weight: 600;
            border: none;
            transition: all 0.3s ease;

            @media (max-width: 768px) {
              width: 100%;
            }
          }

          .btn-next {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;

            &:hover {
              background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
              transform: translateY(-2px);
              box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
            }
          }

          .btn-submit {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;

            &:hover {
              background: linear-gradient(135deg, #059669 0%, #047857 100%);
              transform: translateY(-2px);
              box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
            }

            &:disabled {
              opacity: 0.7;
              transform: none;
              box-shadow: none;
            }
          }
        }
      }
    }
  }

  // Success/Error Result Styles
  .success-card {
    border: 2px solid #10b981 !important;

    .result-icon {
      font-size: 4rem;
      margin-bottom: 1.5rem;
    }

    .result-title {
      color: #059669;
      font-weight: 700;
      margin-bottom: 1rem;
    }

    .result-description {
      color: #374151;
      font-size: 1.1rem;
      line-height: 1.6;
      margin-bottom: 2rem;
    }

    .request-summary {
      background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
      border: 2px solid #bbf7d0;
      border-radius: 12px;
      padding: 24px;
      margin: 24px 0;
      text-align: left;

      .summary-title {
        display: flex;
        align-items: center;
        font-weight: 700;
        color: #059669;
        margin-bottom: 16px;
        font-size: 18px;
      }

      .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #d1fae5;

        &:last-child {
          border-bottom: none;
        }

        .label {
          font-weight: 600;
          color: #374151;
        }

        .value {
          font-weight: 500;
          color: #1f2937;

          &.request-id {
            color: #059669;
            font-weight: 700;
          }

          &.blood-type {
            background: #dc2626;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
          }
        }
      }
    }

    .home-button {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      border: none;
      border-radius: 12px;
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
      }
    }
  }

  .error-card {
    border: 2px solid #ef4444 !important;

    .result-icon {
      font-size: 4rem;
      margin-bottom: 1.5rem;
    }

    .result-title {
      color: #dc2626;
      font-weight: 700;
      margin-bottom: 1rem;
    }

    .result-description {
      color: #374151;
      font-size: 1.1rem;
      line-height: 1.6;
      margin-bottom: 2rem;
    }

    .retry-button {
      border: 2px solid #3b82f6;
      border-radius: 12px;
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover {
        background: #3b82f6;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
      }
    }
  }

  // PDF Upload Section Styles
  .pdf-upload-section {
    .pdf-upload-container {
      margin-top: 12px;

      .upload-area {
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        background: #f9fafb;
        transition: all 0.3s ease;

        &:hover {
          border-color: #3b82f6;
          background: #eff6ff;
        }

        .pdf-upload-label {
          display: block;
          padding: 32px 24px;
          cursor: pointer;
          margin: 0;
          text-align: center;

          .upload-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;

            .upload-icon {
              font-size: 48px;
              opacity: 0.7;
            }

            .upload-text {
              strong {
                color: #374151;
                font-size: 16px;
              }

              .upload-subtext {
                color: #6b7280;
                font-size: 14px;
              }
            }
          }
        }

        &.is-invalid {
          border-color: #ef4444;
          background: #fef2f2;
        }
      }

      .upload-status {
        margin-top: 12px;
        padding: 12px 16px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 14px;

        .status-icon {
          font-size: 18px;
          flex-shrink: 0;
        }

        .status-content {
          flex-grow: 1;

          .status-text {
            margin: 0;

            strong {
              color: #374151;
            }
          }

          .file-size {
            color: #6b7280;
            font-size: 12px;
            margin-top: 2px;
          }
        }

        &.uploading {
          background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
          border: 1px solid #f59e0b;
          color: #92400e;
        }

        &.success {
          background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
          border: 1px solid #10b981;
          color: #065f46;
        }

        &.error {
          background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
          border: 1px solid #ef4444;
          color: #991b1b;
        }
      }
    }
  }

  // Loading and Alert States
  .spinner-border {
    color: #3b82f6;
  }

  .alert {
    border-radius: 12px;
    border: 2px solid;
    padding: 24px;

    &.alert-info {
      background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
      border-color: #93c5fd;
      color: #1e40af;
    }

    &.alert-warning {
      background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
      border-color: #fcd34d;
      color: #92400e;
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    .hero-section {
      padding: 40px 0 30px;
    }

    .steps-navigation {
      padding: 20px 16px;

      .steps-container {
        padding: 20px;
      }
    }

    .form-container {
      padding: 0 16px;
    }
  }
}
