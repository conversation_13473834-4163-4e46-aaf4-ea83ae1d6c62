@use "../base/variables" as vars;

.doctor-sidebar {
  width: 280px;
  height: 100vh;
  background: white;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);

  &__header {
    padding: 2rem 1.5rem 1rem;
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;

    h2 {
      margin: 0 0 1rem 0;
      font-size: 1.5rem;
      font-weight: 600;
      text-align: center;
    }

    .doctor-type {
      text-align: center;

      .blood-dept {
        background: rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 500;
        display: inline-block;
      }

      .other-dept {
        background: rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 500;
        display: inline-block;
      }
    }
  }

  &__nav {
    flex: 1;
    padding: 1rem 0;
    overflow-y: auto;
  }

  &__menu {
    list-style: none;
    margin: 0;
    padding: 0;

    &-item {
      margin: 0;
    }
  }

  &__link {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: #666;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;

    &:hover {
      background: #f8f9fa;
      color: #28a745;
      border-left-color: #28a745;
    }

    &.active {
      background: linear-gradient(90deg, rgba(#28a745, 0.1), transparent);
      color: #28a745;
      border-left-color: #28a745;
      font-weight: 600;
    }
  }

  &__footer {
    padding: 1rem;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;

    .user-info {
      margin-bottom: 1rem;
      padding: 0.75rem;
      background: white;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .user-name {
        font-weight: 600;
        color: #333;
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
      }

      .user-department {
        font-size: 0.8rem;
        color: #666;
      }
    }
  }
}

// Mobile responsive
@media (max-width: 768px) {
  .doctor-sidebar {
    width: 100%;
    height: auto;
    position: relative;
    box-shadow: none;
    border-right: none;
    border-bottom: 1px solid #e9ecef;

    &__header {
      padding: 1rem;

      h2 {
        font-size: 1.2rem;
        margin-bottom: 0.5rem;
      }

      .doctor-type {
        .blood-dept,
        .other-dept {
          font-size: 0.8rem;
          padding: 0.25rem 0.75rem;
        }
      }
    }

    &__nav {
      max-height: 300px;
    }

    &__link {
      padding: 0.75rem 1rem;
      font-size: 0.9rem;
    }

    &__footer {
      padding: 0.75rem;

      .user-info {
        margin-bottom: 0.75rem;
        padding: 0.5rem;

        .user-name {
          font-size: 0.8rem;
        }

        .user-department {
          font-size: 0.7rem;
        }
      }
    }
  }
}
