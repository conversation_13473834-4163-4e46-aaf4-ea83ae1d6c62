import { apiClient } from "./axiosInstance";
import config from "../config/environment";

const API_URL = config.api.bloodArticles;

export async function getBloodArticles() {
  const response = await apiClient.get(API_URL);
  return response.data;
}

export async function createArticle(data) {
  // Process tags - support both tag names and tag IDs
  let tagIds = [];
  let newTagNames = [];

  // Handle both the new format (tagIds + newTags) and legacy format (tags)
  if (data.tagIds && Array.isArray(data.tagIds)) {
    tagIds = data.tagIds;
  }

  if (data.newTags && Array.isArray(data.newTags)) {
    newTagNames = data.newTags;
  }

  // Legacy support for data.tags format
  if (data.tags && !data.tagIds && !data.newTags) {
    const tags = Array.isArray(data.tags)
      ? data.tags
      : data.tags.split(",").map((t) => t.trim());

    tags.forEach((tag) => {
      // If it's a number or numeric string, treat as existing tagId
      if (
        typeof tag === "number" ||
        (typeof tag === "string" && !isNaN(tag) && tag.trim() !== "")
      ) {
        tagIds.push(typeof tag === "number" ? tag : parseInt(tag));
      }
      // If it's a non-numeric string, treat as new tag name
      else if (typeof tag === "string" && tag.trim() !== "") {
        newTagNames.push(tag.trim());
      }
    });
  }

  const requestData = {
    title: data.title || "",
    summary: data.summary || data.excerpt || "",
    content: data.content || "",
    imgUrl: data.imgUrl || "", // Support base64 images
    tagIds: tagIds,
    newTags: newTagNames, // Send new tag names if any
    userId: data.userId,
  };

 

  try {
    const response = await apiClient.post(API_URL, requestData);
    
    return response.data;
  } catch (error) {
    
    throw error;
  }
}

export async function updateArticle(articleId, data) {
  console.log("updateArticle called with:", { articleId, data });

  // Process tags - support both tag names and tag IDs
  let tagIds = [];
  let newTagNames = [];

  // Handle new format first (tagIds + newTags)
  if (data.tagIds && Array.isArray(data.tagIds)) {
    tagIds = data.tagIds;
  }

  if (data.newTags && Array.isArray(data.newTags)) {
    newTagNames = data.newTags;
  }

  // Legacy support: if no tagIds/newTags provided, process tags field
  if (!data.tagIds && !data.newTags && data.tags) {
    const tagsToProcess = data.tags;
    

    if (tagsToProcess.length > 0) {
      const tags = Array.isArray(tagsToProcess)
        ? tagsToProcess
        : tagsToProcess.split(",").map((t) => t.trim());

      tags.forEach((tag) => {
        // If it's a number or numeric string, treat as existing tagId
        if (
          typeof tag === "number" ||
          (typeof tag === "string" && !isNaN(tag) && tag.trim() !== "")
        ) {
          tagIds.push(typeof tag === "number" ? tag : parseInt(tag));
        }
        // If it's a non-numeric string, treat as new tag name
        else if (typeof tag === "string" && tag.trim() !== "") {
          newTagNames.push(tag.trim());
        }
      });
    }
  }

  const requestData = {
    title: data.title || "",
    summary: data.summary || data.excerpt || "",
    content: data.content || "",
    imgUrl: data.imgUrl || "", // Now supports base64 strings
    tagIds: tagIds,
    newTags: newTagNames,
    userId: data.userId,
  };

  try {
    const response = await apiClient.put(
      `${API_URL}/${articleId}`,
      requestData
    );
  
    return response.data;
  } catch (error) {
   
    throw error;
  }
}



export async function deleteArticle(articleId) {
  // Get current user ID from localStorage
  const currentUser = localStorage.getItem("currentUser");
  let userId = null;
  if (currentUser) {
    try {
      const userData = JSON.parse(currentUser);
      userId = userData.id || userData.userId || userData.userID;
    } catch (error) {
      
    }
  }

 

  try {
  
    const url = userId
      ? `${API_URL}/${articleId}?userId=${userId}`
      : `${API_URL}/${articleId}`;

    const response = await apiClient.delete(url, {
      timeout: 10000, // 10 second timeout
    });
    return response.data;
  } catch (error) {
   
    throw error;
  }
}

export async function getBloodArticleDetail(articleId) {
  if (!articleId) {
    throw new Error("Article ID is required");
  }

  try {
   
    const response = await apiClient.get(`/BloodArticles/${articleId}`);
  

    if (response.status === 200 && response.data) {
      return response.data;
    } else {
      throw new Error(`API returned status ${response.status}`);
    }
  } catch (error) {
  
    throw error;
  }
}