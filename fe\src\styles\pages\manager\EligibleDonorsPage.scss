@use '../../base/variables' as vars;
@use '../../base/mixin' as mix;

.eligible-donors-page {
  display: flex;
  min-height: 100vh;
  background: vars.$manager-bg-light;
  font-family: vars.$font-manager;

  .donors-content {
    flex: 1;
    margin-left: 280px;
    padding: vars.$spacing-lg;
    transition: margin-left 0.3s ease;

    &.collapsed {
      margin-left: 80px;
    }

    // Page Header
    .page-header {
      background: vars.$manager-bg;
      padding: vars.$spacing-lg;
      border-radius: 12px;
      margin-bottom: vars.$spacing-lg;
      box-shadow: 0 2px 8px vars.$manager-shadow;
      border: 1px solid vars.$manager-border;
      @include mix.flex-align(space-between, center);

      .header-info {
        h1 {
          margin: 0 0 8px 0;
          font-size: 1.8rem;
          font-weight: 600;
          color: vars.$manager-text;
          font-family: vars.$font-manager;
        }

        p {
          margin: 0;
          color: vars.$manager-text-light;
          font-size: 1rem;
          font-family: vars.$font-manager;
        }
      }

      .header-actions {
        .ant-btn {
          font-family: vars.$font-manager;
        }

        .view-mode-toggle {
          @include mix.flex-align(flex-start, center);
          gap: vars.$spacing-xs;

          span {
            font-size: 0.9rem;
            color: vars.$manager-text;
            font-family: vars.$font-manager;
            font-weight: 500;
          }

          .ant-switch {
            background-color: vars.$manager-border;

            &.ant-switch-checked {
              background-color: vars.$manager-primary;
            }
          }
        }
      }
    }

    // Filters Section
    .filters-section {
      background: vars.$manager-bg;
      padding: vars.$spacing-lg;
      border-radius: 12px;
      margin-bottom: vars.$spacing-lg;
      box-shadow: 0 2px 8px vars.$manager-shadow;
      border: 1px solid vars.$manager-border;

      .filter-group {
        @include mix.flex-align(flex-start, center);
        gap: 8px;

        label {
          font-weight: 500;
          color: vars.$manager-text;
          font-size: 0.9rem;
          font-family: vars.$font-manager;
          min-width: 80px;
        }

        .ant-select,
        .ant-input-search {
          font-family: vars.$font-manager;
        }

        // Send Donation Call Button styling
        .ant-btn {
          font-family: vars.$font-manager;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(238, 90, 36, 0.4) !important;
          }

          &:active {
            transform: translateY(0);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
          }
        }
      }
    }

    // Results Summary
    .results-summary {
      background: vars.$manager-bg-light;
      padding: vars.$spacing-md;
      border-radius: 8px;
      margin-bottom: vars.$spacing-md;
      border-left: 3px solid vars.$manager-primary;

      span {
        color: vars.$manager-text;
        font-size: 0.9rem;
        font-family: vars.$font-manager;
      }
    }

    // Table Section
    .table-section {
      background: vars.$manager-bg;
      border-radius: 12px;
      box-shadow: 0 2px 8px vars.$manager-shadow;
      border: 1px solid vars.$manager-border;
      overflow: hidden;

      .ant-table {
        font-family: vars.$font-manager;

        .ant-table-thead>tr>th {
          background: vars.$manager-bg-light;
          color: vars.$manager-text;
          font-weight: 600;
          border-bottom: 1px solid vars.$manager-border;
        }

        .ant-table-tbody>tr>td {
          border-bottom: 1px solid vars.$manager-border;
        }

        .ant-table-tbody>tr:hover>td {
          background: vars.$manager-hover;
        }

        // STT column styling
        .ant-table-tbody>tr>td:first-child {
          background: rgba(vars.$manager-primary, 0.05);
          font-weight: 600;
          color: vars.$manager-primary;
          text-align: center;
        }

        .ant-table-thead>tr>th:first-child {
          background: rgba(vars.$manager-primary, 0.1);
          color: vars.$manager-primary;
          font-weight: 700;
          text-align: center;
        }

        // Last donation date column styling
        .ant-table-tbody>tr>td:nth-child(6) {
          background: rgba(24, 144, 255, 0.05);

          div:first-child {
            color: #1890ff;
            font-weight: 600;
          }

          div:last-child {
            color: #999;
            font-style: italic;
          }
        }

        .ant-table-thead>tr>th:nth-child(6) {
          background: rgba(24, 144, 255, 0.08);
          color: #1890ff;
          font-weight: 600;
        }
      }
    }

    // Cards Section
    .cards-section {
      .donor-card {
        border-radius: 12px;
        box-shadow: 0 2px 8px vars.$manager-shadow;
        border: 1px solid vars.$manager-border;
        transition: all 0.3s ease;
        font-family: vars.$font-manager;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px vars.$manager-shadow;
        }

        .card-header {
          @include mix.flex-align(space-between, center);
          margin-bottom: vars.$spacing-sm;

          .donor-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: vars.$manager-text;
            font-family: vars.$font-manager;
          }

          .blood-type-tag {
            font-weight: bold;
            font-size: 0.9rem;
          }
        }

        .card-content {
          .status-section {
            margin-bottom: vars.$spacing-sm;

            .status-badge {
              .ant-badge-status-text {
                font-family: vars.$font-manager;
                font-weight: 500;
              }
            }
          }

          .info-item {
            @include mix.flex-align(flex-start, center);
            gap: vars.$spacing-xs;
            margin-bottom: vars.$spacing-xs;
            font-family: vars.$font-manager;

            .info-icon {
              color: vars.$manager-primary;
              font-size: 0.9rem;
            }

            .distance-text {
              font-weight: 500;
              color: vars.$manager-text;
            }

            .phone-link {
              color: vars.$manager-primary;
              text-decoration: none;

              &:hover {
                text-decoration: underline;
              }
            }
          }
        }

        .ant-card-actions {
          border-top: 1px solid vars.$manager-border;

          >li {
            margin: 0;

            .anticon {
              color: vars.$manager-text-light;
              font-size: 1.1rem;
              transition: all 0.3s ease;

              &:hover {
                color: vars.$manager-primary;
                transform: scale(1.1);
              }
            }
          }
        }
      }

      .empty-state {
        text-align: center;
        padding: vars.$spacing-xl;
        color: vars.$manager-text-light;

        .empty-icon {
          font-size: 3rem;
          margin-bottom: vars.$spacing-md;
          opacity: 0.6;
        }

        h3 {
          color: vars.$manager-text;
          margin-bottom: vars.$spacing-sm;
          font-family: vars.$font-manager;
        }

        p {
          font-family: vars.$font-manager;
        }
      }
    }

    // Enhanced Modal Styles - Synchronized with BloodRequestDetailModal
    .donor-detail-modal {
      .ant-modal-content {
        background: #fafbfc;
        border-radius: 12px;
        box-shadow: 0 16px 40px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        border: 1px solid #e8eaed;
      }

      .ant-modal-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-bottom: 2px solid #dee2e6;
        padding: 24px 32px 20px;
        margin: 0;

        .ant-modal-title {
          color: #2c3e50 !important;
          font-size: 20px;
          font-weight: 600;
          line-height: 1.3;
        }
      }

      .ant-modal-body {
        padding: 24px 32px;
        background: #fafbfc;
      }

      .ant-modal-footer {
        background: #f8f9fa;
        border-top: 1px solid #dee2e6;
        padding: 16px 32px;
        text-align: right;

        .ant-btn {
          margin-left: 8px;
          border-radius: 6px;
          font-weight: 500;

          &:first-child {
            margin-left: 0;
          }
        }
      }
    }

    // Modal Content Styles
    .donor-details {
      .detail-section {
        margin-bottom: vars.$spacing-lg;

        &:last-child {
          margin-bottom: 0;
        }

        h4 {
          margin: 0 0 vars.$spacing-md 0;
          color: vars.$manager-text;
          font-size: 1.1rem;
          font-weight: 600;
          font-family: vars.$font-manager;
          border-bottom: 1px solid vars.$manager-border;
          padding-bottom: 8px;
        }

        .detail-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: vars.$spacing-md;

          .detail-item {
            @include mix.flex-align(space-between, flex-start);
            padding: 12px 16px;
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #f0f0f0;
            transition: all 0.2s ease;

            &:hover {
              border-color: #d9d9d9;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            }

            &.full-width {
              grid-column: 1 / -1;
              flex-direction: column;
              align-items: flex-start;
              gap: 8px;
            }

            label {
              font-weight: 600;
              color: #6c757d;
              font-size: 0.85rem;
              font-family: vars.$font-manager;
              min-width: 120px;
              text-transform: uppercase;
              letter-spacing: 0.5px;
            }

            span,
            a {
              color: #2c3e50;
              font-family: vars.$font-manager;
              font-size: 0.95rem;
              font-weight: 500;

              &[href] {
                color: #1890ff;
                text-decoration: none;
                transition: color 0.2s ease;

                &:hover {
                  color: #40a9ff;
                  text-decoration: underline;
                }
              }
            }

            .ant-tag {
              margin: 0;
              border-radius: 6px;
              font-weight: 600;
            }

            .anticon {
              margin-right: 6px;
            }
          }
        }

        p {
          margin: 0;
          color: vars.$manager-text;
          font-family: vars.$font-manager;
          line-height: 1.5;
          background: vars.$manager-bg-light;
          padding: vars.$spacing-sm;
          border-radius: 6px;
          border-left: 3px solid vars.$manager-primary;
        }
      }
    }

    // Process Workflow Modal
    .process-workflow {
      .donor-summary {
        background: vars.$manager-bg-light;
        padding: vars.$spacing-md;
        border-radius: 8px;
        margin-bottom: vars.$spacing-md;

        .summary-item {
          font-family: vars.$font-manager;

          strong {
            color: vars.$manager-text;
            font-weight: 600;
          }
        }
      }

      .workflow-steps {
        .ant-steps {
          .ant-steps-item {
            .ant-steps-item-title {
              font-family: vars.$font-manager;
              font-weight: 600;
              color: vars.$manager-text;
            }

            .ant-steps-item-description {
              font-family: vars.$font-manager;
              color: vars.$manager-text-light;
            }

            &.ant-steps-item-process {
              .ant-steps-item-icon {
                background: vars.$manager-primary;
                border-color: vars.$manager-primary;
              }
            }

            &.ant-steps-item-finish {
              .ant-steps-item-icon {
                background: vars.$success-color;
                border-color: vars.$success-color;
              }
            }

            &.ant-steps-item-error {
              .ant-steps-item-icon {
                background: vars.$error-color;
                border-color: vars.$error-color;
              }
            }
          }
        }
      }

      .action-section {
        background: rgba(vars.$manager-primary, 0.05);
        padding: vars.$spacing-lg;
        border-radius: 8px;
        text-align: center;

        h4 {
          color: vars.$manager-text;
          font-family: vars.$font-manager;
          margin-bottom: vars.$spacing-sm;
        }

        p {
          color: vars.$manager-text-light;
          font-family: vars.$font-manager;
          margin-bottom: vars.$spacing-md;
        }

        .ant-btn {
          font-family: vars.$font-manager;
          font-weight: 600;
        }
      }

      .notes-section {
        h4 {
          color: vars.$manager-text;
          font-family: vars.$font-manager;
          margin-bottom: vars.$spacing-sm;
        }

        p {
          color: vars.$manager-text;
          font-family: vars.$font-manager;
          background: vars.$manager-bg-light;
          padding: vars.$spacing-sm;
          border-radius: 6px;
          border-left: 3px solid vars.$manager-primary;
          margin: 0;
        }
      }
    }
  }
}

// Enhanced Responsive Design - Synchronized with BloodRequestsPage
@media (max-width: 1200px) {
  .eligible-donors-page {
    .donors-content {
      .table-section {
        .donations-table-card {
          .ant-table {
            font-size: 14px;
          }
        }
      }
    }
  }
}

// Responsive Design
@include mix.tablet {
  .eligible-donors-page {
    .donors-content {
      margin-left: 240px;
      padding: vars.$spacing-md;

      &.collapsed {
        margin-left: 80px;
      }

      .page-header {
        padding: vars.$spacing-md;

        .header-info h1 {
          font-size: 1.5rem;
        }
      }

      .filters-section {
        padding: vars.$spacing-md;

        .ant-space {
          width: 100%;
          justify-content: space-between;
        }
      }
    }
  }
}

@include mix.mobile {
  .eligible-donors-page {
    .donors-content {
      margin-left: 0;
      padding: vars.$spacing-sm;

      .page-header {
        padding: vars.$spacing-sm;
        flex-direction: column;
        align-items: stretch;
        gap: vars.$spacing-sm;

        .header-info h1 {
          font-size: 1.3rem;
        }
      }

      .filters-section {
        padding: vars.$spacing-sm;

        .ant-space {
          flex-direction: column;
          align-items: stretch;

          .filter-group {
            flex-direction: column;
            align-items: stretch;

            label {
              min-width: auto;
              margin-bottom: 4px;
            }

            .ant-select,
            .ant-input-search {
              width: 100% !important;
            }

            // Mobile button styling
            .ant-btn {
              width: 100% !important;
              margin-top: vars.$spacing-sm !important;
              font-size: 0.9rem;
              padding: 8px 16px;
              height: auto;
            }
          }
        }
      }

      .table-section {
        .donations-table-card {
          overflow-x: auto;
          margin: 0 -16px;
          border-radius: 0;

          .ant-table {
            min-width: 1000px;
            font-size: 13px;

            .ant-table-thead>tr>th {
              padding: 12px 8px;
              font-size: 13px;
              white-space: nowrap;
              background: #f8f9fa;
            }

            .ant-table-tbody>tr>td {
              padding: 12px 8px;
              font-size: 13px;
              white-space: nowrap;
            }
          }
        }
      }

      .cards-section {
        .donor-card {
          .card-header {
            flex-direction: column;
            align-items: flex-start;
            gap: vars.$spacing-xs;

            .donor-name {
              font-size: 1rem;
            }
          }

          .card-content {
            .info-item {
              font-size: 0.85rem;
            }
          }
        }
      }

      .donor-details {
        .detail-grid {
          grid-template-columns: 1fr;

          .detail-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;

            label {
              min-width: auto;
            }
          }
        }
      }

      // Mobile modal adjustments
      .donor-detail-modal {
        .ant-modal-content {
          margin: 16px;
          max-width: calc(100vw - 32px);
        }

        .ant-modal-header,
        .ant-modal-body,
        .ant-modal-footer {
          padding-left: 16px;
          padding-right: 16px;
        }

        .ant-modal-body {
          padding-top: 16px;
          padding-bottom: 16px;
        }

        // Stack cards vertically on mobile
        .ant-row {
          .ant-col {
            margin-bottom: 16px;
          }
        }
      }
    }
  }
}