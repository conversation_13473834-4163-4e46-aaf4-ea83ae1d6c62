import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import authService from "../../services/authService";
import { API_ROLES } from "../../constants/systemConstants";

// Component to protect routes based on authentication and roles
const ProtectedRoute = ({
  children,
  requireAuth = true,
  allowedRoles = [API_ROLES.MEMBER],
  redirectTo = "/login",
}) => {
  const location = useLocation();
  const { isAuthenticated, loading } = useAuth();
  const userRole = authService.getUserRole();

  // Show loading while auth is being initialized
  if (loading) {
    return <div>Loading...</div>;
  }

  // Check if authentication is required
  if (requireAuth && !isAuthenticated) {
    console.log("ProtectedRoute: User not authenticated, redirecting to:", redirectTo);
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // Check if user has required role
  if (allowedRoles.length > 0 && !allowedRoles.includes(userRole)) {
    console.log("ProtectedRoute: User role not allowed. User role:", userRole, "Allowed roles:", allowedRoles);
    return <Navigate to="/403" replace />;
  }

  return children;
};

export default ProtectedRoute;

// Specific route protection components for different roles
export const GuestRoute = ({ children }) => (
  <ProtectedRoute requireAuth={false}>{children}</ProtectedRoute>
);

export const MemberRoute = ({ children }) => (
  <ProtectedRoute allowedRoles={[API_ROLES.MEMBER]}>{children}</ProtectedRoute>
);

export const DoctorRoute = ({ children }) => (
  <ProtectedRoute allowedRoles={[API_ROLES.STAFF_DOCTOR]}>
    {children}
  </ProtectedRoute>
);

export const ManagerRoute = ({ children }) => {
  // Allow both blood managers and blood department doctors
  const currentUser = authService.getCurrentUser();
  const allowedRoles = [API_ROLES.STAFF_BLOOD_MANAGER];

  // Add blood department doctors to allowed roles
  if (
    currentUser?.role === API_ROLES.STAFF_DOCTOR &&
    currentUser?.doctorType === "blood_department"
  ) {
    allowedRoles.push(API_ROLES.STAFF_DOCTOR);
  }

  return (
    <ProtectedRoute allowedRoles={allowedRoles}>{children}</ProtectedRoute>
  );
};

export const AdminRoute = ({ children }) => (
  <ProtectedRoute allowedRoles={[API_ROLES.ADMIN]}>{children}</ProtectedRoute>
);

export const StaffRoute = ({ children }) => (
  <ProtectedRoute
    allowedRoles={[API_ROLES.STAFF_BLOOD_MANAGER, API_ROLES.STAFF_DOCTOR]}
  >
    {children}
  </ProtectedRoute>
);
