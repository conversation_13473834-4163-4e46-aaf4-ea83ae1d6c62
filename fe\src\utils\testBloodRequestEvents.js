/**
 * Test utility for blood request events
 * Use this in browser console to test the event system
 */

import bloodRequestEvents, { BLOOD_REQUEST_EVENTS, emitBloodRequestStatusUpdate } from './bloodRequestEvents';

// Test function to verify event system is working
export const testBloodRequestEvents = () => {
  console.log('🧪 Testing Blood Request Event System...');

  // Subscribe to events
  const unsubscribe = bloodRequestEvents.subscribe(
    BLOOD_REQUEST_EVENTS.STATUS_UPDATED,
    (data) => {
      console.log('✅ Event received:', data);
    }
  );

  // Emit a test event
  emitBloodRequestStatusUpdate(123, 0, 1, { test: true });

  // Clean up
  setTimeout(() => {
    unsubscribe();
    console.log('🧪 Test completed and cleaned up');
  }, 1000);
};

// Export for console testing
if (typeof window !== 'undefined') {
  window.testBloodRequestEvents = testBloodRequestEvents;
  console.log('🧪 Test function available as window.testBloodRequestEvents()');
}
