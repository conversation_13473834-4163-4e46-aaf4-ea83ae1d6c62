import axiosInstance from "./axiosInstance";

const DASHBOARD_API = import.meta.env.VITE_DASHBOARD_API || "/api/Dashboard";
const BLOOD_REQUEST_API =
  import.meta.env.VITE_BLOOD_REQUEST_API || "/api/BloodRequest";
const BLOOD_INVENTORY_API =
  import.meta.env.VITE_BLOOD_INVENTORY_API || "/api/BloodInventory";


const doctorDashboardService = {
 
  getDashboardStats: async (doctorId) => {
    try {
      const response = await axiosInstance.get(
        `${DASHBOARD_API}/doctor/${doctorId}`
      );
      return {
        success: true,
        data: response.data,
        message: "Lấy thống kê dashboard thành công",
      };
    } catch (error) {
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi lấy thống kê dashboard",
        details: error.response?.data,
      };
    }
  },

  
  getBloodRequestStats: async (doctorId) => {
    try {
      const response = await axiosInstance.get(
        `${BLOOD_REQUEST_API}/stats/doctor/${doctorId}`
      );
      return {
        success: true,
        data: response.data,
        message: "Lấy thống kê yêu cầu máu thành công",
      };
    } catch (error) {
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi lấy thống kê yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  
  getBloodInventoryStats: async () => {
    try {
      const response = await axiosInstance.get(`${BLOOD_INVENTORY_API}/stats`);
      return {
        success: true,
        data: response.data,
        message: "Lấy thống kê kho máu thành công",
      };
    } catch (error) {
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi lấy thống kê kho máu",
        details: error.response?.data,
      };
    }
  },

  
  getRecentBloodRequests: async (doctorId, limit = 5) => {
    try {
      const response = await axiosInstance.get(
        `${BLOOD_REQUEST_API}/recent/doctor/${doctorId}?limit=${limit}`
      );
      return {
        success: true,
        data: response.data,
        message: "Lấy yêu cầu máu gần đây thành công",
      };
    } catch (error) {
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi lấy yêu cầu máu gần đây",
        details: error.response?.data,
      };
    }
  },

  
  getNotifications: async (doctorId) => {
    try {
      const response = await axiosInstance.get(
        `${DASHBOARD_API}/notifications/doctor/${doctorId}`
      );
      return {
        success: true,
        data: response.data,
        message: "Lấy thông báo thành công",
      };
    } catch (error) {
      return {
        success: false,
        error:
          error.response?.data?.message || "Có lỗi xảy ra khi lấy thông báo",
        details: error.response?.data,
      };
    }
  },

  
  markNotificationAsRead: async (notificationId) => {
    try {
      const response = await axiosInstance.patch(
        `${DASHBOARD_API}/notifications/${notificationId}/read`
      );
      return {
        success: true,
        data: response.data,
        message: "Đánh dấu thông báo đã đọc thành công",
      };
    } catch (error) {
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi đánh dấu thông báo",
        details: error.response?.data,
      };
    }
  },
};

export default doctorDashboardService;
