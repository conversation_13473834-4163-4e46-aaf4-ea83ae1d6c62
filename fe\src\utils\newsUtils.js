// Utility functions for News data handling
// Handle inconsistent field names in News API responses

// Utility để lấy ID chính xác của tin tức
export const getNewsId = (newsItem) => {
  if (!newsItem) return null;

  // Thử các trường ID khác nhau theo thứ tự ưu tiên
  return (
    newsItem.contentID ||
    newsItem.postId ||
    newsItem.id ||
    newsItem.newsId ||
    newsItem.newsID
  );
};

// Utility để lấy userId chính xác của tin tức
export const getNewsUserId = (newsItem) => {
  if (!newsItem) return null;

  // Thử các trường userId khác nhau theo thứ tự ưu tiên
  return (
    newsItem.userId ||
    newsItem.userID ||
    newsItem.authorId ||
    newsItem.createdBy
  );
};

// Utility để tìm tin tức theo ID
export const findNewsById = (newsArray, targetId) => {
  if (!Array.isArray(newsArray) || !targetId) return null;

  return newsArray.find((news) => {
    const newsId = getNewsId(news);
    return newsId === targetId || String(newsId) === String(targetId);
  });
};

// Utility để lấy ngày tạo thực tế của tin tức
export const getNewsCreatedDate = (newsItem) => {
  if (!newsItem) return null;

  // Thử các trường date khác nhau
  const dateFields = [
    "createdAt",
    "publishedAt",
    "postedAt",
    "updatedAt",
    "dateCreated",
    "created_at",
  ];

  for (const field of dateFields) {
    const date = newsItem[field];
    if (
      date &&
      date !== "1900-01-01T00:00:00" &&
      new Date(date).getFullYear() !== 1900
    ) {
      return date;
    }
  }

  // Nếu không có ngày hợp lệ, trả về null
  return null;
};
