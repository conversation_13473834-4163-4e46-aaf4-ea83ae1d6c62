import React from "react";
import { useNavigate } from "react-router-dom";
import {
  FaCheckCircle,
  FaExclamationTriangle,
  FaHome,
  FaRedo,
  FaFileAlt,
  FaInfoCircle,
  FaCalendarAlt,
} from "react-icons/fa";

/**
 * Component hiển thị kết quả đăng ký hiến máu
 * Đồng bộ UI với BloodRequestResultDisplay
 */
const ResultDisplay = ({
  registrationResult,
  appointmentData,
  getTimeSlotText,
  onRetry,
}) => {
  const navigate = useNavigate();

  if (!registrationResult) return null;

  return (
    <div className="blood-donation-form-page">
      <div className="registration-content result-page">
        <div className="result-section">
          <div className={`result-card ${registrationResult.status}`}>
            <div className="result-icon">
              {registrationResult.status === "failed" ||
                registrationResult.status === "error" ? (
                <FaExclamationTriangle />
              ) : (
                <FaCheckCircle />
              )}
            </div>
            <div className="result-content">
              <h2>{registrationResult.message}</h2>
              <p>{registrationResult.description}</p>

              {registrationResult.status === "scheduled" && (
                <div className="appointment-summary">
                  <div className="summary-title">
                    <FaCalendarAlt className="me-2" />
                    Thông tin lịch hẹn
                  </div>
                  <div className="appointment-details">
                    <div className="detail-item">
                      <strong>Ngày:</strong>
                      <span className="value">
                        {new Date(
                          appointmentData.preferredDate
                        ).toLocaleDateString("vi-VN")}
                      </span>
                    </div>
                    <div className="detail-item">
                      <strong>Khung giờ:</strong>
                      <span className="value">
                        {getTimeSlotText(appointmentData.timeSlot)}
                      </span>
                    </div>
                    <div className="detail-item">
                      <strong>Địa điểm:</strong>
                      <span className="value">
                        Bệnh viện Đa khoa Ánh Dương - Khoa Huyết học, Tầng 2
                      </span>
                    </div>
                    <div className="detail-item">
                      <strong>Địa chỉ:</strong>
                      <span className="value">
                        Đường Cách Mạng Tháng 8, Quận 3, TP.HCM, Vietnam
                      </span>
                    </div>
                  </div>
                </div>
              )}

              <div className="result-actions">
                <button
                  className="btn btn-primary"
                  onClick={() => navigate("/member")}
                >
                  <FaHome className="me-2" />
                  Về trang chủ
                </button>

                {(registrationResult.status === "failed" ||
                  registrationResult.status === "error") && (
                    <button
                      className="btn btn-outline-primary retry-button"
                      onClick={onRetry}
                    >
                      <FaRedo className="me-2" />
                      Thử lại
                    </button>
                  )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResultDisplay;
