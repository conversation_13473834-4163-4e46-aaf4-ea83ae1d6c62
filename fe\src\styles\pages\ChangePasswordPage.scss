@use "../base/variables" as vars;
@use "../base/mixin" as mix;

.change-password-page__container {
    min-height: 100vh;
    background: #f8f9fa;
    padding-top: 80px; // Account for navbar height
}

.change-password-page__content {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 40px 20px;
    min-height: calc(100vh - 80px);
}

.change-password-page {
  padding: vars.$spacing-lg;
  background: vars.$background-light;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 60px;

  .change-password-card {
    width: 100%;
    max-width: 500px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid vars.$border-color;
    padding: vars.$spacing-lg;

    .page-title {
      text-align: center;
      color: vars.$primary-color;
      font-weight: 600;
      margin-bottom: vars.$spacing-xl;
      font-size: 24px;
    }

    .change-password-form {
      .ant-form-item-label {
        > label {
          color: vars.$text-color;
          font-weight: 500;
          font-size: 14px;
        }
      }

      .ant-input-password {
        border-radius: 8px;
        border: 1px solid vars.$border-color;

        &:hover {
          border-color: vars.$primary-color;
        }

        &:focus,
        &.ant-input-focused {
          border-color: vars.$primary-color;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .ant-input {
          border: none;
          box-shadow: none;

          &:focus {
            box-shadow: none;
          }
        }

        .ant-input-prefix {
          color: vars.$text-muted;
          margin-right: 8px;
        }

        .ant-input-suffix {
          .anticon {
            color: vars.$text-muted;

            &:hover {
              color: vars.$primary-color;
            }
          }
        }
      }

      .submit-button {
        height: 48px;
        font-size: 16px;
        font-weight: 500;
        border-radius: 8px;
        background: vars.$primary-color;
        border-color: vars.$primary-color;
        margin-top: vars.$spacing-md;

        &:hover {
          background: #40a9ff;
          border-color: #40a9ff;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
}

// Responsive
@media (max-width: 768px) {
    .change-password-page__content {
        padding: 20px 16px;
    }

    .change-password-page {
      padding: vars.$spacing-md;
      padding-top: 40px;

      .change-password-card {
        padding: vars.$spacing-md;

        .page-title {
          font-size: 20px;
          margin-bottom: vars.$spacing-lg;
        }
      }
    }
}

@media (max-width: 576px) {
  .change-password-page {
    padding: vars.$spacing-sm;
    padding-top: 20px;

    .change-password-card {
      padding: vars.$spacing-sm;

      .page-title {
        font-size: 18px;
        margin-bottom: vars.$spacing-md;
      }

      .change-password-form {
        .ant-form-item {
          margin-bottom: vars.$spacing-md;
        }

        .submit-button {
          height: 44px;
          font-size: 15px;
        }
      }
    }
  }
}
