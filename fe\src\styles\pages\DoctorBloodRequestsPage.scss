@use "../base/variables" as vars;

.doctor-blood-requests {
  display: flex;
  min-height: 100vh;
  background: #f6f6f6;

  .doctor-blood-requests-content {
    flex: 1;
    margin-left: 0px;
    padding: 0 4px;
    font-family: "Inter", sans-serif;

    .page-header {
      margin-bottom: 24px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      gap: 16px;
      background: #fff;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      border: 1px solid #e8e8e8;

      h1 {
        color: #20374e;
        margin-bottom: 8px;
        font-size: 2rem;
        font-weight: 700;
      }

      p {
        color: #666;
        font-size: 1.1rem;
        margin: 0 0 8px 0;
      }

      .auto-approve-notice {
        background: #deccaa;
        color: #20374e;
        padding: 6px 16px;
        border-radius: 16px;
        font-size: 0.95rem;
        font-weight: 600;
        border: none;
      }
    }

    .doctor-info-card {
      background: #fff;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      margin-bottom: 24px;
      border: 1px solid #e8e8e8;

      .doctor-details {
        h3 {
          margin: 0 0 16px 0;
          color: #d93e4c;
          font-size: 1.1rem;
        }

        .info-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .label {
            font-weight: 600;
            color: #666;
            min-width: 100px;
          }

          .value {
            color: #20374e;
            font-weight: 500;

            &.blood-dept {
              color: #52c41a;
              font-weight: 600;
            }

            &.other-dept {
              color: #6c757d;
            }
          }
        }
      }
    }

    .tabs-section {
      margin-bottom: 24px;

      .tabs-header {
        display: flex;
        gap: 8px;
        background: #fff;
        padding: 8px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

        .tab-btn {
          flex: 1;
          padding: 12px 18px;
          border: none;
          border-radius: 8px;
          background: transparent;
          color: #666;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 1rem;

          &:hover {
            background: #f8f9fa;
            color: #d93e4c;
          }

          &.active {
            background: #d93e4c;
            color: #fff;
            box-shadow: 0 2px 8px rgba(217, 62, 76, 0.08);
          }
        }
      }
    }

    .requests-section {
      margin-bottom: 24px;

      h2 {
        color: #d93e4c;
        margin-bottom: 16px;
        font-size: 1.2rem;
        border-bottom: 2px solid #f0f0f0;
        padding-bottom: 8px;
      }

      .requests-table-container {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        border: 1px solid #e8e8e8;

        .requests-table {
          width: 100%;
          border-collapse: collapse;

          th,
          td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
            font-family: "Inter", sans-serif;
          }

          th {
            background: #f8f9fa;
            font-weight: 600;
            color: #20374e;
            font-size: 0.95rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          td {
            font-size: 0.95rem;
            color: #20374e;
          }

          tr:hover {
            background: #f8f9fa;
          }

          .blood-type-badge {
            display: inline-block;
            padding: 6px 25px;
            border-radius: 20px;
            font-weight: 700;
            font-size: 20px;
            text-align: center;
            min-height: 40px;
            min-width: 50px;

            &.positive {
              background-color: #e3f2fd;
              color: #1976d2;
              border: 1px solid #bbdefb;
            }

            &.negative {
              background-color: #ffebee;
              color: #c62828;
              border: 1px solid #ffcdd2;
            }
          }
        }
      }
    }

    .statistics-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;

      .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        text-align: center;

        h3 {
          margin: 0 0 1rem 0;
          color: #666;
          font-size: 0.9rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .stat-number {
          font-size: 2rem;
          font-weight: 700;
          margin: 0;
          color: #28a745;

          &.warning {
            color: #ffc107;
          }

          &.success {
            color: #28a745;
          }

          &.danger {
            color: #dc3545;
          }
        }
      }
    }
  }
}

// Modern Modal styles
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(4px);

  .modal-content {
    background: white;
    border-radius: 20px;
    max-width: 800px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border: 1px solid #e5e7eb;

    &.modern-modal {
      animation: modalSlideIn 0.3s ease-out;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32px;
      border-bottom: 2px solid #f1f5f9;
      background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
      color: white;
      border-radius: 20px 20px 0 0;
      position: relative;
      overflow: hidden;

      &.modern-header {
        &::before {
          content: "";
          position: absolute;
          top: -50%;
          right: -20%;
          width: 200px;
          height: 200px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 50%;
          filter: blur(60px);
        }

        .header-content {
          display: flex;
          align-items: center;
          gap: 20px;
          position: relative;
          z-index: 2;
          flex: 1;

          .header-icon {
            width: 56px;
            height: 56px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            flex-shrink: 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
          }

          .header-text {
            flex: 1;

            .header-title {
              font-size: 24px;
              font-weight: 700;
              color: white;
              margin: 0 0 4px;
              text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            }

            .header-description {
              font-size: 16px;
              color: rgba(255, 255, 255, 0.9);
              margin: 0;
              font-weight: 400;
            }
          }
        }
      }

      h2 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
      }

      .close-btn {
        background: none;
        border: none;
        color: white;
        font-size: 1.2rem;
        cursor: pointer;
        padding: 8px;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 12px;
        transition: all 0.3s ease;
        position: relative;
        z-index: 3;

        &.modern-close {
          background: rgba(255, 255, 255, 0.2);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.3);

          &:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
          }
        }

        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }
      }
    }

    .modal-body {
      padding: 40px;

      &.modern-body {
        @media (max-width: 768px) {
          padding: 24px;
        }
      }

      .form-section {
        margin-bottom: 32px;

        &.modern-section {
          .section-title {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
            padding: 20px 24px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 12px;
            border: 2px solid #e2e8f0;

            .section-icon {
              color: #3b82f6;
              font-size: 20px;
            }

            h3 {
              color: #1e293b;
              font-weight: 700;
              margin: 0;
              font-size: 18px;
            }
          }
        }
      }

      .form-grid {
        .form-group-row {
          display: grid;
          gap: 20px;
          margin-bottom: 24px;
          grid-template-columns: 1fr 1fr;

          @media (max-width: 768px) {
            grid-template-columns: 1fr;
          }

          &.quantity-row {
            grid-template-columns: 2fr 1fr;

            @media (max-width: 768px) {
              grid-template-columns: 1fr;
            }
          }

          .form-group {
            &.flex-1 {
              grid-column: span 1;
            }

            &.flex-2 {
              grid-column: span 2;

              @media (max-width: 768px) {
                grid-column: span 1;
              }
            }
          }
        }

        .form-group {
          margin-bottom: 24px;

          .form-label {
            display: block;
            font-weight: 700;
            color: #1f2937;
            font-size: 15px;
            margin-bottom: 8px;
            letter-spacing: 0.3px;

            .required {
              color: #ef4444;
              font-weight: 600;
            }
          }

          .form-input,
          .form-select,
          .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 15px;
            transition: all 0.3s ease;
            background: white;

            &:focus {
              outline: none;
              border-color: #3b82f6;
              box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
            }

            &.is-invalid {
              border-color: #ef4444;
              box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
            }

            &::placeholder {
              color: #9ca3af;
            }

            &.loading {
              background: #f8fafc;
            }
          }

          .form-textarea {
            min-height: 120px;
            resize: vertical;
            line-height: 1.6;
          }

          .unit-input {
            background: #f8fafc !important;
            color: #64748b;
            font-weight: 600;
            text-align: center;
          }

          .input-status {
            margin-top: 6px;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;

            &.loading {
              color: #3b82f6;
            }

            &.error {
              color: #ef4444;
            }

            .spin {
              animation: spin 1s linear infinite;
            }
          }
        }
      }

      .auto-approve-info {
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        color: #1e40af;
        padding: 20px 24px;
        border-radius: 12px;
        margin-bottom: 24px;
        font-size: 15px;
        border: 2px solid #93c5fd;
        display: flex;
        align-items: center;
        gap: 12px;

        &.modern-info {
          .info-icon {
            font-size: 20px;
            flex-shrink: 0;
          }

          .info-text {
            flex: 1;
            font-weight: 500;
          }
        }
      }

      .form-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 40px;
        padding-top: 32px;
        border-top: 2px solid #f1f5f9;

        &.modern-actions {
          @media (max-width: 768px) {
            flex-direction: column;
            gap: 16px;
          }

          .actions-left,
          .actions-right {
            @media (max-width: 768px) {
              width: 100%;
            }
          }

          .btn-back {
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            border: 2px solid #e2e8f0;
            background: white;
            color: #64748b;
            transition: all 0.3s ease;
            cursor: pointer;

            &:hover {
              border-color: #cbd5e1;
              background: #f8fafc;
              transform: translateY(-2px);
            }

            @media (max-width: 768px) {
              width: 100%;
            }
          }

          .btn-submit {
            padding: 12px 32px;
            border-radius: 12px;
            font-weight: 600;
            border: none;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;

            &:hover {
              background: linear-gradient(135deg, #059669 0%, #047857 100%);
              transform: translateY(-2px);
              box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
            }

            &:disabled {
              opacity: 0.7;
              transform: none;
              box-shadow: none;
              cursor: not-allowed;
            }

            @media (max-width: 768px) {
              width: 100%;
              justify-content: center;
            }

            .spin {
              animation: spin 1s linear infinite;
            }
          }
        }
      }
    }
  }
}

// Keyframes for animations
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

// Responsive
@media (max-width: 768px) {
  .doctor-blood-requests {
    .doctor-blood-requests-content {
      margin-left: 0;
      padding: 1rem;

      .page-header {
        flex-direction: column;
        align-items: stretch;
      }

      .doctor-info-card .doctor-details .info-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
      }

      .requests-section .requests-table-container {
        overflow-x: auto;

        .requests-table {
          min-width: 800px;
        }
      }

      .statistics-section {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;

        .stat-card {
          padding: 1rem;

          .stat-number {
            font-size: 1.5rem;
          }
        }
      }
    }
  }

  .modal-overlay .modal-content {
    .modal-header.modern-header {
      padding: 24px;

      .header-content {
        gap: 16px;

        .header-icon {
          width: 48px;
          height: 48px;
          font-size: 20px;
        }

        .header-text {
          .header-title {
            font-size: 20px;
          }

          .header-description {
            font-size: 14px;
          }
        }
      }
    }

    .modal-body.modern-body {
      .form-grid .form-group-row {
        grid-template-columns: 1fr;

        &.quantity-row {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}

// Action buttons styling
.doctor-blood-requests .doctor-blood-requests-content .requests-section .requests-table-container .requests-table {
  .action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;

    .btn {
      &.btn-success {
        background: #28a745;
        color: white;

        &:hover {
          background: #218838;
          transform: translateY(-1px);
        }
      }

      &.btn-danger {
        background: #dc3545;
        color: white;

        &:hover {
          background: #c82333;
          transform: translateY(-1px);
        }
      }
    }
  }
}