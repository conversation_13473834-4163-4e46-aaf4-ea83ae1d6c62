// Environment configuration
const config = {
  // API configuration - all URLs come from .env.local
  api: {
    baseUrl: import.meta.env.VITE_API_URL,
    news: import.meta.env.VITE_NEWS_API,
    bloodArticles:
      import.meta.env.VITE_BLOOD_ARTICLES_API,
    auth: import.meta.env.VITE_AUTH_API,
    information:
      import.meta.env.VITE_INFORMATION_API,
    nominatim:
      import.meta.env.VITE_NOMINATIM_API,
    activityLog:
      import.meta.env.VITE_ACTIVITY_LOG_API,
    googleLogin:
      import.meta.env.VITE_GOOGLE_LOGIN_API,
    googleCallback:
      import.meta.env.VITE_GOOGLE_CALLBACK_API,
    bloodInventory:
      import.meta.env.VITE_BLOOD_INVENTORY_API,
    bloodDonation:
      import.meta.env.VITE_BLOOD_DONATION_API ,
    bloodRequest:
      import.meta.env.VITE_BLOOD_REQUEST_API ,
    patient:
      import.meta.env.VITE_PATIENT_API,
    notification:
      import.meta.env.VITE_NOTIFICATION_API,
    bloodStatistics:
      import.meta.env.VITE_BLOOD_STATISTICS_API,
  },

  // Hospital information (static data)
  hospital: {
    name: "Bệnh viện Đa khoa Ánh Dương",
    address: "Đường Cách Mạng Tháng 8, Quận 3, TP.HCM, Vietnam",
    coordinates: {
      lat: 10.7751237,
      lng: 106.6862143,
    },
  },
};

export default config;
