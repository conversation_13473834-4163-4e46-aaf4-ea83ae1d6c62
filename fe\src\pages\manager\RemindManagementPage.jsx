import React from 'react';
import ManagerLayout from '../../components/manager/ManagerLayout';
import PageHeader from '../../components/manager/PageHeader';
import RemindManagement from '../../components/manager/RemindManagement';
import { BellOutlined } from '@ant-design/icons';
import '../../styles/pages/RemindManagementPage.scss';

/**
 * Trang quản lý nhắc nhở cho Manager
 */
const RemindManagementPage = () => {
  return (
    <ManagerLayout pageTitle="Quản lý nhắc nhở">
      <div className="main-content">
        <PageHeader
          title="Quản lý nhắc nhở"
          icon={BellOutlined}
          description="Quản lý và theo dõi các nhắc nhở tự động và thủ công"
        />
        
        <div className="remind-management-content">
          <RemindManagement />
        </div>
      </div>
    </ManagerLayout>
  );
};

export default RemindManagementPage;
