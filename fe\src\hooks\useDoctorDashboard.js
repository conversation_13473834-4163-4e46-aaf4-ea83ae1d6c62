import { useState, useEffect, useCallback } from "react";
import doctorDashboardService from "../services/doctorDashboardService";
import userInfoService from "../services/userInfoService";
import { fetchBloodInventory } from "../services/bloodInventoryService";
import { getInventoryStatus } from "../constants/bloodInventoryConstants";
import bloodRequestService from "../services/bloodRequestService";
import authService from "../services/authService";

/**
 * Custom hook for Doctor Dashboard data management
 */
const useDoctorDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dashboardData, setDashboardData] = useState({
    statistics: {
      totalRequests: 0,
      pendingRequests: 0,
      approvedRequests: 0,
      completedRequests: 0,
      urgentNotifications: 0,
    },
    bloodInventory: [],
    recentRequests: [],
    notifications: [],
  });

  const currentUser = authService.getCurrentUser();
  const isBloodDepartment = currentUser?.departmentID === 1; // K<PERSON>a <PERSON>t học có departmentID = 1

  /**
   * Load dashboard statistics
   */
  const loadDashboardStats = useCallback(async () => {
    try {
      // Get blood requests from API
      const requestsResponse = await bloodRequestService.getBloodRequests();

      if (!requestsResponse.success) {
        throw new Error(
          requestsResponse.error || "Không thể tải dữ liệu yêu cầu máu"
        );
      }

      let requests = requestsResponse.data || [];

      // Filter requests for non-blood department doctors (only their own requests)
      if (!isBloodDepartment) {
        const doctorId = currentUser?.id;
        if (doctorId) {
          const filteredRequests = requests.filter((req) => {
            const reqUserId = parseInt(req.userId);
            const reqUserID = parseInt(req.userID);
            const docId = parseInt(doctorId);
            return reqUserId === docId || reqUserID === docId;
          });
          requests = filteredRequests;
        } else {
          requests = [];
        }
      }
      // Process requests according to API format
      const processedRequests = requests.map((request) => ({
        ...request,
        requestID: request.requestId || request.requestID,
        userID: request.userID || request.userId,
        patientId: request.patientId,
        patientName: request.patientName,
        bloodGroup: request.bloodGroup,
        rhType: request.rhType,
        componentId: request.componentId,
        quantity: request.quantity,
        status: request.status,
        createdTime: request.createdTime,
        bloodTypeDisplay:
          request.bloodGroup && request.rhType
            ? `${request.bloodGroup}${request.rhType}`
            : "",
      }));

      // Lấy tổng số donors giống manager
      let totalDonors = 0;
      try {
        const users = await userInfoService.getAllUsers();
        totalDonors = users.filter(u => u.bloodGroup && u.rhType).length;
      } catch (err) {
        console.warn("Không thể lấy danh sách donors:", err);
      }

      const statistics = {
        totalRequests: processedRequests.length,
        pendingRequests: processedRequests.filter(
          (r) => parseInt(r.status) === 0
        ).length,
        approvedRequests: processedRequests.filter(
          (r) => parseInt(r.status) === 1
        ).length,
        completedRequests: processedRequests.filter(
          (r) => parseInt(r.status) === 2
        ).length,
        urgentNotifications: 0,
        totalDonors,
      };

      setDashboardData((prev) => ({
        ...prev,
        statistics,
        recentRequests: processedRequests
          .sort((a, b) => new Date(b.createdTime) - new Date(a.createdTime))
          .slice(0, 5),
      }));
    } catch (err) {
      console.error("Error loading dashboard stats:", err);
      setError("Không thể tải thống kê dashboard");
    }
  }, [currentUser?.id, isBloodDepartment]);

  /**
   * Load blood inventory data
   */
  const loadBloodInventory = useCallback(async () => {
    try {
      const inventoryData = await fetchBloodInventory();

      if (!inventoryData || !Array.isArray(inventoryData)) {
        console.warn("Invalid inventory data:", inventoryData);
        setDashboardData((prev) => ({
          ...prev,
          bloodInventory: [],
        }));
        return;
      }

      // Xử lý dữ liệu chi tiết giống manager
      // Map từng item sang đúng cấu trúc inventory
      const processedInventory = inventoryData.map((item) => {
        let rhSymbol = "";
        if (item.rhType === "Rh+" || item.rhType === "+") {
          rhSymbol = "+";
        } else if (item.rhType === "Rh-" || item.rhType === "-") {
          rhSymbol = "-";
        }
        const bloodType = `${item.bloodGroup}${rhSymbol}`;
        const status = getInventoryStatus(parseInt(item.quantity) || 0);
        return {
          ...item,
          bloodType,
          status,
          quantity: parseInt(item.quantity) || 0,
        };
      });

      setDashboardData((prev) => ({
        ...prev,
        bloodInventory: processedInventory,
      }));
    } catch (err) {
      console.error("Error loading blood inventory:", err);
      setError("Không thể tải dữ liệu kho máu");
    }
  }, []);

  /**
   * Load notifications - simplified to avoid infinite loop
   */
  const loadNotifications = useCallback(async () => {
    try {
      // For now, just set empty notifications to avoid infinite loop
      // In the future, this should call a real notifications API
      const notifications = [];
      let urgentCount = 0;

      setDashboardData((prev) => ({
        ...prev,
        notifications,
        statistics: {
          ...prev.statistics,
          urgentNotifications: urgentCount,
        },
      }));
    } catch (err) {
      console.error("Error loading notifications:", err);
      setError("Không thể tải thông báo");
    }
  }, []);

  /**
   * Mark notification as read
   */
  const markNotificationAsRead = useCallback(async (notificationId) => {
    try {
      setDashboardData((prev) => ({
        ...prev,
        notifications: prev.notifications.map((notification) =>
          notification.id === notificationId
            ? { ...notification, isRead: true }
            : notification
        ),
      }));

      // Update urgent notifications count
      setDashboardData((prev) => ({
        ...prev,
        statistics: {
          ...prev.statistics,
          urgentNotifications: prev.notifications.filter(
            (n) =>
              n.type === "emergency" && !n.isRead && n.id !== notificationId
          ).length,
        },
      }));
    } catch (err) {
      console.error("Error marking notification as read:", err);
    }
  }, []);

  /**
   * Refresh all dashboard data
   */
  const refreshDashboard = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // Load data sequentially to better handle errors
      await loadDashboardStats();
      await loadBloodInventory();
      await loadNotifications();
    } catch (err) {
      console.error("Error refreshing dashboard:", err);
      setError(
        `Không thể tải dữ liệu dashboard: ${err.message || "Unknown error"}`
      );
    } finally {
      setLoading(false);
    }
  }, [loadDashboardStats, loadBloodInventory, loadNotifications]);

  // Load data on mount
  useEffect(() => {
    refreshDashboard();
  }, [refreshDashboard]);

  return {
    loading,
    error,
    dashboardData,
    isBloodDepartment,
    currentUser,
    refreshDashboard,
    markNotificationAsRead,
  };
};

export default useDoctorDashboard;
