import React from 'react';
import {
  DONATION_STATUS,
  REQUEST_STATUS
} from '../../constants/systemConstants';
import { DonationStatusBadge, RequestStatusBadge, UrgencyBadge } from './StatusBadge';
import '../../styles/components/SimpleStatusTracker.scss';

const SimpleStatusTracker = ({ 
  currentStatus, 
  workflowType = 'donation',
  urgency = null,
  compact = false 
}) => {
  
  // Determine current status based on workflow
  const currentStatus = status || (workflowType === 'donation' ? DONATION_STATUS.REGISTERED : REQUEST_STATUS.PENDING);

  // Determine type based on workflowType if not provided
  const type = workflowType || 'donation';

  if (compact) {
    return (
      <div className="simple-status-tracker compact">
        {type === "donation" ? (
          <DonationStatusBadge status={currentStatus} size="small" />
        ) : (
          <RequestStatusBadge status={currentStatus} size="small" />
        )}
        {urgency !== null && (
          <UrgencyBadge level={urgency} size="small" />
        )}
      </div>
    );
  }

  return (
    <div className="simple-status-tracker">
      <div className="status-card">
        <div className="status-badge-container">
          {type === "donation" ? (
            <DonationStatusBadge status={currentStatus} size="default" />
          ) : (
            <RequestStatusBadge status={currentStatus} size="default" />
          )}
        </div>
        <div className="status-details">
          <div className="status-type">
            {workflowType === 'donation' ? 'Hiến máu' : 'Yêu cầu máu'}
          </div>
        </div>
        {urgency !== null && (
          <div className="urgency-info">
            <UrgencyBadge level={urgency} size="default" />
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleStatusTracker;
