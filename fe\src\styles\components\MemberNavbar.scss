@use './NavbarCommon' as *;
@use '../base/variables' as vars;
@use '../base/mixin' as *;

.member-navbar {
  @extend .navbar;
  background-color: #fff;
  position: relative;

  .navbar-logo {
    color: vars.$secondary-color;
    font-weight: bold;
    font-size: 1.3rem;
    letter-spacing: 1px;
    z-index: 1001;
  }

  // Desktop Navigation
  .desktop-nav {
    a {
      color: black;

      &.active {
        color: vars.$secondary-color;
      }
    }
  }

  // Mobile Menu Toggle Button
  .mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-around;
    width: 30px;
    height: 30px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 1001;

    .hamburger-line {
      width: 100%;
      height: 3px;
      background-color: vars.$secondary-color;
      border-radius: 2px;
      transition: all 0.3s ease;
      transform-origin: center;

      &.active {
        &:nth-child(1) {
          transform: rotate(45deg) translate(7px, 7px);
        }

        &:nth-child(2) {
          opacity: 0;
        }

        &:nth-child(3) {
          transform: rotate(-45deg) translate(7px, -7px);
        }
      }
    }
  }

  // Mobile Navigation Overlay
  .mobile-nav-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;

    &.active {
      opacity: 1;
      visibility: visible;
    }

    .mobile-nav {
      position: absolute;
      top: 0;
      right: 0;
      width: 280px;
      height: 100%;
      background: #fff;
      box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
      transform: translateX(100%);
      transition: transform 0.3s ease;
      overflow-y: auto;

      .mobile-nav-header {
        padding: vars.$spacing-lg;
        background: vars.$primary-color;
        color: #fff;

        .mobile-user-info {
          @include flex-align(flex-start, center);
          gap: vars.$spacing-sm;

          .mobile-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            @include flex-center;
            font-weight: bold;
            font-size: 1.2rem;
          }

          .mobile-user-name {
            font-weight: 600;
            font-size: 1.1rem;
          }
        }
      }

      .mobile-nav-items {
        padding: vars.$spacing-base 0;

        .mobile-nav-item {
          display: block;
          padding: vars.$spacing-sm vars.$spacing-lg;
          text-decoration: none;
          color: vars.$text-color;
          font-weight: 500;
          border: none;
          background: none;
          width: 100%;
          text-align: left;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: #f8f9fa;
            color: vars.$primary-color;
          }

          &.active {
            background-color: rgba(vars.$primary-color, 0.1);
            color: vars.$primary-color;
            font-weight: 600;
          }

          &.logout-btn {
            color: vars.$secondary-color;
            font-weight: 600;

            &:hover {
              background-color: #f8d7da;
              color: vars.$secondary-hover;
            }
          }
        }

        .mobile-nav-divider {
          height: 1px;
          background: #e9ecef;
          margin: vars.$spacing-sm 0;
        }
      }
    }

    &.active .mobile-nav {
      transform: translateX(0);
    }
  }

  // Desktop Actions
  .desktop-actions {
    .user-info {
      .user-name {
        font-weight: 600;
        margin-right: 8px;
      }

      .member-avatar-wrapper {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: vars.$primary-color;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 1.1rem;
        cursor: pointer;
        transition: box-shadow 0.2s;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      }
    }

    .member-dropdown-menu {
      position: absolute;
      top: 60px;
      right: 40px;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      z-index: 2000;
      min-width: 200px;
      overflow: hidden;

      .dropdown-divider {
        height: 1px;
        background: #e9ecef;
        margin: 8px 0;
      }

      .dropdown-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        text-decoration: none;
        color: #333;
        border: none;
        background: none;
        width: 100%;
        text-align: left;
        cursor: pointer;
        transition: all 0.2s ease;
        font-weight: 500;

        &:hover {
          background-color: #f8f9fa;
          color: vars.$primary-color;
        }

        &.logout-btn {
          color: vars.$secondary-color;
          font-weight: 600;

          &:hover {
            background-color: #f8d7da;
            color: vars.$secondary-hover;
          }
        }
      }
    }
  }

  // Responsive Design
  @include tablet {
    .desktop-nav {
      gap: 20px;
    }

    .desktop-actions {
      .user-info .user-name {
        display: none;
      }
    }

    .member-dropdown-menu {
      right: 20px;
      min-width: 180px;
    }
  }

  @include mobile {
    .desktop-nav {
      display: none;
    }

    .desktop-actions {
      display: none;
    }

    .mobile-menu-toggle {
      display: flex;
    }

    .navbar-logo {
      font-size: 1.2rem;
    }
  }

  // Small mobile devices
  @media (max-width: 480px) {
    .mobile-nav-overlay .mobile-nav {
      width: 100%;
    }

    .navbar-logo {
      font-size: 1.1rem;
    }
  }

  // Avatar Notification Dot
  .member-avatar-wrapper,
  .mobile-avatar {
    position: relative;

    .avatar-notification-dot {
      position: absolute;
      top: 2px;
      right: 2px;
      width: 12px;
      height: 12px;
      background: #ef4444;
      border: 2px solid white;
      border-radius: 50%;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      animation: pulse 2s infinite;
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }

    50% {
      transform: scale(1.1);
      opacity: 0.8;
    }

    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  .dropdown-item,
  .mobile-nav-item {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .notification-badge {
      background: #ef4444;
      color: white;
      font-size: 0.7rem;
      font-weight: 600;
      padding: 2px 6px;
      border-radius: 10px;
      min-width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
      margin-left: 0.5rem;
    }
  }
}