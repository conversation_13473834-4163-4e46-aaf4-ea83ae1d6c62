import { Card, Row, Col, Statistic, Skeleton } from "antd";

/**
 * Component hiển thị thống kê người hiến máu
 * Updated to match BloodRequestStats structure
 */
const DonorStatistics = ({ statistics, loading = false }) => {
  const {
    todayCount = 0,
    approvedCount = 0,
    rejectedCount = 0,
    pendingCount = 0,
    totalCount = 0,
    cancelledCount = 0
  } = statistics || {};

  console.log("📊 DonorStatistics received:", statistics);

  const stats = [
    {
      title: "Tổng cộng",
      value: totalCount,
      color: "#1677ff",
    },
    {
      title: "Hôm nay",
      value: todayCount,
      color: "#faad14",
    },
    {
      title: "Đã đăng kí",
      value: pendingCount,
      color: "#722ed1",
    },
    {
      title: "Chấp nhận",
      value: approvedCount,
      color: "#52c41a",
    },
    {
      title: "Không chấp nhận",
      value: rejectedCount,
      color: "#dc3545",
    },
  ];

  if (loading) {
    return (
      <Row gutter={16} style={{ marginBottom: 24 }}>
        {Array.from({ length: 5 }).map((_, index) => (
          <Col xs={24} sm={12} md={8} lg={4.8} key={index}>
            <Card>
              <Skeleton active paragraph={{ rows: 1 }} />
            </Card>
          </Col>
        ))}
      </Row>
    );
  }

  return (
    <Row gutter={16} style={{ marginBottom: 24 }}>
      {stats.map((stat) => (
        <Col xs={24} sm={12} md={8} lg={4.8} key={stat.title}>
          <Card>
            <Statistic
              title={stat.title}
              value={stat.value}
              valueStyle={{ color: stat.color, fontWeight: 600 }}
            />
          </Card>
        </Col>
      ))}
    </Row>
  );
};

export default DonorStatistics;
