@use '../base/variables' as vars;
@use '../base/mixin' as mixins;

.simple-status-tracker {
  &.compact {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .status-badge.compact {
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      color: white;
      font-size: 0.8rem;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 0.25rem;
    }

    .urgency-badge.compact {
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      color: white;
      font-size: 0.75rem;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 0.25rem;
    }
  }

  &:not(.compact) {
    .status-card {
      background: white;
      border-radius: 8px;
      padding: 1rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      border: 1px solid #e9ecef;
      display: flex;
      align-items: center;
      gap: 1rem;

      .status-icon {
        font-size: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.05);
      }

      .status-details {
        flex: 1;

        .status-title {
          font-size: 1.1rem;
          font-weight: 600;
          color: #333;
          margin-bottom: 0.25rem;
        }

        .status-type {
          font-size: 0.9rem;
          color: #6c757d;
        }
      }

      .urgency-info {
        .urgency-badge {
          padding: 0.5rem 1rem;
          border-radius: 6px;
          color: white;
          font-size: 0.9rem;
          font-weight: 500;
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }
      }
    }
  }
}
