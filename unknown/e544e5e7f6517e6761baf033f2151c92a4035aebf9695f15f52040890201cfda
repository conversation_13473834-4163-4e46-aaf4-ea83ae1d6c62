// <PERSON><PERSON><PERSON> sắc
$primary-color: #1976d2; // Xanh dương chính
$primary-hover: #1565c0; // Xanh dương khi hover
$secondary-color: #d32f2f; // Đỏ chính
$secondary-hover: #c21121; // Đỏ khi hover
$dark-blue: #02314b; // Xanh đậm cho nền section
$background-main: #f6fbfd; // N<PERSON><PERSON> chính
$background-box: #e3f2fd; // N<PERSON>n hộp (nhóm máu dươ<PERSON>)
$background-box-secondary: #ffebee; // N<PERSON>n hộp (nhóm máu âm)
$background-light: #f6f6f6; // Nền sáng
$text-color: #222; // <PERSON><PERSON><PERSON> chữ chính
$text-secondary: #6c757d; // Màu chữ phụ
$white: #fff; // Trắng
$black: #000; // Đen
$border-light: #e8ecef; // <PERSON>i<PERSON><PERSON> sáng
$shadow-color: rgba(0, 0, 0, 0.2); // <PERSON><PERSON>u bóng

// Khoảng cách
$spacing-xxl: 64px;
$spacing-xl: 48px;
$spacing-lg: 32px;
$spacing-md: 20px;
$spacing-base: 16px;
$spacing-sm: 12px;
$spacing-xs: 8px;
$spacing-xxs: 4px;

// Extended spacing for admin components only
$spacing-1: 4px;
$spacing-2: 8px;
$spacing-3: 12px;
$spacing-4: 16px;
$spacing-5: 20px;
$spacing-6: 24px;
$spacing-7: 28px;
$spacing-8: 32px;
$spacing-10: 40px;
$spacing-12: 48px;
$spacing-16: 64px;

// Font size
$font-size-xlarge: 2.25rem; // 36px
$font-size-large: 1.5rem; // 24px
$font-size-base: 1rem; // 16px
$font-size-small: 0.875rem; // 14px

// Extended font sizes
$font-size-xs: 0.75rem; // 12px
$font-size-sm: 0.875rem; // 14px
$font-size-lg: 1.125rem; // 18px
$font-size-xl: 1.25rem; // 20px
$font-size-2xl: 1.5rem; // 24px
$font-size-3xl: 1.875rem; // 30px
$font-size-4xl: 2.25rem; // 36px
$font-size-5xl: 3rem; // 48px

// Font weights
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;
$font-weight-extrabold: 800;

// Font family
$font-primary: "Montserrat", "Poppins", Arial, Helvetica, sans-serif;
$font-secondary: "Open Sans", "Roboto", "Inter", Arial, Helvetica, sans-serif;
$font-manager: "Inter", "Roboto", "Segoe UI", sans-serif; // Font cho Manager

// Extended colors
$accent-color: #02314b; // Same as dark-blue
$primary-light: #e3f2fd;
$secondary-light: #ffebee;
$secondary-dark: #b71c1c;

$success-color: #4caf50;
$warning-color: #ff9800;
$error-color: #f44336;
$info-color: #2196f3;

// Manager Theme Colors (Minimalistic)
$manager-primary: #d93e4c; // Màu chính cho Manager - nổi bật
$manager-dark: #20374e; // Nền tối sang trọng cho báo cáo
$manager-warm: #deccaa; // Màu ấm áp cho vùng phụ trợ
$manager-emergency: #d91022; // Cảnh báo/khẩn cấp
$manager-bg: #ffffff; // Nền trắng
$manager-bg-light: #f5f7fa; // Nền xanh nhạt
$manager-text: #2c3e50; // Text chính
$manager-text-light: #6c757d; // Text phụ
$manager-border: #e9ecef; // Border nhẹ
$manager-hover: #f8f9fa; // Hover state
$manager-shadow: rgba(0, 0, 0, 0.08); // Shadow nhẹ

$text-primary: #222;
$text-secondary: #6c757d;
$text-muted: #9e9e9e;
$text-inverse: #ffffff;

$background-card: #ffffff;
$background-section: #f8f9fa;
$background-light: #f6f6f6;

// Background variants
$bg-light: #f8f9fa;
$bg-hover: #f1f3f4;

$border-medium: #dee2e6;

// Border colors
$border-color: #e9ecef;

// Border radius
$border-radius-sm: 4px;
$border-radius-base: 8px;
$border-radius-md: 6px;
$border-radius-lg: 12px;
$border-radius-full: 50%;

// Line heights
$line-height-tight: 1.25;
$line-height-normal: 1.5;
$line-height-relaxed: 1.75;

// Z-index
$z-index-dropdown: 1000;
$z-index-fixed: 1020;
$z-index-modal: 1050;

// Shadows
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
  0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
  0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
  0 10px 10px -5px rgba(0, 0, 0, 0.04);
