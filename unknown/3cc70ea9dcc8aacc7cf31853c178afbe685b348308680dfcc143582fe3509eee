@use '../base/variables' as vars;
@use '../base/mixin' as mix;

.login-form__container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: unset;
    background: transparent;
    width: 100%;
    padding: 0 10px;
}

.login-form__box {
    background: transparent;
    border-radius: 24px;
    padding: 40px 32px 32px 32px;
    width: 100%;
    max-width: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.login-form__logo {
    font-family: vars.$font-primary;
    font-size: 2.2rem;
    font-weight: 900;
    margin-bottom: 20px;
    letter-spacing: 2px;
    text-align: center;
    background: linear-gradient(135deg, vars.$primary-color, vars.$secondary-color);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.login-form__welcome {
    font-family: vars.$font-primary;
    font-size: 1.5rem;
    font-weight: 900;
    margin-bottom: 36px;
    text-align: center;
    line-height: 1.3;
    letter-spacing: 0.5px;
    color: vars.$text-color;
}

.login-form__google-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 350px;
    padding: 14px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    background: #fff;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    gap: 12px;
    margin-bottom: 28px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border-color: #d0d0d0;
        transform: translateY(-1px);
    }

    &:active {
        transform: translateY(0);
    }

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }
}

.login-form__google-icon {
    display: flex;
    align-items: center;
    margin-right: 8px;
}

.login-form__divider {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 320px;
    margin: 16px 0 24px 0;
    color: #bbb;
    font-size: 13px;
    gap: 8px;

    span:first-child,
    span:last-child {
        flex: 1;
        height: 1px;
        background: #eee;
    }
}

.login-form__divider-text {
    padding: 0 8px;
    white-space: nowrap;
    color: #bbb;
    font-weight: 400;
    font-size: 13px;
    letter-spacing: 0.5px;
}

.login-form__form {
    width: 100%;
    max-width: 320px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.password-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.password-toggle-btn {
    position: absolute;
    right: 16px;
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    font-size: 16px;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s ease;
    z-index: 1;

    &:hover {
        color: vars.$primary-color;
    }

    &:focus {
        outline: none;
        color: vars.$primary-color;
    }
}

.login-form__label {
    font-family: vars.$font-secondary;
    font-weight: 700;
    font-size: 15px;
    margin-bottom: 4px;
    letter-spacing: 0.5px;
}

.login-form__input {
    width: 100%;
    padding: 14px 50px 14px 16px; // Add right padding for the eye icon
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    font-size: 16px;
    outline: none;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    background: #fafafa;
    font-family: vars.$font-secondary;
    color: vars.$text-color;

    &:focus {
        border: 2px solid vars.$primary-color;
        background: #fff;
        box-shadow: 0 0 0 3px rgba(vars.$primary-color, 0.1);
        transform: translateY(-1px);
    }

    &::placeholder {
        color: #999;
        font-size: 15px;
    }
}

.login-form__submit {
    width: 100%;
    max-width: 200px;
    padding: 14px 0;
    background: linear-gradient(135deg, vars.$secondary-color, vars.$secondary-hover);
    color: #fff;
    border: none;
    border-radius: 12px;
    font-family: vars.$font-secondary;
    font-weight: 700;
    font-size: 16px;
    margin-top: 12px;
    cursor: pointer;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    align-self: center;
    position: relative;
    overflow: hidden;

    &:hover {
        background: linear-gradient(135deg, vars.$secondary-hover, vars.$secondary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(vars.$secondary-color, 0.3);
    }

    &:active {
        transform: translateY(0);
    }

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }
}

.login-form__register {
    text-align: center;
    margin-top: 36px;
    font-size: 15px;
    font-family: vars.$font-secondary;
    color: vars.$text-color;

    span {
        font-weight: 700;
        text-decoration: underline;
        color: vars.$secondary-color;
        margin-left: 4px;
    }
}

.login-form__success {
    color: #28a745;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    margin-bottom: 0.5rem;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    padding: 8px 12px;
}

.login-form__error {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    margin-bottom: 0.5rem;
}

.login-form__forgot-password {
    text-align: center;
    margin-top: 16px;
    font-size: 14px;
    font-family: vars.$font-secondary;

    a {
        color: vars.$secondary-color;
        text-decoration: none;
        font-weight: 600;

        &:hover {
            text-decoration: underline;
        }
    }
}

.login-form__register {
    text-align: center;
    margin-top: 20px;
    font-size: 15px;
    font-family: vars.$font-secondary;
    color: vars.$text-color;

    span {
        font-weight: 700;
        text-decoration: underline;
        color: vars.$secondary-color;
        margin-left: 4px;
    }
}

// Responsive Design
@media (max-width: 768px) {
    .login-form__container {
        padding: 0 5px;
    }

    .login-form__box {
        max-width: 100%;
        padding: 30px 20px 25px 20px;
    }

    .login-form__logo {
        font-size: 1.8rem;
        margin-bottom: 15px;
    }

    .login-form__welcome {
        font-size: 1.3rem;
        margin-bottom: 30px;
    }

    .login-form__form,
    .login-form__google-btn,
    .login-form__divider {
        max-width: 100%;
    }

    .login-form__google-btn {
        padding: 12px 16px;
        font-size: 15px;
    }

    .login-form__input {
        padding: 12px 14px;
        font-size: 15px;
    }

    .login-form__submit {
        padding: 12px 0;
        font-size: 15px;
    }
}

@media (max-width: 576px) {
    .login-form__box {
        padding: 20px 15px;
    }

    .login-form__logo {
        font-size: 1.6rem;
        margin-bottom: 12px;
    }

    .login-form__welcome {
        font-size: 1.2rem;
        margin-bottom: 25px;
        line-height: 1.4;
    }

    .login-form__google-btn {
        padding: 11px 14px;
        font-size: 14px;
        margin-bottom: 20px;
    }

    .login-form__input {
        padding: 11px 12px;
        font-size: 14px;
        margin-bottom: 10px;
    }

    .login-form__label {
        font-size: 14px;
    }

    .login-form__submit {
        padding: 11px 0;
        font-size: 14px;
        margin-top: 8px;
    }

    .login-form__register {
        font-size: 13px;
        margin-top: 25px;
    }

    .login-form__forgot-password {
        font-size: 13px;
        margin-top: 12px;
    }
}

@media (max-width: 480px) {
    .login-form__container {
        padding: 0;
    }

    .login-form__box {
        padding: 15px 10px;
        max-width: 100%;
    }

    .login-form__logo {
        font-size: 1.4rem;
        margin-bottom: 10px;
    }

    .login-form__welcome {
        font-size: 1.1rem;
        margin-bottom: 20px;
    }

    .login-form__google-btn {
        padding: 10px 12px;
        font-size: 13px;
        margin-bottom: 18px;
        border-radius: 8px;
    }

    .login-form__input {
        padding: 10px;
        font-size: 14px;
        border-radius: 8px;
    }

    .login-form__submit {
        padding: 10px 0;
        font-size: 13px;
        border-radius: 8px;
        max-width: 100%;
    }

    .login-form__divider {
        margin: 12px 0 18px 0;
        font-size: 12px;
    }

    .login-form__register {
        font-size: 12px;
        margin-top: 20px;
    }

    .login-form__forgot-password {
        font-size: 12px;
        margin-top: 10px;
    }
}