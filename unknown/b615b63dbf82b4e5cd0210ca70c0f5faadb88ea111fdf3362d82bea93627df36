.auth-page__container {
    min-height: calc(100vh - 70px); // Account for navbar height
    background: linear-gradient(135deg, #f3f6fb 0%, #e8f2ff 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;

    // Add subtle background pattern
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        pointer-events: none;
    }
}

.auth-page__content {
    display: flex;
    background: #fff;
    border-radius: 32px;
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
    width: 100%;
    max-width: 900px;
    min-height: 500px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;

    &:hover {
        box-shadow: 0 12px 50px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }
}

.auth-page__left {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 32px 0 0 32px;
    min-height: 500px;
    padding: 40px 20px;
    position: relative;

    // Add subtle overlay pattern
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(0,0,0,0.02)"/><circle cx="75" cy="75" r="1" fill="rgba(0,0,0,0.02)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
        pointer-events: none;
    }
}

.auth-page__right {
    flex: 1;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0 32px 32px 0;
    min-height: 500px;
    padding: 40px 20px;
    position: relative;
}

.auth-page__image-placeholder {
    width: 220px;
    height: 220px;
    background: #ccc;
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1.5px solid #fff;
    margin-bottom: 18px;
}

.auth-page__image-real {
    width: 85%;
    max-width: 400px;
    height: auto;
    object-fit: contain;
    display: block;
    margin: 0 auto;
    background: transparent;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;

    &:hover {
        transform: scale(1.02);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }
}

// Responsive styles
@media (max-width: 1200px) {
    .auth-page__container {
        padding: 15px;
    }

    .auth-page__content {
        max-width: 800px;
    }

    .auth-page__image-real {
        max-width: 300px;
    }

    .auth-page__left,
    .auth-page__right {
        padding: 30px 15px;
    }
}

@media (max-width: 992px) {
    .auth-page__content {
        max-width: 700px;
        min-height: 450px;
    }

    .auth-page__left,
    .auth-page__right {
        min-height: 450px;
        padding: 25px 15px;
    }
}

@media (max-width: 768px) {
    .auth-page__container {
        padding: 10px;
        min-height: calc(100vh - 70px);
    }

    .auth-page__content {
        flex-direction: column;
        border-radius: 24px;
        min-height: auto;
        max-width: 500px;
        box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);

        &:hover {
            transform: none;
        }
    }

    .auth-page__left {
        border-radius: 24px 24px 0 0;
        min-height: 200px;
        padding: 20px 15px 10px 15px;
        border-right: none;
        border-bottom: 1px solid #f0f0f0;

        &::before {
            display: none;
        }
    }

    .auth-page__right {
        border-radius: 0 0 24px 24px;
        min-height: auto;
        padding: 20px 15px 30px 15px;
        border-left: none;
        border-top: none;
    }

    .auth-page__image-real {
        max-width: 200px;
        border-radius: 12px;
        box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);

        &:hover {
            transform: scale(1.01);
        }
    }
}

@media (max-width: 576px) {
    .auth-page__container {
        padding: 5px;
        align-items: flex-start;
        padding-top: 20px;
    }

    .auth-page__content {
        border-radius: 16px;
        width: 100%;
        max-width: none;
        margin: 0;
    }

    .auth-page__left {
        border-radius: 16px 16px 0 0;
        min-height: 160px;
        padding: 15px 10px 5px 10px;
    }

    .auth-page__right {
        border-radius: 0 0 16px 16px;
        padding: 15px 10px 20px 10px;
    }

    .auth-page__image-real {
        max-width: 150px;
        border-radius: 8px;
    }
}

@media (max-width: 480px) {
    .auth-page__container {
        padding: 0;
        min-height: 100vh;
    }

    .auth-page__content {
        border-radius: 0;
        min-height: 100vh;
        box-shadow: none;
    }

    .auth-page__left {
        border-radius: 0;
        min-height: 140px;
        padding: 10px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .auth-page__right {
        border-radius: 0;
        padding: 10px;
        flex: 1;
    }

    .auth-page__image-real {
        max-width: 120px;
        border-radius: 6px;
    }
}