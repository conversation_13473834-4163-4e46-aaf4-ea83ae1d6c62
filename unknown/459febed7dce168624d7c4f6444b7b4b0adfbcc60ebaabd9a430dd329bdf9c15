@use '../base/variables' as vars;
@use '../base/mixin' as mix;

.change-password-form__container {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 100%;
}

.change-password-form__header {
    margin-bottom: 24px;
}

.change-password-form__title {
    font-family: vars.$font-primary;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: vars.$text-color;
}

.change-password-form__description {
    font-family: vars.$font-secondary;
    font-size: 0.9rem;
    color: #666;
    line-height: 1.4;
}

.change-password-form__form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.password-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.password-toggle-btn {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    font-size: 16px;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s ease;
    z-index: 1;

    &:hover {
        color: vars.$primary-color;
    }

    &:focus {
        outline: none;
        color: vars.$primary-color;
    }
}

.change-password-form__label {
    font-family: vars.$font-secondary;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 6px;
    color: vars.$text-color;
}

.change-password-form__input {
    width: 100%;
    padding: 10px 40px 10px 12px; // Add right padding for the eye icon
    border: 1.5px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    transition: border 0.2s;
    background: #fafafa;
    font-family: vars.$font-secondary;

    &:focus {
        border: 1.5px solid vars.$primary-color;
        background: #fff;
    }

    &.error {
        border-color: #dc3545;
    }
}

.change-password-form__success {
    color: #28a745;
    font-size: 0.875rem;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    padding: 8px 12px;
    text-align: center;
}

.change-password-form__error {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 4px;
}

.change-password-form__actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 8px;
}

.change-password-form__cancel {
    padding: 10px 20px;
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-family: vars.$font-secondary;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
        background: #e9ecef;
        border-color: #adb5bd;
    }

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
}

.change-password-form__submit {
    padding: 10px 20px;
    background: vars.$secondary-color;
    color: #fff;
    border: none;
    border-radius: 6px;
    font-family: vars.$font-secondary;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.2s;

    &:hover {
        background: vars.$secondary-hover;
    }

    &:disabled {
        background: #ccc;
        cursor: not-allowed;
    }
}

// Responsive
@media (max-width: 600px) {
    .change-password-form__container {
        padding: 20px;
        margin: 0 16px;
    }

    .change-password-form__actions {
        flex-direction: column-reverse;
        gap: 8px;
    }

    .change-password-form__cancel,
    .change-password-form__submit {
        width: 100%;
        padding: 12px;
    }
}