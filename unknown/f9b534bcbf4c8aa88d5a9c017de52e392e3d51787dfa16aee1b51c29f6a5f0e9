@use "../../styles/base/variables" as vars;

// Define missing variables locally
$text-tertiary: #9ca3af;
$text-primary: #222;
$text-secondary: #6c757d;
$primary-color: #1976d2;
$font-primary: "Montser<PERSON>", "<PERSON><PERSON>s", Arial, Helvetica, sans-serif;

.modern-page-header {
  position: relative;
  margin-bottom: 1.5rem;
  overflow: hidden;

  .page-header-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8fffe 50%, #f0f9ff 100%);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08), 0 8px 16px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.8);
    padding: 2.5rem 3rem;
    position: relative;
    z-index: 2;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);

    // Glassmorphism effect
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.1),
        rgba(255, 255, 255, 0.05)
      );
      border-radius: 20px;
      z-index: -1;
    }

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 32px 64px rgba(0, 0, 0, 0.12),
        0 16px 32px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.9);
      border-color: rgba(102, 126, 234, 0.3);
    }

    .page-header-content {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }

    .header-main {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .header-breadcrumb {
      margin-top: 0.5rem;
    }

    .header-title-wrapper {
      display: flex;
      align-items: flex-start;
      gap: 1.5rem;
      margin-bottom: 1rem;

      .title-icon {
        width: 64px;
        height: 64px;
        background: linear-gradient(
          135deg,
          #1976d2 0%,
          #2196f3 50%,
          #03a9f4 100%
        );
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 28px;
        box-shadow: 0 8px 24px rgba(25, 118, 210, 0.3),
          0 4px 8px rgba(25, 118, 210, 0.2);
        transition: all 0.3s ease;
        position: relative;

        &::before {
          content: "";
          position: absolute;
          inset: -2px;
          background: linear-gradient(135deg, #1976d2, #2196f3, #03a9f4);
          border-radius: 18px;
          z-index: -1;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          transform: scale(1.05) rotate(5deg);
          box-shadow: 0 12px 32px rgba(25, 118, 210, 0.4),
            0 6px 12px rgba(25, 118, 210, 0.3);

          &::before {
            opacity: 1;
          }
        }
      }

      .title-content {
        flex: 1;

        .page-title {
          color: $text-primary;
          margin: 0 0 0.5rem 0;
          font-size: 2.5rem;
          font-weight: 800;
          font-family: $font-primary;
          background: linear-gradient(
            135deg,
            #1a1a1a 0%,
            #2563eb 50%,
            #1976d2 100%
          );
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          line-height: 1.2;
          letter-spacing: -0.5px;

          // Fallback for browsers that don't support background-clip
          @supports not (-webkit-background-clip: text) {
            color: $text-primary;
          }
        }

        .page-description {
          color: $text-secondary;
          font-size: 1.1rem;
          font-weight: 500;
          line-height: 1.5;
          margin: 0;
          max-width: 500px;
        }
      }
    }

    .modern-breadcrumb {
      margin-bottom: 0;

      .ant-breadcrumb-link {
        color: $text-secondary;
        font-weight: 500;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 4px;

        &:hover {
          color: $primary-color;
        }
      }

      .ant-breadcrumb-separator {
        color: #9ca3af;
        font-weight: 600;
        margin: 0 8px;
      }

      // Last breadcrumb item (current page)
      li:last-child .ant-breadcrumb-link {
        color: $primary-color;
        font-weight: 600;
      }
    }

    .header-actions {
      .reload-button {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        height: 48px;
        padding: 0 24px;
        font-weight: 600;
        font-size: 1rem;
        color: #1976d2;
        box-shadow: 0 8px 24px rgba(255, 255, 255, 0.2),
          0 4px 8px rgba(255, 255, 255, 0.1);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 12px 32px rgba(255, 255, 255, 0.3),
            0 6px 12px rgba(255, 255, 255, 0.2);
          background: linear-gradient(135deg, #ffffff 0%, #e3f2fd 100%);
          border-color: rgba(255, 255, 255, 0.5);
        }

        &:active:not(:disabled) {
          transform: translateY(0);
        }

        &:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }
      }
    }
  }

  // Decorative elements
  .header-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;

    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      background: linear-gradient(
        135deg,
        rgba(37, 99, 235, 0.1),
        rgba(25, 118, 210, 0.05)
      );
      animation: float 6s ease-in-out infinite;

      &.decoration-1 {
        width: 120px;
        height: 120px;
        top: -60px;
        right: 10%;
        animation-delay: 0s;
      }

      &.decoration-2 {
        width: 80px;
        height: 80px;
        bottom: -40px;
        left: 15%;
        animation-delay: 2s;
      }

      &.decoration-3 {
        width: 60px;
        height: 60px;
        top: 50%;
        right: -30px;
        animation-delay: 4s;
      }
    }
  }
}

// Animations
@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

// Variants
.modern-page-header {
  &.gradient {
    .page-header-container {
      background: linear-gradient(
        135deg,
        #1976d2 0%,
        #2196f3 30%,
        #03a9f4 60%,
        #00bcd4 100%
      );
      color: white;

      .header-title-wrapper {
        .title-content {
          .page-title {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;

            @supports not (-webkit-background-clip: text) {
              color: white;
            }
          }

          .page-description {
            color: rgba(255, 255, 255, 0.9);
          }
        }
      }

      .modern-breadcrumb {
        .ant-breadcrumb-link {
          color: rgba(255, 255, 255, 0.8);

          &:hover {
            color: white;
          }
        }

        li:last-child .ant-breadcrumb-link {
          color: white;
        }

        .ant-breadcrumb-separator {
          color: rgba(255, 255, 255, 0.6);
        }
      }
    }
  }

  &.glass {
    .page-header-container {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
  }

  &.dark {
    .page-header-container {
      background: linear-gradient(
        135deg,
        #1a1a1a 0%,
        #2d2d2d 50%,
        #1a1a1a 100%
      );
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.1);

      .header-title-wrapper {
        .title-content {
          .page-title {
            background: linear-gradient(135deg, #ffffff 0%, #64b5f6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;

            @supports not (-webkit-background-clip: text) {
              color: white;
            }
          }

          .page-description {
            color: rgba(255, 255, 255, 0.7);
          }
        }
      }

      .modern-breadcrumb {
        .ant-breadcrumb-link {
          color: rgba(255, 255, 255, 0.6);

          &:hover {
            color: rgba(255, 255, 255, 0.9);
          }
        }

        li:last-child .ant-breadcrumb-link {
          color: white;
        }

        .ant-breadcrumb-separator {
          color: rgba(255, 255, 255, 0.4);
        }
      }
    }
  }
}

// Apply animations to header elements
.modern-page-header {
  animation: fadeInUp 0.6s ease-out;

  .page-header-container {
    .header-title-wrapper {
      .title-icon {
        animation: fadeInUp 0.8s ease-out 0.2s both;
      }

      .title-content {
        .page-title {
          animation: fadeInUp 0.8s ease-out 0.3s both;
        }

        .page-description {
          animation: fadeInUp 0.8s ease-out 0.4s both;
        }
      }
    }

    .modern-breadcrumb {
      animation: fadeInUp 0.8s ease-out 0.5s both;
    }

    .header-actions {
      animation: slideInRight 0.8s ease-out 0.4s both;

      .reload-button:hover:not(:disabled) {
        animation: pulse 0.6s ease-in-out;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .modern-page-header {
    margin-bottom: 1rem;

    .page-header-container {
      padding: 2rem 1.5rem;
      border-radius: 16px;

      .header-main {
        flex-direction: column;
        align-items: stretch;
        gap: 1.5rem;

        .header-actions {
          align-self: center;
        }
      }

      .header-title-wrapper {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 1rem;

        .title-icon {
          width: 56px;
          height: 56px;
          font-size: 24px;
          border-radius: 14px;
        }

        .title-content {
          .page-title {
            font-size: 2rem;
          }

          .page-description {
            font-size: 1rem;
            max-width: none;
          }
        }
      }

      .modern-breadcrumb {
        text-align: center;

        .ant-breadcrumb-link {
          font-size: 0.9rem;
        }
      }

      .header-actions {
        justify-content: center;

        .reload-button {
          height: 44px;
          padding: 0 20px;
          font-size: 0.95rem;
        }
      }
    }

    .header-decoration {
      .decoration-circle {
        &.decoration-1 {
          width: 80px;
          height: 80px;
          top: -40px;
        }

        &.decoration-2 {
          width: 60px;
          height: 60px;
          bottom: -30px;
        }

        &.decoration-3 {
          display: none;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .modern-page-header {
    .page-header-container {
      padding: 1.5rem 1rem;
      border-radius: 12px;

      .header-title-wrapper {
        .title-icon {
          width: 48px;
          height: 48px;
          font-size: 20px;
          border-radius: 12px;
        }

        .title-content {
          .page-title {
            font-size: 1.75rem;
          }

          .page-description {
            font-size: 0.95rem;
          }
        }
      }

      .header-actions {
        .reload-button {
          height: 40px;
          padding: 0 16px;
          font-size: 0.9rem;
        }
      }
    }
  }
}
