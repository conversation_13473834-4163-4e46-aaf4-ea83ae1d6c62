{"name": "fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 5173", "dev-network": "vite --host 0.0.0.0 --port 5173", "dev-3000": "vite --port 3000", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/charts": "^2.3.0", "@ant-design/icons": "^6.0.0", "antd": "^5.26.3", "aos": "^2.3.4", "axios": "^1.10.0", "bootstrap": "^5.3.7", "dayjs": "^1.11.13", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "prop-types": "^15.8.1", "prosemirror-commands": "^1.7.1", "prosemirror-example-setup": "^1.2.3", "prosemirror-history": "^1.4.1", "prosemirror-keymap": "^1.2.3", "prosemirror-menu": "^1.2.5", "prosemirror-model": "^1.25.1", "prosemirror-schema-basic": "^1.2.4", "prosemirror-schema-list": "^1.5.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.40.0", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-dom": "^19.1.0", "react-highlight-words": "^0.21.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.1", "recharts": "^2.15.3", "sass": "^1.89.0", "swiper": "^11.2.8", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}