@use "../base/variables" as vars;
@use "../base/mixin" as mix;

.member-info-bg {
  min-height: 100vh;
  background: vars.$background-main;
  padding: vars.$spacing-xl 0;
}

.member-info-header {
  background: vars.$white;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(vars.$black, 0.08);
  @include mix.flex-align(flex-start, center);
  gap: vars.$spacing-md;
  padding: vars.$spacing-lg vars.$spacing-xl;
  max-width: 1000px;
  margin: 0 auto vars.$spacing-lg auto;

  .avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background: vars.$background-light;
    @include mix.flex-center;
    font-size: vars.$font-size-xlarge;
    color: vars.$text-secondary;
  }

  h2 {
    @include mix.heading(vars.$font-size-large, vars.$text-color);
    margin-bottom: vars.$spacing-xxs;
  }

  p {
    @include mix.text(vars.$font-size-base, vars.$text-secondary);
    margin-bottom: 0;
  }
}

.member-info-form-box {
  background: vars.$white;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(vars.$black, 0.08);
  max-width: 1000px;
  margin: 0 auto;
  padding: vars.$spacing-lg vars.$spacing-xl;
}

.member-info-form {
  @include mix.flex-align(flex-start, flex-start);
  gap: vars.$spacing-lg;
  flex-wrap: wrap;

  .form-col {
    flex: 1 1 0;
    min-width: 260px;
  }

  .form-group {
    margin-bottom: vars.$spacing-md;
  }

  label {
    @include mix.text(vars.$font-size-base, vars.$text-color);
    font-weight: 500;
    margin-bottom: vars.$spacing-xs;
    display: block;
  }

  input,
  select {
    border-radius: 8px;
    min-height: 40px;
    border: 1px solid vars.$border-light;
    font-family: vars.$font-secondary;

    &:focus {
      box-shadow: 0 0 0 2px rgba(vars.$primary-color, 0.2);
      border-color: vars.$primary-color;
    }
  }

  // Style cho input email disabled
  input[name="email"]:disabled {
    background-color: #f8f9fa !important;
    color: #6c757d;
    cursor: not-allowed;
    border-color: #dee2e6;
    opacity: 0.8;

    &::placeholder {
      color: #6c757d;
      opacity: 0.7;
    }
  }

  // Style cho text muted dưới email field
  .form-text.text-muted {
    color: #6c757d !important;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
  }

  .invalid-feedback {
    @include mix.text(vars.$font-size-small, vars.$secondary-color);
  }

  .form-control.is-invalid {
    border-color: vars.$secondary-color;
  }
}

.input-box {
  border: 1.5px solid vars.$border-light;
  border-radius: 12px;
  padding: vars.$spacing-base vars.$spacing-sm;
  margin-bottom: vars.$spacing-md;
  background: vars.$background-light;
  box-shadow: 0 2px 8px rgba(vars.$shadow-color, 0.06);
  transition: all 0.2s ease;

  &:hover {
    border-color: rgba(vars.$primary-color, 0.4);
  }

  &:focus-within {
    border-color: vars.$primary-color;
    box-shadow: 0 0 0 3px rgba(vars.$primary-color, 0.1);
  }

  label {
    @include mix.text(vars.$font-size-small, vars.$text-color);
    font-weight: 600;
    margin-bottom: vars.$spacing-xs;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  input,
  select {
    border: none;
    // background: transparent;
    width: 100%;
    font-family: vars.$font-secondary;
    color: vars.$text-color;

    &:focus {
      outline: none;
      box-shadow: none;
    }

    &:disabled {
      background: rgba(vars.$text-secondary, 0.1);
      color: vars.$text-secondary;
    }
  }
}

.member-info-actions {
  @include mix.flex-center;
  gap: vars.$spacing-md;
  margin-top: vars.$spacing-xl;

  .btn-secondary {
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
    border-radius: 24px;
    font-size: vars.$font-size-base;
    padding: vars.$spacing-sm vars.$spacing-xl;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background-color: #e9ecef;
      border-color: #adb5bd;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .btn-primary {
    @include mix.primary-button(vars.$secondary-color, vars.$secondary-hover);
    border-radius: 24px;
    font-size: vars.$font-size-base;
    padding: vars.$spacing-sm vars.$spacing-xl;
    box-shadow: 0 4px 12px rgba(vars.$secondary-color, 0.25);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 700;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(vars.$secondary-color, 0.35);
    }
  }
}

// Responsive Design
@include mix.tablet {
  .member-info-form-box {
    padding: vars.$spacing-md vars.$spacing-base;
    margin: 0 vars.$spacing-base;
  }

  .member-info-header {
    padding: vars.$spacing-md vars.$spacing-base;
    margin: 0 vars.$spacing-base vars.$spacing-md vars.$spacing-base;
  }

  .member-info-form {
    flex-direction: column;
    gap: 0;
  }
}

@include mix.mobile {
  .member-info-bg {
    padding: vars.$spacing-md 0;
  }

  .member-info-header {
    .avatar {
      width: 48px;
      height: 48px;
      font-size: vars.$font-size-large;
    }

    h2 {
      font-size: vars.$font-size-base;
    }

    p {
      font-size: vars.$font-size-small;
    }
  }

  .input-box {
    padding: vars.$spacing-sm vars.$spacing-xs;
  }

  .member-info-actions {
    flex-direction: column;
    gap: vars.$spacing-sm;

    .btn-primary,
    .btn-secondary {
      padding: vars.$spacing-xs vars.$spacing-md;
      font-size: vars.$font-size-small;
      width: 100%;
    }
  }
}
