import React, { useState, useEffect, useRef } from "react";
import { Modal, Form, Input, Select } from "antd";
import { getDepartmentNameById } from "../../../utils/departmentUtils";

const { Option } = Select;

const UserFormModal = ({
  visible,
  onOk,
  onCancel,
  editingUser,
  form,
  statusOptions,
  departments = [],
}) => {
  const [selectedRole, setSelectedRole] = useState(null);
  const isInitialized = useRef(false);

  // Watch form field changes
  const watchedRole = Form.useWatch("roleID", form);

  // Role mapping
  const roleStringToId = {
    member: 1,
    doctor_blood: 2,
    doctor_other: 2,
    doctor: 2,
    manager: 3,
    admin: 4,
  };

  const roleIdToString = {
    1: "member",
    2: "doctor",
    3: "manager",
    4: "admin",
  };



  // Get role ID from editingUser
  const getUserRoleId = (user) => {
    if (!user) return undefined;

    // If roleID is already a number, use it
    if (typeof user.roleID === "number") {
      return user.roleID;
    }

    // If roleID is a string number, parse it
    if (typeof user.roleID === "string" && !isNaN(user.roleID)) {
      return parseInt(user.roleID);
    }

    // If role is a string, map it to ID
    if (typeof user.role === "string") {
      return roleStringToId[user.role] || undefined;
    }

    // If roleID is a string, try to map it
    if (typeof user.roleID === "string") {
      return roleStringToId[user.roleID] || undefined;
    }

    return undefined;
  };

  // Reset form and state when modal opens/closes or editingUser changes
  useEffect(() => {
    if (visible) {
      if (editingUser) {
        // Editing existing user
        const roleId = getUserRoleId(editingUser);

        // Convert departmentId to number for proper matching
        let departmentId = null;

        if (editingUser.departmentId) {
          departmentId = Number(editingUser.departmentId);
        } else if (editingUser.department) {
          // Fallback: if we have department name but no departmentId, try to find the ID
          departmentId = DEPARTMENT_IDS[editingUser.department] || null;
        }

        form.setFieldsValue({
          name: editingUser.name || "",
          email: editingUser.email || "",
          phone: editingUser.phone || "",
          roleID: roleId,
          status: editingUser.status || "active",
          departmentId: departmentId,
        });
        setSelectedRole(roleId);
      } else {
        // Creating new user - only reset once when modal opens
        if (!isInitialized.current) {
          form.resetFields();
          form.setFieldsValue({
            status: "active",
          });
          setSelectedRole(null);
          isInitialized.current = true;
        }
      }
    } else {
      // Modal is closed, reset everything
      form.resetFields();
      setSelectedRole(null);
      isInitialized.current = false; // Reset for next time
    }
  }, [visible, editingUser]);

  const handleCancel = () => {
    form.resetFields();
    setSelectedRole(null);
    onCancel();
  };

  return (
    <Modal
      title={editingUser ? "Chỉnh sửa người dùng" : "Thêm người dùng"}
      open={visible}
      onOk={onOk}
      onCancel={handleCancel}
      okText={editingUser ? "Cập nhật" : "Thêm mới"}
      cancelText="Hủy"
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="name"
          label="Họ và tên"
          rules={[{ required: true, message: "Vui lòng nhập họ tên" }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name="email"
          label="Email"
          rules={[
            { required: true, type: "email", message: "Email không hợp lệ" },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name="phone"
          label="Số điện thoại"
          rules={[{ required: true, message: "Vui lòng nhập số điện thoại" }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name="roleID"
          label="Vai trò"
          rules={[{ required: true, message: "Chọn vai trò" }]}
          extra={
            editingUser && getUserRoleId(editingUser) === 1 ? (
              <span style={{ color: "#888", fontSize: 12 }}>
                Vai trò "Thành viên" không thể thay đổi.
              </span>
            ) : null
          }
        >
          <Select
            onChange={(value) => {
              console.log("Role selected:", value);
              setSelectedRole(value);

              // Update form with new role
              const newValues = { roleID: value };

              // Reset department if not doctor role
              if (value !== 2) {
                console.log("Not doctor role, clearing department");
                newValues.department = undefined;
              }

              form.setFieldsValue(newValues);
            }}
            placeholder="Chọn vai trò"
            disabled={editingUser && getUserRoleId(editingUser) === 1}
          >
            {/* Chỉ hiển thị "Thành viên" khi đang edit user có role = 1 */}
            {editingUser && getUserRoleId(editingUser) === 1 && (
              <Option value={1}>Thành viên</Option>
            )}
            <Option value={2}>Bác sĩ</Option>
            <Option value={3}>Quản lý</Option>
            <Option value={4}>Quản trị viên</Option>
          </Select>
        </Form.Item>
        <Form.Item
          name="status"
          label="Trạng thái"
          rules={[{ required: true, message: "Chọn trạng thái" }]}
          extra={
            <span style={{ color: "#888", fontSize: 12 }}>
              Trạng thái "Đình chỉ hoạt động" sẽ ngăn người dùng đăng nhập và tự
              động đăng xuất nếu đang online.
            </span>
          }
        >
          <Select>
            {statusOptions.map((s) => (
              <Option key={s.value} value={s.value}>
                {s.label}
              </Option>
            ))}
          </Select>
        </Form.Item>
        {(() => {
          // Prioritize watchedRole (form value) over selectedRole (state)
          const currentRole =
            watchedRole !== undefined ? watchedRole : selectedRole;
          const isDoctorRole = currentRole === 2;
          return isDoctorRole;
        })() && (
          <Form.Item
            name="departmentId"
            label="Khoa"
            rules={[
              { required: true, message: "Vui lòng chọn khoa cho bác sĩ" },
            ]}
          >
            <Select showSearch placeholder="Chọn khoa">
              {Object.entries(DEPARTMENT_IDS).map(([name, id]) => (
                <Option key={id} value={Number(id)}>
                  {name}
                </Option>
              ))}
            </Select>
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};

export default UserFormModal;
