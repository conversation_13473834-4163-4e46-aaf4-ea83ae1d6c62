// Global toast instance - will be set by ToastProvider
let globalToastInstance = null;

/**
 * Set the global toast instance (called by ToastProvider)
 */
export const setGlobalToastInstance = (instance) => {
  globalToastInstance = instance;
};

/**
 * Enhanced toast utilities using React Bootstrap Toast
 */
export const toast = {
  /**
   * Show success toast
   * @param {string} content - Message content
   * @param {Object} options - Additional options
   */
  success: (content, options = {}) => {
    if (!globalToastInstance) {
      console.warn('Toast instance not available. Make sure ToastProvider is wrapped around your app.');
      return;
    }
    return globalToastInstance.success(content, options);
  },

  /**
   * Show error toast
   * @param {string} content - Message content
   * @param {Object} options - Additional options
   */
  error: (content, options = {}) => {
    if (!globalToastInstance) {
      console.warn('Toast instance not available. Make sure ToastProvider is wrapped around your app.');
      return;
    }
    return globalToastInstance.error(content, options);
  },

  /**
   * Show warning toast
   * @param {string} content - Message content
   * @param {Object} options - Additional options
   */
  warning: (content, options = {}) => {
    if (!globalToastInstance) {
      console.warn('Toast instance not available. Make sure ToastProvider is wrapped around your app.');
      return;
    }
    return globalToastInstance.warning(content, options);
  },

  /**
   * Show info toast
   * @param {string} content - Message content
   * @param {Object} options - Additional options
   */
  info: (content, options = {}) => {
    if (!globalToastInstance) {
      console.warn('Toast instance not available. Make sure ToastProvider is wrapped around your app.');
      return;
    }
    return globalToastInstance.info(content, options);
  },

  /**
   * Show loading toast
   * @param {string} content - Message content
   * @param {Object} options - Additional options
   */
  loading: (content, options = {}) => {
    if (!globalToastInstance) {
      console.warn('Toast instance not available. Make sure ToastProvider is wrapped around your app.');
      return;
    }
    return globalToastInstance.loading(content, options);
  },

  /**
   * Remove specific toast
   * @param {string} id - Toast ID
   */
  remove: (id) => {
    if (!globalToastInstance) {
      console.warn('Toast instance not available. Make sure ToastProvider is wrapped around your app.');
      return;
    }
    return globalToastInstance.removeToast(id);
  },

  /**
   * Destroy all toasts (for compatibility with Ant Design)
   */
  destroy: () => {
    // React Bootstrap toasts auto-hide, so this is mainly for compatibility
    console.info('Toast destroy called - React Bootstrap toasts auto-hide');
  },
};

/**
 * Predefined toast messages for common actions
 */
export const toastMessages = {
  // Success messages
  saveSuccess: () => toast.success('Lưu thành công!'),
  updateSuccess: () => toast.success('Cập nhật thành công!'),
  deleteSuccess: () => toast.success('Xóa thành công!'),
  createSuccess: () => toast.success('Tạo mới thành công!'),
  uploadSuccess: () => toast.success('Tải lên thành công!'),
  sendSuccess: () => toast.success('Gửi thành công!'),
  approveSuccess: () => toast.success('Phê duyệt thành công!'),
  rejectSuccess: () => toast.success('Từ chối thành công!'),

  // Error messages
  saveError: () => toast.error('Lưu thất bại! Vui lòng thử lại.'),
  updateError: () => toast.error('Cập nhật thất bại! Vui lòng thử lại.'),
  deleteError: () => toast.error('Xóa thất bại! Vui lòng thử lại.'),
  createError: () => toast.error('Tạo mới thất bại! Vui lòng thử lại.'),
  uploadError: () => toast.error('Tải lên thất bại! Vui lòng thử lại.'),
  sendError: () => toast.error('Gửi thất bại! Vui lòng thử lại.'),
  networkError: () => toast.error('Lỗi kết nối! Vui lòng kiểm tra mạng.'),
  serverError: () => toast.error('Lỗi máy chủ! Vui lòng thử lại sau.'),
  validationError: () => toast.error('Dữ liệu không hợp lệ! Vui lòng kiểm tra lại.'),
  permissionError: () => toast.error('Bạn không có quyền thực hiện hành động này!'),

  // Warning messages
  unsavedChanges: () => toast.warning('Bạn có thay đổi chưa được lưu!'),
  confirmDelete: () => toast.warning('Bạn có chắc chắn muốn xóa?'),
  dataNotFound: () => toast.warning('Không tìm thấy dữ liệu!'),
  duplicateData: () => toast.warning('Dữ liệu đã tồn tại!'),

  // Info messages
  loading: () => toast.loading('Đang xử lý...'),
  processing: () => toast.loading('Đang xử lý dữ liệu...'),
  uploading: () => toast.loading('Đang tải lên...'),
  downloading: () => toast.loading('Đang tải xuống...'),

  // Custom messages
  bloodRequestCreated: () => toast.success('Yêu cầu máu đã được tạo thành công!'),
  bloodRequestApproved: () => toast.success('Yêu cầu máu đã được phê duyệt!'),
  bloodRequestRejected: () => toast.warning('Yêu cầu máu đã bị từ chối!'),
  donationScheduled: () => toast.success('Lịch hiến máu đã được đặt thành công!'),
  profileUpdated: () => toast.success('Thông tin cá nhân đã được cập nhật!'),
  passwordChanged: () => toast.success('Mật khẩu đã được thay đổi thành công!'),
  emailVerified: () => toast.success('Email đã được xác thực thành công!'),
  notificationRead: () => toast.info('Đã đánh dấu thông báo đã đọc'),
  logoutSuccess: () => toast.info('Đăng xuất thành công!'),
  loginSuccess: () => toast.success('Đăng nhập thành công!'),
};

/**
 * Toast with promise handling
 * @param {Promise} promise - Promise to handle
 * @param {Object} messages - Success and error messages
 */
export const toastPromise = async (promise, messages = {}) => {
  const {
    loading: loadingMsg = 'Đang xử lý...',
    success: successMsg = 'Thành công!',
    error: errorMsg = 'Có lỗi xảy ra!',
  } = messages;

  const loadingToastId = toast.loading(loadingMsg);
  
  try {
    const result = await promise;
    if (loadingToastId) {
      toast.remove(loadingToastId);
    }
    toast.success(successMsg);
    return result;
  } catch (error) {
    if (loadingToastId) {
      toast.remove(loadingToastId);
    }
    toast.error(errorMsg);
    throw error;
  }
};

// Compatibility layer for existing Ant Design message usage
export const message = {
  success: (content, duration) => {
    if (typeof content === 'string') {
      toast.success(content, { duration: duration * 1000 });
    } else if (typeof content === 'object' && content.content) {
      toast.success(content.content, { duration: (content.duration || 4) * 1000 });
    }
  },
  error: (content, duration) => {
    if (typeof content === 'string') {
      toast.error(content, { duration: duration * 1000 });
    } else if (typeof content === 'object' && content.content) {
      toast.error(content.content, { duration: (content.duration || 5) * 1000 });
    }
  },
  warning: (content, duration) => {
    if (typeof content === 'string') {
      toast.warning(content, { duration: duration * 1000 });
    } else if (typeof content === 'object' && content.content) {
      toast.warning(content.content, { duration: (content.duration || 4) * 1000 });
    }
  },
  info: (content, duration) => {
    if (typeof content === 'string') {
      toast.info(content, { duration: duration * 1000 });
    } else if (typeof content === 'object' && content.content) {
      toast.info(content.content, { duration: (content.duration || 4) * 1000 });
    }
  },
  loading: (content, duration) => {
    if (typeof content === 'string') {
      return toast.loading(content, { duration: duration * 1000 });
    } else if (typeof content === 'object' && content.content) {
      return toast.loading(content.content, { duration: (content.duration || 0) * 1000 });
    }
  },
  destroy: () => toast.destroy(),
};

export default toast;
