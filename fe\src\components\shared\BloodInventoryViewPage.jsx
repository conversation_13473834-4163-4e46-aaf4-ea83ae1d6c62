import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Tabs,
  Spin,
} from "antd";
import { toast } from "../../utils/toastUtils";
import {
  getBloodComponentName,
  getBloodComponentId,
  mapRhTypeToSymbol,
  getInventoryStatus,
  BLOOD_GROUPS,
  RH_TYPES,
  COMPONENT_TYPES,
} from "../../constants/bloodInventoryConstants";
import BloodInventoryTable from "./BloodInventoryTable";
import {
  fetchBloodInventory,
  checkInBloodInventory,
  checkOutBloodInventory,
} from "../../services/bloodInventoryService";
import authService from "../../services/authService";
import { API_ROLES } from "../../constants/systemConstants";
import useBloodInventoryHistory from "../../hooks/useBloodInventoryHistory";
import ManagerBloodCheckInModal from "../manager/blood-inventory/ManagerBloodCheckInModal";
import ManagerBloodCheckOutModal from "../manager/blood-inventory/ManagerBloodCheckOutModal";
import ManagerBloodInventoryHistoryTable from "../manager/blood-inventory/ManagerBloodInventoryHistoryTable";
import ManagerBloodInventoryHistoryFilters from "../manager/blood-inventory/ManagerBloodInventoryHistoryFilters";
import ManagerBloodInventoryStats from "../manager/blood-inventory/ManagerBloodInventoryStats";

/**
 * Shared Blood Inventory View Component
 * Được sử dụng chung cho Doctor và Manager với logic phân quyền
 * 
 * Props:
 * - showManagerFeatures: boolean - Hiển thị các tính năng của Manager (tabs lịch sử, nút nhập/xuất)
 * - pageHeaderComponent: Component - Component header tùy chỉnh cho từng role
 * - layoutComponent: Component - Component layout wrapper cho từng role
 */
const BloodInventoryViewPage = ({ 
  showManagerFeatures = false, 
  pageHeaderComponent: PageHeaderComponent,
  layoutComponent: LayoutComponent 
}) => {
  // === STATE MANAGEMENT ===
  const [inventory, setInventory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("inventory");

  // Manager-specific states (chỉ sử dụng khi showManagerFeatures = true)
  const [showCheckInModal, setShowCheckInModal] = useState(false);
  const [showCheckOutModal, setShowCheckOutModal] = useState(false);
  const [checkInForm, setCheckInForm] = useState({
    bloodGroup: "",
    rhType: "",
    componentType: "",
    bagType: "",
    quantity: 0,
    notes: "",
  });
  const [checkOutForm, setCheckOutForm] = useState({
    bloodGroup: "",
    rhType: "",
    componentType: "",
    bagType: "",
    quantity: 0,
    notes: "",
  });
  const [loadingCheckIn, setLoadingCheckIn] = useState(false);
  const [loadingCheckOut, setLoadingCheckOut] = useState(false);

  // History hook (chỉ sử dụng khi showManagerFeatures = true)
  const historyHook = useBloodInventoryHistory();
  const {
    filters: historyFilters,
    setFilters: setHistoryFilters,
    filteredHistory,
    loading: historyLoading,
    performers,
    fetchHistory,
  } = showManagerFeatures ? historyHook : {
    filters: {},
    setFilters: () => {},
    filteredHistory: [],
    loading: false,
    performers: [],
    fetchHistory: () => {},
  };

  // === ROLE CHECKING ===
  const currentUser = authService.getCurrentUser();
  const isManager = currentUser?.role === API_ROLES.STAFF_BLOOD_MANAGER;
  const isBloodDepartmentDoctor =
    currentUser?.role === API_ROLES.STAFF_DOCTOR &&
    currentUser?.doctorType === "blood_department";

  // CHỈ MANAGER mới có quyền sử dụng manager features (tab lịch sử, nút nhập/xuất)
  // Doctor khoa huyết học và khoa khác đều chỉ xem
  const canUseManagerFeatures = showManagerFeatures && isManager;

  // === DATA LOADING ===
  const loadInventory = async () => {
    try {
      setLoading(true);
      const data = await fetchBloodInventory();
      const inventoryWithStatus = data.map((item) => {
        const bloodType = `${item.bloodGroup}${mapRhTypeToSymbol(item.rhType)}`;
        const status = getInventoryStatus(item.quantity);
        return {
          ...item,
          bloodType,
          status,
          componentType: getBloodComponentName(item.componentId),
          inventoryId: item.InventoryID || item.inventoryId,
          bagType: item.bagType || "250ml",
        };
      });
      setInventory(inventoryWithStatus);
    } catch (err) {
      console.error("Failed to load inventory:", err);
      toast.error("Không thể tải dữ liệu kho máu");
      setInventory([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadInventory();
  }, []);

  // === COMPUTED VALUES ===
  const totalUnits = inventory.reduce((sum, item) => sum + item.quantity, 0);
  const criticalItems = inventory.filter(item => item.status === "critical").length;
  const lowItems = inventory.filter(item => item.status === "low").length;
  const rareBloodUnits = inventory.filter(item => item.isRare).reduce((sum, item) => sum + item.quantity, 0);

  // === MANAGER FUNCTIONS (chỉ hoạt động khi có quyền) ===
  const handleCheckIn = async () => {
    if (!canUseManagerFeatures) {
      toast.error("Bạn không có quyền thực hiện thao tác này");
      return;
    }

    // Validation form trước khi gửi
    if (
      !checkInForm.bloodGroup ||
      !checkInForm.rhType ||
      !checkInForm.componentType ||
      !checkInForm.quantity ||
      checkInForm.quantity <= 0
    ) {
      toast.error("Vui lòng điền đầy đủ thông tin hợp lệ!");
      return;
    }

    setLoadingCheckIn(true);
    try {
      const userId = authService.getCurrentUser()?.id;

      if (!userId) {
        throw new Error("Không tìm thấy thông tin người dùng");
      }

      // Tạo payload đúng format API
      const payload = {
        bloodGroup: checkInForm.bloodGroup,
        rhType: checkInForm.rhType,
        componentId: getBloodComponentId(checkInForm.componentType),
        quantity: parseInt(checkInForm.quantity),
        bagType: checkInForm.bagType || "450ml",
        notes: checkInForm.notes || "",
        performedBy: parseInt(userId),
      };

      console.log("Check-in payload:", payload);

      await checkInBloodInventory(payload);

      // Cập nhật UI ngay lập tức sau khi thành công (như code cũ)
      setInventory((prev) =>
        prev.map((item) => {
          if (
            item.bloodGroup === checkInForm.bloodGroup &&
            item.rhType === checkInForm.rhType &&
            item.componentType === checkInForm.componentType
          ) {
            return {
              ...item,
              quantity: item.quantity + parseInt(checkInForm.quantity),
            };
          }
          return item;
        })
      );

      toast.success("Nhập kho thành công!");
      setShowCheckInModal(false);
      setCheckInForm({
        bloodGroup: "",
        rhType: "",
        componentType: "",
        bagType: "",
        quantity: 0,
        notes: "",
      });

      // Reload data để đồng bộ với server
      if (showManagerFeatures) {
        fetchHistory();
      }
      loadInventory();
    } catch (error) {
      console.error("Check-in error:", error);
      const errorMessage =
        error.response?.data?.message || error.message || "Lỗi không xác định";
      toast.error(`Nhập kho thất bại: ${errorMessage}`);
    } finally {
      setLoadingCheckIn(false);
    }
  };

  const handleCheckOut = async () => {
    if (!canUseManagerFeatures) {
      toast.error("Bạn không có quyền thực hiện thao tác này");
      return;
    }

    // Validation form trước khi gửi
    if (
      !checkOutForm.bloodGroup ||
      !checkOutForm.rhType ||
      !checkOutForm.componentType ||
      !checkOutForm.quantity ||
      checkOutForm.quantity <= 0
    ) {
      toast.error("Vui lòng điền đầy đủ thông tin hợp lệ!");
      return;
    }

    setLoadingCheckOut(true);
    try {
      const userId = authService.getCurrentUser()?.id;

      if (!userId) {
        throw new Error("Không tìm thấy thông tin người dùng");
      }

      // Kiểm tra số lượng tồn kho trước khi xuất (như code cũ)
      const selected = inventory.find(
        (item) =>
          item.bloodGroup === checkOutForm.bloodGroup &&
          item.rhType === checkOutForm.rhType &&
          item.componentType === checkOutForm.componentType
      );

      if (!selected) {
        toast.error(
          "Không tìm thấy kho máu phù hợp. Vui lòng kiểm tra lại thông tin!"
        );
        setLoadingCheckOut(false);
        return;
      }

      if (selected.quantity < parseInt(checkOutForm.quantity)) {
        toast.error(
          `Số lượng tồn kho (${selected.quantity} túi) không đủ để xuất ${checkOutForm.quantity} túi!`
        );
        setLoadingCheckOut(false);
        return;
      }

      // Tạo payload đúng format API
      const payload = {
        bloodGroup: checkOutForm.bloodGroup,
        rhType: checkOutForm.rhType,
        componentId: getBloodComponentId(checkOutForm.componentType),
        quantity: parseInt(checkOutForm.quantity),
        bagType: checkOutForm.bagType || "450ml",
        notes: checkOutForm.notes || "",
        performedBy: parseInt(userId),
      };

      console.log("Check-out payload:", payload);

      await checkOutBloodInventory(payload);

      // Cập nhật UI ngay lập tức sau khi thành công (như code cũ)
      setInventory((prev) =>
        prev.map((item) => {
          if (
            item.bloodGroup === checkOutForm.bloodGroup &&
            item.rhType === checkOutForm.rhType &&
            item.componentType === checkOutForm.componentType
          ) {
            return {
              ...item,
              quantity: Math.max(
                0,
                item.quantity - parseInt(checkOutForm.quantity)
              ),
            };
          }
          return item;
        })
      );

      toast.success("Xuất kho thành công!");
      setShowCheckOutModal(false);
      setCheckOutForm({
        bloodGroup: "",
        rhType: "",
        componentType: "",
        bagType: "",
        quantity: 0,
        notes: "",
      });

      // Reload data để đồng bộ với server
      if (showManagerFeatures) {
        fetchHistory();
      }
      loadInventory();
    } catch (error) {
      console.error("Check-out error:", error);
      const errorMessage =
        error.response?.data?.message || error.message || "Lỗi không xác định";
      toast.error(`Xuất kho thất bại: ${errorMessage}`);
    } finally {
      setLoadingCheckOut(false);
    }
  };

  // === RENDER FUNCTIONS ===
  const renderInventoryTab = () => (
    <>
      {/* Stats chỉ hiển thị cho Manager */}
      {canUseManagerFeatures && (
        <ManagerBloodInventoryStats
          totalUnits={totalUnits}
          criticalItems={criticalItems}
          lowItems={lowItems}
          rareBloodUnits={rareBloodUnits}
        />
      )}
      
      {/* Bảng kho máu */}
      <BloodInventoryTable
        data={inventory}
        showActions={false} // Không hiển thị actions cho tất cả role
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} của ${total} mục`,
        }}
        scroll={{ x: 800 }}
        loading={loading}
      />
    </>
  );

  const renderHistoryTab = () => (
    <Card>
      <ManagerBloodInventoryHistoryFilters
        filters={historyFilters}
        setFilters={setHistoryFilters}
        inventory={inventory}
        performers={performers}
      />
      <ManagerBloodInventoryHistoryTable
        data={filteredHistory}
        loading={historyLoading}
      />
    </Card>
  );

  const renderTabsAndActions = () => {
    if (!canUseManagerFeatures) {
      // Doctor chỉ xem inventory, không có tabs và actions
      return null;
    }

    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          marginBottom: 16,
          gap: 12,
        }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            { key: "inventory", label: "Kho máu" },
            { key: "history", label: "Lịch sử hoạt động" },
          ]}
          style={{ flex: 1 }}
        />
        {activeTab === "inventory" && (
          <>
            <Button
              type="primary"
              style={{ backgroundColor: "#52c41a", borderColor: "#52c41a" }}
              onClick={() => setShowCheckInModal(true)}
            >
              Nhập kho
            </Button>
            <Button
              type="primary"
              style={{ backgroundColor: "#D91022", borderColor: "#D91022" }}
              onClick={() => setShowCheckOutModal(true)}
            >
              Xuất kho
            </Button>
          </>
        )}
      </div>
    );
  };

  const renderContent = () => {
    if (canUseManagerFeatures) {
      // Manager có tabs
      return (
        <>
          {activeTab === "inventory" && renderInventoryTab()}
          {activeTab === "history" && renderHistoryTab()}
        </>
      );
    } else {
      // Doctor chỉ xem inventory
      return renderInventoryTab();
    }
  };

  // === MAIN RENDER ===
  return (
    <LayoutComponent>
      <div className="blood-inventory-view-page">
        {/* Page Header */}
        <PageHeaderComponent
          loadInventory={loadInventory}
          loading={loading}
        />

        {/* Tabs + Actions (chỉ cho Manager) */}
        {renderTabsAndActions()}

        {/* Main Content */}
        {loading ? (
          <div style={{ textAlign: "center", padding: "50px" }}>
            <Spin size="large" />
          </div>
        ) : (
          renderContent()
        )}

        {/* Modals (chỉ cho Manager) */}
        {canUseManagerFeatures && (
          <>
            <ManagerBloodCheckInModal
              open={showCheckInModal}
              onOk={handleCheckIn}
              onCancel={() => setShowCheckInModal(false)}
              confirmLoading={loadingCheckIn}
              inventory={inventory}
              form={checkInForm}
              setForm={setCheckInForm}
            />

            <ManagerBloodCheckOutModal
              open={showCheckOutModal}
              onOk={handleCheckOut}
              onCancel={() => setShowCheckOutModal(false)}
              confirmLoading={loadingCheckOut}
              inventory={inventory}
              form={checkOutForm}
              setForm={setCheckOutForm}
            />
          </>
        )}
      </div>
    </LayoutComponent>
  );
};

export default BloodInventoryViewPage;
