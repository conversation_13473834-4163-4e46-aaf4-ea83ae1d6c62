import React from "react";
import { Typography, Space, Button } from "antd";
import PropTypes from "prop-types";

const { Title, Text } = Typography;

const BloodRequestPageHeader = ({
  // Core props
  role = "doctor", // "admin", "doctor", "manager"
  activeTab = "internal", // "internal", "external"

  // Custom overrides
  customTitle,
  customDescription,

  // Standard props
  icon: IconComponent,
  actions = [],
  className = "",
  style = {},

  // Blood department specific
  isBloodDepartment = false,
}) => {
  // Get dynamic title based on role and tab
  const getTitle = () => {
    if (customTitle) return customTitle;

    if (role === "manager") {
      return isBloodDepartment && activeTab === "external"
        ? "Yêu cầu máu từ bên ngoài"
        : isBloodDepartment && activeTab === "internal"
        ? "Yêu cầu máu nội bộ"
        : "Quản lý Yêu cầu <PERSON>";
    }

    if (role === "doctor") {
      if (isBloodDepartment) {
        return activeTab === "external"
          ? "Yêu cầu máu từ bên ngoài"
          : "Yêu cầu máu nội bộ";
      }
      return "Yêu cầu Máu";
    }

    return "Quản lý Yêu cầu Máu";
  };

  // Get dynamic description based on role and tab
  const getDescription = () => {
    if (customDescription) return customDescription;

    if (role === "manager") {
      if (isBloodDepartment) {
        return activeTab === "external"
          ? "Xử lý các yêu cầu máu từ thành viên bên ngoài"
          : "Xử lý các yêu cầu máu từ bác sĩ khoa khác trong bệnh viện";
      }
      return "Xử lý và theo dõi tất cả yêu cầu máu từ bác sĩ và bệnh nhân";
    }

    if (role === "doctor") {
      if (isBloodDepartment) {
        return activeTab === "external"
          ? "Xử lý các yêu cầu máu từ thành viên bên ngoài"
          : "Xử lý các yêu cầu máu từ bác sĩ khoa khác trong bệnh viện";
      }
      return "Quản lý và theo dõi các yêu cầu máu";
    }

    return "Xử lý và theo dõi tất cả yêu cầu máu";
  };

  // Get CSS class based on role
  const getHeaderClass = () => {
    const baseClass = "blood-request-page-header";
    const roleClass = `${role}-page-header`;
    return `${baseClass} ${roleClass} ${className}`;
  };

  return (
    <div className={getHeaderClass()} style={style}>
      <div className="header-info">
        <div className="header-title-section">
          {IconComponent && (
            <div className="header-icon">
              <IconComponent />
            </div>
          )}
          <div className="header-text">
            <Title level={2} className="header-title">
              {getTitle()}
            </Title>
            <Text className="header-description">{getDescription()}</Text>
          </div>
        </div>
      </div>

      {actions.length > 0 && (
        <div className="header-actions">
          <Space size="middle" wrap>
            {actions.map((action, index) => (
              <Button
                key={index}
                type={action.type || "default"}
                icon={action.icon}
                onClick={action.onClick}
                loading={action.loading}
                disabled={action.disabled}
                size={action.size || "default"}
                className={action.className || ""}
                style={action.style || {}}
              >
                {action.label}
              </Button>
            ))}
          </Space>
        </div>
      )}
    </div>
  );
};

BloodRequestPageHeader.propTypes = {
  role: PropTypes.oneOf(["admin", "doctor", "manager"]),
  activeTab: PropTypes.oneOf(["internal", "external"]),
  customTitle: PropTypes.string,
  customDescription: PropTypes.string,
  icon: PropTypes.elementType,
  actions: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      onClick: PropTypes.func,
      type: PropTypes.string,
      icon: PropTypes.node,
      loading: PropTypes.bool,
      disabled: PropTypes.bool,
      size: PropTypes.string,
      className: PropTypes.string,
      style: PropTypes.object,
    })
  ),
  className: PropTypes.string,
  style: PropTypes.object,
  isBloodDepartment: PropTypes.bool,
};

export default BloodRequestPageHeader;
