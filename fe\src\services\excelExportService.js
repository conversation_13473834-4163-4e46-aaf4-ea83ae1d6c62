import * as XLSX from "xlsx";
import { getBloodComponentName } from "../constants/bloodInventoryConstants";

import * as newsService from "./newsService";

import * as bloodArticleService from "./bloodArticleService";


const excelExportService = {
  // Export Excel for Admin Dashboard - only users and blogs management
  exportAdminDashboardExcel: async (dashboardData) => {
    try {
      const workbook = XLSX.utils.book_new();

      // --- Sheet 1: Quản lý người dùng ---
      try {
        const userInfoService = (await import('./userInfoService')).default;
        let users = [];
        try {
          users = await userInfoService.getAllUsers();
        } catch (err) {
          console.warn('Không thể lấy danh sách người dùng:', err);
        }

        if (users.length > 0) {
          const userHeaders = [
            'ID', 'Tên', '<PERSON><PERSON>', '<PERSON><PERSON> trò', '<PERSON><PERSON><PERSON><PERSON> t<PERSON>h', '<PERSON>ổ<PERSON>', '<PERSON><PERSON><PERSON><PERSON> máu', 'Rh',
            'SĐT', 'Địa chỉ', 'Trạng thái', '<PERSON>ày tạo', 'Cập nhật lần cuối'
          ];

          const getRoleName = (user) => {
            const roleId = user.roleID || user.role;
            const departmentId = user.departmentId || user.departmentID;
            const departmentName = user.department;

            const roleMap = {
              1: 'Thành viên',
              2: 'Bác sĩ',
              3: 'Quản lý',
              4: 'Quản trị viên'
            };

            let roleName = roleMap[roleId] || 'Không xác định';

            // Nếu là bác sĩ, thêm thông tin khoa
            if (roleId === 2) {
              const departmentMap = {
                1: 'Khoa Huyết học',
                2: 'Khoa Tim mạch',
                3: 'Khoa Nhi',
                4: 'Khoa Cấp Cứu',
                5: 'Khoa Giải phẫu',
                6: 'Khoa Ngoại'
              };

              let department = '';
              if (departmentId && departmentMap[departmentId]) {
                department = departmentMap[departmentId];
              } else if (departmentName && departmentName.trim()) {
                department = departmentName.trim();
              }

              if (department) {
                roleName = `Bác sĩ - ${department}`;
              }
            }

            return roleName;
          };

          const getStatusText = (status) => {
            return status === 1 ? 'Hoạt động' : status === 0 ? 'Bị đình chỉ' : 'Không xác định';
          };

          const userData = users.map(user => [
            user.userID || user.id || user.userId || '',
            user.fullName || user.name || '',
            user.email || '',
            getRoleName(user),
            user.gender || '',
            user.age || '',
            user.bloodGroup || '',
            user.rhType || '',
            user.phoneNumber || user.phone || '',
            user.address || '',
            getStatusText(user.status),
            user.createdAt ? new Date(user.createdAt).toLocaleDateString('vi-VN') : '',
            user.updatedAt ? new Date(user.updatedAt).toLocaleDateString('vi-VN') : ''
          ]);

          const userSheet = XLSX.utils.aoa_to_sheet([
            userHeaders,
            ...userData
          ]);
          XLSX.utils.book_append_sheet(workbook, userSheet, 'Quản lý người dùng');
        }
      } catch (err) {
        console.error('Lỗi khi thêm sheet Quản lý người dùng:', err);
      }

      // --- Sheet 2: Quản lý bài viết (tương tự doctor) ---
      try {
        const bloodArticleService = (await import('./bloodArticleService'));
        const newsService = (await import('./newsService'));
        let articles = [];
        let blogs = [];
        try {
          articles = await bloodArticleService.getBloodArticles();
        } catch (err) {
          console.warn('Không thể lấy danh sách bài viết:', err);
        }
        try {
          blogs = await newsService.fetchAllNews();
        } catch (err) {
          console.warn('Không thể lấy danh sách blog:', err);
        }

        // Gộp dữ liệu và chuẩn hóa
        const { getArticleId, getArticleUserId } = await import('../utils/articleUtils');
        const { getNewsId, getNewsUserId } = await import('../utils/newsUtils');

        // Tạo userMap cho tên tác giả
        let userMap = {};
        try {
          const userIds = [
            ...new Set([
              ...articles.map(a => getArticleUserId(a)).filter(Boolean),
              ...blogs.map(b => getNewsUserId(b)).filter(Boolean)
            ])
          ];
          if (userIds.length > 0) {
            const userInfoService = (await import('./userInfoService')).default;
            userMap = await userInfoService.getUserNames(userIds);
          }
        } catch (err) {
          userMap = {};
        }

        const allPosts = [
          ...articles.map(a => ({
            type: 'Tài liệu',
            id: getArticleId(a) || '',
            title: a.title || '',
            author: userMap[getArticleUserId(a)] || a.authorName || a.userName || getArticleUserId(a) || '',
            created: a.createdAt || a.createdDate || '',
            tags: Array.isArray(a.tags)
              ? a.tags.map(tag => typeof tag === 'object' && tag.tagName ? tag.tagName : tag).join(', ')
              : (a.tags || ''),
          })),
          ...blogs.map(b => ({
            type: 'Tin tức',
            id: getNewsId(b) || '',
            title: b.title || '',
            author: userMap[getNewsUserId(b)] || b.authorName || b.userName || getNewsUserId(b) || '',
            created: b.createdAt || b.createdDate || b.postedAt || '',
            tags: Array.isArray(b.tags)
              ? b.tags.map(tag => typeof tag === 'object' && tag.tagName ? tag.tagName : tag).join(', ')
              : (b.tags || ''),
          }))
        ];

        if (allPosts.length > 0) {
          const postHeaders = [
            'Loại', 'ID', 'Tiêu đề', 'Tác giả', 'Ngày tạo', 'Tags'
          ];
          const postData = allPosts.map(p => [
            p.type,
            p.id,
            p.title,
            p.author,
            p.created,
            p.tags
          ]);
          const postSheet = XLSX.utils.aoa_to_sheet([
            postHeaders,
            ...postData
          ]);
          XLSX.utils.book_append_sheet(workbook, postSheet, 'Quản lý bài viết');
        }
      } catch (err) {
        console.error('Lỗi khi thêm sheet Quản lý bài viết:', err);
      }

      // --- Xuất file ---
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");
      const filename = `BaoCao_Admin_QuanTri_${timestamp}.xlsx`;
      XLSX.writeFile(workbook, filename);
      return { success: true, message: "Xuất báo cáo Excel thành công", filename };
    } catch (error) {
      console.error('Lỗi khi xuất Excel cho Admin:', error);
      return { success: false, error: error.message || "Có lỗi xảy ra khi xuất Excel" };
    }
  },

  exportDoctorDashboardExcel: async (dashboardData) => {
    // Tạo workbook trước
    const workbook = XLSX.utils.book_new();

    // --- Thêm sheet Quản lý bài viết (blog + article) ---
    try {
      const bloodArticleService = (await import('./bloodArticleService'));
      const newsService = (await import('./newsService'));
      let articles = [];
      let blogs = [];
      try {
        articles = await bloodArticleService.getBloodArticles();
      } catch (err) {
        console.warn('Không thể lấy danh sách bài viết:', err);
      }
      try {
        blogs = await newsService.fetchAllNews();
      } catch (err) {
        console.warn('Không thể lấy danh sách blog:', err);
      }
      // Gộp dữ liệu và chuẩn hóa
      const { getArticleId, getArticleUserId } = await import('../utils/articleUtils');
      const { getNewsId, getNewsUserId } = await import('../utils/newsUtils');
      // Tạo userMap cho tên tác giả
      let userMap = {};
      try {
        const userIds = [
          ...new Set([
            ...articles.map(a => getArticleUserId(a)).filter(Boolean),
            ...blogs.map(b => getNewsUserId(b)).filter(Boolean)
          ])
        ];
        if (userIds.length > 0) {
          const userInfoService = (await import('./userInfoService')).default;
          userMap = await userInfoService.getUserNames(userIds);
        }
      } catch (err) {
        userMap = {};
      }
      const allPosts = [
        ...articles.map(a => ({
          type: 'Article',
          id: getArticleId(a) || '',
          title: a.title || '',
          author: userMap[getArticleUserId(a)] || a.authorName || a.userName || getArticleUserId(a) || '',
          created: a.createdAt || a.createdDate || '',
          tags:
            Array.isArray(a.tags)
              ? a.tags.map(tag => typeof tag === 'object' && tag.tagName ? tag.tagName : tag).join(', ')
              : (a.tags || ''),
        })),
        ...blogs.map(b => ({
          type: 'Blog',
          id: getNewsId(b) || '',
          title: b.title || '',
          author: userMap[getNewsUserId(b)] || b.authorName || b.userName || getNewsUserId(b) || '',
          created: b.createdAt || b.createdDate || b.postedAt || '',
          tags:
            Array.isArray(b.tags)
              ? b.tags.map(tag => typeof tag === 'object' && tag.tagName ? tag.tagName : tag).join(', ')
              : (b.tags || ''),
        }))
      ];
      if (allPosts.length > 0) {
        const postHeaders = [
          'Loại', 'ID', 'Tiêu đề', 'Tác giả', 'Ngày tạo', 'Tags'
        ];
        const postData = allPosts.map(p => [
          p.type,
          p.id,
          p.title,
          p.author,
          p.created,
          p.tags
        ]);
        const postSheet = XLSX.utils.aoa_to_sheet([
          postHeaders,
          ...postData
        ]);
        XLSX.utils.book_append_sheet(workbook, postSheet, 'Quản lý bài viết');
      }
    } catch (err) {
      console.error('Lỗi khi thêm sheet Quản lý bài viết:', err);
    }

    // --- Các sheet giống manager ---
    excelExportService._appendManagerSheets(workbook, dashboardData);

    // --- Thêm sheet Người hiến máu từ appointment ---
    try {
      const bloodDonationService = (await import('./bloodDonationService')).default;
      let donors = [];
      try {
        const appointments = await bloodDonationService.getAllAppointments();
        donors = await Promise.all(
          appointments.map(async (appointment) => {
            const userId = appointment.UserId || appointment.userId || appointment.UserID;
            let userInfo = {};
            if (userId) {
              try {
                const userResponse = await bloodDonationService.getUserInfo(userId);
                userInfo = userResponse.data || userResponse;
              } catch (userError) {
                userInfo = {};
              }
            }
            // Chỉ lấy donor có bloodGroup và rhType
            if ((userInfo.bloodGroup || userInfo.BloodGroup) && (userInfo.rhType || userInfo.RhType)) {
              return {
                id: userId || '',
                name: userInfo.name || userInfo.fullName || '',
                gender: userInfo.gender || '',
                age: userInfo.age || '',
                bloodGroup: userInfo.bloodGroup || userInfo.BloodGroup || '',
                rhType: userInfo.rhType || userInfo.RhType || '',
                phone: userInfo.phone || '',
                email: userInfo.email || '',
                address: userInfo.address || ''
              };
            }
            return null;
          })
        );
        donors = donors.filter(d => d !== null);
      } catch (err) {
        console.warn('Không thể lấy danh sách donors:', err);
      }
      if (donors.length > 0) {
        const donorHeaders = [
          'ID', 'Tên', 'Giới tính', 'Tuổi', 'Nhóm máu', 'Rh', 'SĐT', 'Email', 'Địa chỉ'
        ];
        const donorData = donors.map(u => [
          u.id,
          u.name,
          u.gender,
          u.age,
          u.bloodGroup,
          u.rhType,
          u.phone,
          u.email,
          u.address
        ]);
        const donorSheet = XLSX.utils.aoa_to_sheet([
          donorHeaders,
          ...donorData
        ]);
        XLSX.utils.book_append_sheet(workbook, donorSheet, 'Người hiến máu');
      }
    } catch (err) {
      console.error('Lỗi khi thêm sheet Người hiến máu:', err);
    }

    // --- Xuất file ---
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");
    const filename = `BaoCao_Doctor_HienMau_${timestamp}.xlsx`;
    XLSX.writeFile(workbook, filename);
    return { success: true, message: "Xuất báo cáo Excel thành công", filename };
  },
  _appendManagerSheets: (workbook, dashboardData) => {
    // 2. Blood Inventory Sheet
    if (
      dashboardData.bloodInventory &&
      dashboardData.bloodInventory.length > 0
    ) {
      const inventoryHeaders = [
        "ID Kho",
        "Nhóm máu",
        "Rh",
        "Thành phần",
        "Số lượng",
        "Trạng thái",
        "Cập nhật lần cuối",
      ];

      const inventoryData = dashboardData.bloodInventory.map((item) => [
        item.inventoryID || item.inventoryId || "",
        item.bloodGroup || "",
        item.rhType || "",
        item.componentId ? getBloodComponentName(item.componentId) : (item.componentType || ""),
        item.quantity || 0,
        item.status || "",
        item.lastUpdated
          ? new Date(item.lastUpdated).toLocaleDateString("vi-VN")
          : "",
      ]);

      const inventorySheet = XLSX.utils.aoa_to_sheet([
        inventoryHeaders,
        ...inventoryData,
      ]);
      XLSX.utils.book_append_sheet(workbook, inventorySheet, "Kho máu");
    }

    // 3. Blood Group Distribution Sheet
    if (
      dashboardData.bloodGroupData &&
      dashboardData.bloodGroupData.length > 0
    ) {
      const bloodGroupHeaders = ["Nhóm máu", "Số lượng", "Tỷ lệ %"];
      const totalUnits = dashboardData.bloodGroupData.reduce(
        (sum, item) => sum + (item.value || 0),
        0
      );

      const bloodGroupData = dashboardData.bloodGroupData.map((item) => [
        item.name || "",
        item.value || 0,
        totalUnits > 0
          ? ((item.value / totalUnits) * 100).toFixed(2) + "%"
          : "0%",
      ]);

      const bloodGroupSheet = XLSX.utils.aoa_to_sheet([
        bloodGroupHeaders,
        ...bloodGroupData,
      ]);
      XLSX.utils.book_append_sheet(
        workbook,
        bloodGroupSheet,
        "Phân bố nhóm máu"
      );
    }

    // 4. Recent Requests Sheet
    if (
      dashboardData.recentRequests &&
      dashboardData.recentRequests.length > 0
    ) {
      const requestHeaders = [
        "ID Yêu cầu",
        "Tên bệnh nhân",
        "Nhóm máu",
        "Rh",
        "Số lượng",
        "Trạng thái",
        "Ngày tạo",
        "Bác sĩ",
        "Cơ sở y tế",
      ];

      const requestData = dashboardData.recentRequests
        .slice(0, 100)
        .map((request) => [
          request.requestId || request.requestID || "",
          request.patientName || "",
          request.bloodGroup || "",
          request.rhType || "",
          request.quantity || 0,
          excelExportService.getStatusText(request.status),
          request.createdTime
            ? new Date(request.createdTime).toLocaleDateString("vi-VN")
            : "",
          request.doctorName || "",
          request.facilityName || "",
        ]);

      const requestSheet = XLSX.utils.aoa_to_sheet([
        requestHeaders,
        ...requestData,
      ]);
      XLSX.utils.book_append_sheet(workbook, requestSheet, "Yêu cầu gần đây");
    }

    // 5. Critical Inventory Sheet
    if (
      dashboardData.criticalInventory &&
      dashboardData.criticalInventory.length > 0
    ) {
      const criticalHeaders = [
        "Nhóm máu",
        "Loại thành phần",
        "Số lượng",
        "Mức độ cảnh báo",
        "Ghi chú",
      ];

      const criticalData = dashboardData.criticalInventory.map((item) => [
        `${item.bloodGroup}${item.rhType}`,
        item.componentId ? getBloodComponentName(item.componentId) : (item.componentType || ""),
        item.quantity || 0,
        item.quantity <= 2 ? "Cực kỳ thiếu" : "Thiếu",
        item.quantity <= 2 ? "Cần bổ sung ngay lập tức" : "Cần bổ sung",
      ]);

      const criticalSheet = XLSX.utils.aoa_to_sheet([
        criticalHeaders,
        ...criticalData,
      ]);
      XLSX.utils.book_append_sheet(workbook, criticalSheet, "Kho máu thiếu");
    }
  },

  getStatusText: (status) => {
    const statusMap = {
      0: "Chờ duyệt",
      1: "Đã chấp nhận",
      2: "Hoàn thành",
      3: "Từ chối",
      4: "Đã xóa",
    };
    return statusMap[status] || "Không xác định";
  },

  exportBloodInventory: (inventoryData) => {
    try {
      const workbook = XLSX.utils.book_new();

      const headers = [
        "ID Kho",
        "Nhóm máu",
        "Rh",
        "Loại thành phần",
        "Số lượng",
        "Trạng thái",
        "Hiếm",
        "Cập nhật lần cuối",
      ];

      const data = inventoryData.map((item) => [
        item.inventoryID || item.inventoryId || "",
        item.bloodGroup || "",
        item.rhType || "",
        item.componentType || "",
        item.quantity || 0,
        item.status || "",
        item.isRare ? "Có" : "Không",
        item.lastUpdated
          ? new Date(item.lastUpdated).toLocaleDateString("vi-VN")
          : "",
      ]);

      const worksheet = XLSX.utils.aoa_to_sheet([headers, ...data]);
      XLSX.utils.book_append_sheet(workbook, worksheet, "Kho máu");

      const timestamp = new Date()
        .toISOString()
        .slice(0, 19)
        .replace(/:/g, "-");
      const filename = `KhoMau_${timestamp}.xlsx`;

      XLSX.writeFile(workbook, filename);

      return {
        success: true,
        message: "Xuất dữ liệu kho máu thành công",
        filename,
      };
    } catch (error) {
      console.error("Error exporting blood inventory:", error);
      return {
        success: false,
        error: error.message || "Có lỗi xảy ra khi xuất dữ liệu kho máu",
      };
    }
  },

  exportBloodRequests: (requestsData) => {
    try {
      const workbook = XLSX.utils.book_new();

      const headers = [
        "ID Yêu cầu",
        "Tên bệnh nhân",
        "Tuổi",
        "Giới tính",
        "Nhóm máu",
        "Rh",
        "Số lượng",
        "Lý do",
        "Trạng thái",
        "Ngày tạo",
        "Bác sĩ",
        "Số điện thoại",
        "Cơ sở y tế",
      ];

      const data = requestsData.map((request) => [
        request.requestId || request.requestID || "",
        request.patientName || "",
        request.age || "",
        request.gender || "",
        request.bloodGroup || "",
        request.rhType || "",
        request.quantity || 0,
        request.reason || "",
        excelExportService.getStatusText(request.status),
        request.createdTime
          ? new Date(request.createdTime).toLocaleDateString("vi-VN")
          : "",
        request.doctorName || "",
        request.doctorPhone || "",
        request.facilityName || "",
      ]);

      const worksheet = XLSX.utils.aoa_to_sheet([headers, ...data]);
      XLSX.utils.book_append_sheet(workbook, worksheet, "Yêu cầu máu");

      const timestamp = new Date()
        .toISOString()
        .slice(0, 19)
        .replace(/:/g, "-");
      const filename = `YeuCauMau_${timestamp}.xlsx`;

      XLSX.writeFile(workbook, filename);

      return {
        success: true,
        message: "Xuất dữ liệu yêu cầu máu thành công",
        filename,
      };
    } catch (error) {
      console.error("Error exporting blood requests:", error);
      return {
        success: false,
        error: error.message || "Có lỗi xảy ra khi xuất dữ liệu yêu cầu máu",
      };
    }
  },
};

export default excelExportService;
