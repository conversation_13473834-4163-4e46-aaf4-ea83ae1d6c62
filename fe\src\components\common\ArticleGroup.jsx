import React from "react";
import { Typography, Card, Pagination } from "antd";
import { Link } from "react-router-dom";
import { FaCalendarAlt } from "react-icons/fa";
import ArticleTags from "./ArticleTags";

const { Title, Paragraph } = Typography;

const ArticleGroup = ({ title, articles = [], gradient, isMember = false }) => {
  const [currentPage, setCurrentPage] = React.useState(1);
  const pageSize = 3;
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentArticles = articles.slice(startIndex, endIndex);
  const totalPages = Math.ceil(articles.length / pageSize);

  if (articles.length === 0) {
    return null;
  }

  return (
    <div className="article-group" style={{ "--header-gradient": gradient }}>
      <div className="group-header">
        <div className="group-header-content">
          <Title level={3} className="group-title">
            {title}
          </Title>
          <div className="group-count">{articles.length} bài viết</div>
        </div>
      </div>

      <div className="article-grid">
        <div className="article-list">
          {currentArticles.map((article) => (
            <Card
              key={article.id}
              className="article-card fade-in"
              hoverable
              cover={
                <div className="article-image-container">
                  <img
                    alt={article.title}
                    src={
                      article.imgUrl ||
                      article.imageUrl ||
                      "https://via.placeholder.com/400x200?text=Blood+Donation"
                    }
                  />
                </div>
              }
            >
              <Link
                to={
                  isMember
                    ? `/member/blood-info/${article.id}`
                    : `/blood-info/${article.id}`
                }
              >
                <div className="article-content">
                  <div className="article-meta">
                    <div className="article-date">
                      <FaCalendarAlt className="date-icon" style={{ color: '#1890ff' }} />
                      <span>
                        {new Date(
                          article.createdAt || article.postedAt || article.date
                        ).toLocaleDateString("vi-VN")}
                      </span>
                    </div>
                  </div>
                  <Title level={4} className="article-title">
                    {article.title || ""}
                  </Title>
                  {article.summary && (
                    <Paragraph className="article-summary">
                      {article.summary}
                    </Paragraph>
                  )}
                  <div className="article-description">
                    {(() => {
                      const content =
                        article.shortContent || article.description || "";
                      // If content contains HTML tags, render as HTML
                      if (content.includes("<") && content.includes(">")) {
                        return (
                          <div dangerouslySetInnerHTML={{ __html: content }} />
                        );
                      }
                      // Otherwise render as plain text
                      return content;
                    })()}
                  </div>
                  <div className="article-tags">
                    <ArticleTags tags={article.tags} />
                  </div>
                </div>
              </Link>
            </Card>
          ))}
        </div>

        {totalPages > 1 && (
          <div className="pagination-container">
            <Pagination
              current={currentPage}
              pageSize={pageSize}
              total={articles.length}
              onChange={setCurrentPage}
              showSizeChanger={false}
              className="article-pagination"
              showTotal={(total, range) =>
                `${range[0]}-${range[1]} của ${total} bài viết`
              }
              itemRender={(page, type, originalElement) => {
                if (type === "page") {
                  if (page <= 3 || page === totalPages) return originalElement;
                  if (page === 4 && totalPages > 4)
                    return <span key="ellipsis">...</span>;
                  return null;
                }
                return originalElement;
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ArticleGroup;
