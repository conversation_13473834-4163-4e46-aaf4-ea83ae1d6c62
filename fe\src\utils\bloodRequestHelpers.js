
export const formatBloodRequestData = (formData, currentUser) => {
  // Parse blood type to get bloodGroup and rhType
  const parseBloodType = (bloodType) => {
    if (!bloodType) return { bloodGroup: "", rhType: "" };

    // Extract ABO group (A, B, AB, O) and Rh factor (+, -)
    const match = bloodType.match(/^(A|B|AB|O)([+-])$/);
    if (match) {
      return {
        bloodGroup: match[1],
        rhType: match[2] === "+" ? "Rh+" : "Rh-",
      };
    }

    // Fallback
    return {
      bloodGroup: bloodType.replace(/[+-]/, ""),
      rhType: bloodType.includes("+") ? "Rh+" : "Rh-",
    };
  };

  const { bloodGroup, rhType } = parseBloodType(formData.bloodType);

  // Format according to API schema
  const apiData = {
    userID: parseInt(currentUser?.id) || 0,
    patientName: formData.patientName?.trim() || "",
    age: parseInt(formData.patientAge) || 0,
    gender: formData.patientGender || "",
    relationship:
      formData.patientRelation === "other"
        ? formData.patientRelationOther?.trim() || ""
        : getPatientRelationApiValue(formData.patientRelation),
    facilityName: formData.hospitalName?.trim() || "",
    doctorName: formData.doctorName?.trim() || "",
    doctorPhone: formData.doctorPhone?.trim() || "",
    bloodGroup: bloodGroup,
    rhType: rhType,
    componentId: parseInt(formData.componentId) || 1, // Default to "Toàn phần" if not specified
    quantity: parseInt(formData.quantity) || 0,
    reason: formData.medicalCondition?.trim() || "",
    MedicalReportUrl: formData.medicalReports, // Backend expects this field name
    status: 0, // 0 = pending
    createdTime: new Date().toISOString(),
  };

  return apiData;
};

/**
 * Convert form relation values to API values
 */
const getPatientRelationApiValue = (relation) => {
  const relationMap = {
    self: "Bản thân",
    family: "Gia đình",
    friend: "Bạn bè",
    doctor: "Bác sĩ phụ trách",
  };

  return relationMap[relation] || relation;
};


export const validateBloodRequestData = (formData) => {
  const errors = [];

  // Required fields validation
  if (!formData.bloodType) {
    errors.push("Vui lòng chọn nhóm máu");
  }

  if (!formData.quantity || formData.quantity <= 0) {
    errors.push("Vui lòng nhập số lượng máu hợp lệ");
  }

  if (!formData.patientName?.trim()) {
    errors.push("Vui lòng nhập tên bệnh nhân");
  }

  if (!formData.patientAge || formData.patientAge <= 0) {
    errors.push("Tuổi bệnh nhân phải lớn hơn 0");
  }

  if (!formData.patientGender) {
    errors.push("Vui lòng chọn giới tính");
  }

  if (!formData.patientRelation) {
    errors.push("Vui lòng chọn mối quan hệ với bệnh nhân");
  }

  if (
    formData.patientRelation === "other" &&
    !formData.patientRelationOther?.trim()
  ) {
    errors.push("Vui lòng nhập mối quan hệ cụ thể");
  }

  if (!formData.medicalCondition?.trim()) {
    errors.push("Vui lòng mô tả tình trạng bệnh lý");
  }

  if (!formData.hospitalName?.trim()) {
    errors.push("Vui lòng nhập tên bệnh viện/phòng khám");
  }

  if (!formData.doctorName?.trim()) {
    errors.push("Vui lòng nhập tên bác sĩ điều trị");
  }

  if (!formData.doctorPhone?.trim()) {
    errors.push("Vui lòng nhập số điện thoại bác sĩ");
  } else if (formData.doctorPhone.length !== 10) {
    errors.push("Số điện thoại bác sĩ phải đủ 10 số");
  }

  if (!formData.medicalReports?.trim()) {
    errors.push("Vui lòng nhập báo cáo y tế/chẩn đoán");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};


export const formatBloodRequestResponse = (apiResponse) => {
  if (!apiResponse.success) {
    return {
      status: "error",
      message: "GỬI YÊU CẦU THẤT BẠI",
      description: apiResponse.error || "Có lỗi xảy ra khi gửi yêu cầu máu",
    };
  }

  const data = apiResponse.data;
  return {
    status: "success",
    message: "GỬI YÊU CẦU THÀNH CÔNG",
    description:
      "Yêu cầu máu của bạn đã được gửi đến bác sĩ khoa Huyết học. Bạn sẽ nhận được phản hồi sớm.",
    requestId: data.requestId,
    data: {
      bloodType: `${data.bloodGroup}${data.rhType === "Positive" ? "+" : "-"}`,
      quantity: data.quantity,
      unit: "ml",
      patientName: data.patientName,
    },
  };
};


export const getBloodTypeDisplayName = (bloodType) => {
  const bloodTypeMap = {
    "O+": "O+",
    "O-": "O-",
    "A+": "A+",
    "A-": "A-",
    "B+": "B+",
    "B-": "B-",
    "AB+": "AB+",
    "AB-": "AB-",
  };

  return bloodTypeMap[bloodType] || bloodType;
};

export const getPatientRelationDisplayName = (relation, relationOther) => {
  const relationMap = {
    self: "Chính bản thân tôi",
    family: "Gia đình",
    friend: "Bạn bè",
    doctor: "Bác sĩ phụ trách",
    other: relationOther || "Khác",
  };

  return relationMap[relation] || relation;
};


export const getGenderDisplayName = (gender) => {
  const genderMap = {
    male: "Nam",
    female: "Nữ",
    other: "Khác",
  };

  return genderMap[gender] || gender;
};
