import { useState, useEffect } from "react";
import { useUserData } from "../contexts/UserDataContext";

/**
 * Hook for enhancing blood request data with user information for table display
 * Uses UserDataContext for optimized user data loading
 */
export const useBloodRequestTableData = (requests = []) => {
  const { getMultipleUsersData } = useUserData();
  const [enhancedRequests, setEnhancedRequests] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const enhanceRequestsWithUserData = async () => {
      if (!requests || requests.length === 0) {
        setEnhancedRequests([]);
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        // Extract unique user IDs from requests
        const userIds = [...new Set(
          requests
            .map(req => req.userID || req.userId || req.UserID)
            .filter(id => id && id !== 0)
        )];

        if (userIds.length === 0) {
          // No user IDs to fetch, return original requests
          setEnhancedRequests(requests);
          setLoading(false);
          return;
        }

        // Batch fetch user data
        const usersData = await getMultipleUsersData(userIds);
        
        // Enhance requests with user data
        const enhanced = requests.map(request => {
          const userId = String(request.userID || request.userId || request.UserID || '');
          const userData = usersData.get(userId);
          
          // Determine if this is a member request
          const isMemberRequest = [
            "Gia đình", "gia đình", "Bản thân", "bạn bè", 
            "Chính bản thân tôi", "Other"
          ].includes(request.relationship);
          
          // Enhanced request object
          const enhancedRequest = {
            ...request,
            // Add user data
            userData: userData?.data || null,
            userDepartment: userData?.department || null,
            userLoading: userData?.loading || false,
            userError: userData?.error || null,
            
            // Add computed fields for table display
            requesterName: isMemberRequest 
              ? (userData?.data?.name || userData?.data?.fullName || "Thành viên")
              : (request.doctorName || "Bác sĩ"),
            
            department: isMemberRequest 
              ? null  // Don't show department for member requests
              : (userData?.department || "Không xác định"),
            
            requesterType: isMemberRequest ? "member" : "doctor",
            
            // Enhanced display fields
            displayName: isMemberRequest
              ? `${userData?.data?.name || userData?.data?.fullName || "Thành viên"} (Thành viên)`
              : `${request.doctorName || "Bác sĩ"} (${userData?.department || "Không xác định"})`,
          };
          
          return enhancedRequest;
        });

        setEnhancedRequests(enhanced);
      } catch (err) {
        console.error("Error enhancing requests with user data:", err);
        setError(err.message);
        // Fallback to original requests
        setEnhancedRequests(requests);
      } finally {
        setLoading(false);
      }
    };

    enhanceRequestsWithUserData();
  }, [requests, getMultipleUsersData]);

  return {
    enhancedRequests,
    loading,
    error,
    // Helper functions
    getRequesterInfo: (request) => ({
      name: request.requesterName,
      department: request.department,
      type: request.requesterType,
      displayName: request.displayName
    }),
    
    // Check if any requests are still loading user data
    hasLoadingUsers: enhancedRequests.some(req => req.userLoading),
    
    // Get requests by type
    getMemberRequests: () => enhancedRequests.filter(req => req.requesterType === "member"),
    getDoctorRequests: () => enhancedRequests.filter(req => req.requesterType === "doctor"),
  };
};

/**
 * Hook for single blood request user data enhancement
 */
export const useBloodRequestUserData = (request) => {
  const { getUserData } = useUserData();
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadUserData = async () => {
      if (!request) {
        setUserData(null);
        setLoading(false);
        return;
      }

      const userId = request.userID || request.userId || request.UserID;
      if (!userId || userId === 0) {
        setUserData(null);
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const result = await getUserData(userId);
        setUserData(result);
      } catch (err) {
        console.error("Error loading user data for request:", err);
        setError(err.message);
        setUserData(null);
      } finally {
        setLoading(false);
      }
    };

    loadUserData();
  }, [request, getUserData]);

  // Determine request type
  const isMemberRequest = request && [
    "Gia đình", "gia đình", "Bản thân", "bạn bè", 
    "Chính bản thân tôi", "Other"
  ].includes(request.relationship);

  return {
    userData: userData?.data || null,
    department: userData?.department || null,
    loading: loading || userData?.loading,
    error: error || userData?.error,
    
    // Computed display values
    requesterName: isMemberRequest 
      ? (userData?.data?.name || userData?.data?.fullName || "Thành viên")
      : (request?.doctorName || "Bác sĩ"),
    
    requesterType: isMemberRequest ? "member" : "doctor",
    
    shouldShowDepartment: !isMemberRequest,
    
    displayName: isMemberRequest
      ? `${userData?.data?.name || userData?.data?.fullName || "Thành viên"} (Thành viên)`
      : `${request?.doctorName || "Bác sĩ"} (${userData?.department || "Không xác định"})`,
  };
};

export default useBloodRequestTableData;
