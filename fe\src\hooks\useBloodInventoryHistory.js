import { useState, useEffect, useMemo, useCallback } from "react";
import axios from "axios";
import { getBloodComponentName } from "../constants/bloodInventoryConstants";
import dayjs from "dayjs";

export default function useBloodInventoryHistory() {
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [inventoryData, setInventoryData] = useState([]);
  const [filters, setFilters] = useState({
    dateRange: null,
    performedBy: "",
    bloodType: "",
  });

  // Fetch inventory data to map inventoryID to componentType
  const fetchInventory = useCallback(() => {
    return axios
      .get("https://localhost:7021/api/BloodInventory")
      .then((res) => {
        setInventoryData(res.data);
        return res.data;
      })
      .catch((error) => {
        setInventoryData([]);
        return [];
      });
  }, []);

  const fetchHistory = useCallback(() => {
    setLoading(true);

    // First fetch inventory data, then fetch history
    fetchInventory().then((inventory) => {
      axios
        .get("https://localhost:7021/api/BloodInventory/history/all")
        .then((res) => {
          // Create inventory lookup map
          const inventoryMap = new Map();
          inventory.forEach((item) => {
            inventoryMap.set(item.inventoryID, {
              componentType: item.componentType,
              componentId: item.componentId,
            });
          });

          // Map inventoryID to componentType for display
          const mappedData = res.data.map((item) => {
            let componentType = "Không xác định"; // Default fallback

            // Try to get componentType from inventory lookup
            if (item.inventoryID && inventoryMap.has(item.inventoryID)) {
              const inventoryItem = inventoryMap.get(item.inventoryID);
              if (inventoryItem.componentType) {
                componentType = inventoryItem.componentType;
              } else if (inventoryItem.componentId) {
                componentType = getBloodComponentName(
                  inventoryItem.componentId
                );
              }
            }
            // Fallback: try direct componentId if available
            else if (item.componentId) {
              componentType = getBloodComponentName(item.componentId);
            }
            // Fallback: try direct componentType if available
            else if (item.componentType) {
              componentType = item.componentType;
            }

            return {
              ...item,
              componentType: componentType,
            };
          });

          setHistory(mappedData);
        })
        .catch(() => setHistory([]))
        .finally(() => setLoading(false));
    });
  }, [fetchInventory]);

  useEffect(() => {
    fetchHistory();
  }, [fetchHistory]);

  // Danh sách performer duy nhất
  const performers = useMemo(() => {
    const map = new Map();
    history.forEach((h) => {
      if (h.performedBy) {
        const id = h.performedBy.userId || h.performedBy.id;
        if (id && !map.has(id)) {
          map.set(id, { id, name: h.performedBy.name || id });
        }
      }
    });
    return Array.from(map.values());
  }, [history]);

  // Danh sách bloodType duy nhất (cho filter nhóm máu)
  const inventory = useMemo(() => {
    const map = new Map();
    history.forEach((h) => {
      const bloodType = `${h.bloodGroup}${h.rhType === "Rh+" ? "+" : "-"}`;
      if (!map.has(bloodType)) {
        map.set(bloodType, {
          bloodType,
          bloodGroup: h.bloodGroup,
          rhType: h.rhType,
          componentType: h.componentType,
        });
      }
    });
    return Array.from(map.values());
  }, [history]);

  // Lọc dữ liệu
  const filteredHistory = useMemo(
    () =>
      history.filter((item) => {
        let pass = true;
        if (filters.dateRange && filters.dateRange.length === 2) {
          const [start, end] = filters.dateRange;
          const performedAt = dayjs(item.performedAt);
          if (
            performedAt.isBefore(start.startOf("day")) ||
            performedAt.isAfter(end.endOf("day"))
          )
            pass = false;
        }
        if (filters.performedBy) {
          const id = String(
            item.performedBy?.userId || item.performedBy?.id || ""
          );
          if (id !== String(filters.performedBy)) pass = false;
        }
        if (filters.bloodType) {
          const type = `${item.bloodGroup}${item.rhType === "Rh+" ? "+" : "-"}`;
          if (type !== filters.bloodType) pass = false;
        }
        return pass;
      }),
    [history, filters]
  );

  return {
    history,
    loading,
    filters,
    setFilters,
    filteredHistory,
    performers,
    inventory,
    fetchHistory,
    setHistory,
  };
}
