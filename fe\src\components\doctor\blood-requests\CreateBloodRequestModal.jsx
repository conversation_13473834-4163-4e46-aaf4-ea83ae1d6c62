import React, { useState } from "react";
import { BLOOD_GROUPS, RH_TYPES } from "../../../constants/systemConstants";
import { BLOOD_COMPONENT_MAP } from "../../../constants/bloodInventoryConstants";
import usePatientAutoFill from "../../../hooks/usePatientAutoFill";
import { FaUser, FaHeart, FaStethoscope, FaTimes, FaSpinner } from "react-icons/fa";

/**
 * Modal component for creating blood requests
 * Only shown for non-hematology doctors
 */
const CreateBloodRequestModal = ({
  showCreateModal,
  loading,
  newRequest,
  setNewRequest,
  handleCreateRequest,
  closeCreateModal,
  isBloodDepartment,
}) => {
  const [patientIdInput, setPatientIdInput] = useState("");
  const {
    loading: patientLoading,
    patientData,
    error: patientError,
    fillPatientInfo,
  } = usePatientAutoFill();

  if (!showCreateModal || isBloodDepartment) {
    return null;
  }

  // Handle patient ID change and auto-fill
  const handlePatientIdChange = async (e) => {
    const patientId = e.target.value;
    setPatientIdInput(patientId);

    // Update the patientID in newRequest
    setNewRequest((prev) => ({
      ...prev,
      patientID: parseInt(patientId) || 0,
    }));

    // Auto-fill if patientId is valid
    if (patientId && patientId.trim() !== "") {
      const patientInfo = await fillPatientInfo(patientId);
      if (patientInfo) {
        // Auto-fill form fields
        setNewRequest((prev) => ({
          ...prev,
          patientName: patientInfo.patientName || prev.patientName,
          age: patientInfo.patientAge || prev.age,
          gender: patientInfo.patientGender || prev.gender,
          bloodGroup: patientInfo.bloodGroup || prev.bloodGroup,
          rhType: patientInfo.rhType || prev.rhType,
          // Add other fields as needed
        }));
      }
    }
  };

  return (
    <div className="modal-overlay" onClick={closeCreateModal}>
      <div className="modal-content modern-modal" onClick={(e) => e.stopPropagation()}>
        {/* Modern Header */}
        <div className="modal-header modern-header">
          <div className="header-content">
            <div className="header-icon">
              <FaHeart />
            </div>
            <div className="header-text">
              <h2 className="header-title">Tạo yêu cầu máu mới</h2>
              <p className="header-description">Điền thông tin chi tiết để tạo yêu cầu máu</p>
            </div>
          </div>
          <button className="close-btn modern-close" onClick={closeCreateModal}>
            <FaTimes />
          </button>
        </div>

        <div className="modal-body modern-body">
          {/* Patient Information Section */}
          <div className="form-section modern-section">
            <div className="section-title">
              <div className="section-icon">
                <FaUser />
              </div>
              <h3>Thông tin bệnh nhân</h3>
            </div>
            <div className="form-grid">
              <div className="form-group-row">
                <div className="form-group">
                  <label className="form-label">
                    Mã bệnh nhân (tùy chọn)
                  </label>
                  <input
                    type="number"
                    className={`form-input ${patientLoading ? 'loading' : ''}`}
                    value={patientIdInput}
                    onChange={handlePatientIdChange}
                    placeholder="Nhập mã bệnh nhân"
                    disabled={patientLoading}
                  />
                  {patientLoading && (
                    <div className="input-status loading">
                      <FaSpinner className="spin" /> Đang tải thông tin bệnh nhân...
                    </div>
                  )}
                  {patientError && (
                    <div className="input-status error">
                      {patientError}
                    </div>
                  )}
                </div>
                <div className="form-group">
                  <label className="form-label">
                    Tên bệnh nhân <span className="required">*</span>
                  </label>
                  <input
                    type="text"
                    className="form-input"
                    value={newRequest.patientName}
                    onChange={(e) =>
                      setNewRequest((prev) => ({
                        ...prev,
                        patientName: e.target.value,
                      }))
                    }
                    placeholder="Nhập tên bệnh nhân..."
                  />
                </div>
              </div>

              <div className="form-group-row">
                <div className="form-group">
                  <label className="form-label">
                    Tuổi <span className="required">*</span>
                  </label>
                  <input
                    type="number"
                    className="form-input"
                    value={newRequest.age}
                    onChange={(e) =>
                      setNewRequest((prev) => ({
                        ...prev,
                        age: parseInt(e.target.value) || 0,
                      }))
                    }
                    min="0"
                    max="120"
                    placeholder="Nhập tuổi..."
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">
                    Giới tính <span className="required">*</span>
                  </label>
                  <select
                    className="form-select"
                    value={newRequest.gender}
                    onChange={(e) =>
                      setNewRequest((prev) => ({
                        ...prev,
                        gender: e.target.value,
                      }))
                    }
                  >
                    <option value="">Chọn giới tính</option>
                    <option value="Nam">Nam</option>
                    <option value="Nữ">Nữ</option>
                    <option value="Khác">Khác</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Blood Information Section */}
          <div className="form-section modern-section">
            <div className="section-title">
              <div className="section-icon">
                <FaHeart />
              </div>
              <h3>Thông tin máu cần thiết</h3>
            </div>
            <div className="form-grid">
              <div className="form-group-row">
                <div className="form-group">
                  <label className="form-label">
                    Nhóm máu <span className="required">*</span>
                  </label>
                  <select
                    className="form-select"
                    value={newRequest.bloodGroup}
                    onChange={(e) =>
                      setNewRequest((prev) => ({
                        ...prev,
                        bloodGroup: e.target.value,
                      }))
                    }
                  >
                    <option value="">Chọn nhóm máu</option>
                    {Object.values(BLOOD_GROUPS).map((group) => (
                      <option key={group} value={group}>
                        {group}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="form-group">
                  <label className="form-label">
                    Rh <span className="required">*</span>
                  </label>
                  <select
                    className="form-select"
                    value={newRequest.rhType}
                    onChange={(e) =>
                      setNewRequest((prev) => ({
                        ...prev,
                        rhType: e.target.value,
                      }))
                    }
                  >
                    <option value="">Chọn Rh</option>
                    {Object.values(RH_TYPES).map((rh) => (
                      <option key={rh} value={rh}>
                        {rh}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="form-group-row">
                <div className="form-group flex-2">
                  <label className="form-label">
                    Thành phần máu <span className="required">*</span>
                  </label>
                  <select
                    className="form-select"
                    value={newRequest.componentId || ""}
                    onChange={(e) =>
                      setNewRequest((prev) => ({
                        ...prev,
                        componentId: parseInt(e.target.value) || null,
                      }))
                    }
                  >
                    <option value="">Chọn thành phần máu</option>
                    {Object.entries(BLOOD_COMPONENT_MAP).map(([id, name]) => (
                      <option key={id} value={id}>
                        {name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="form-group-row quantity-row">
                <div className="form-group flex-2">
                  <label className="form-label">
                    Số lượng <span className="required">*</span>
                  </label>
                  <input
                    type="number"
                    className="form-input"
                    value={newRequest.quantity}
                    onChange={(e) =>
                      setNewRequest((prev) => ({
                        ...prev,
                        quantity: parseInt(e.target.value) || 1,
                      }))
                    }
                    min="1"
                    placeholder="Nhập số lượng..."
                  />
                </div>
                <div className="form-group flex-1">
                  <label className="form-label">Đơn vị</label>
                  <input
                    type="text"
                    className="form-input unit-input"
                    value="ml"
                    readOnly
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Medical Reason Section */}
          <div className="form-section modern-section">
            <div className="section-title">
              <div className="section-icon">
                <FaStethoscope />
              </div>
              <h3>Lý do y tế</h3>
            </div>
            <div className="form-grid">
              <div className="form-group">
                <label className="form-label">
                  Lý do yêu cầu máu <span className="required">*</span>
                </label>
                <textarea
                  className="form-textarea"
                  value={newRequest.reason}
                  onChange={(e) =>
                    setNewRequest((prev) => ({
                      ...prev,
                      reason: e.target.value,
                    }))
                  }
                  placeholder="Mô tả lý do cần máu cho bệnh nhân..."
                  rows="4"
                />
              </div>
            </div>
          </div>

          <div className="auto-approve-info modern-info">

            <div className="info-text">
              Yêu cầu này sẽ được tự động duyệt với trạng thái "Hợp lệ"
            </div>
          </div>

          <div className="form-actions modern-actions">
            <div className="actions-left">
              <button
                className="btn-back"
                onClick={closeCreateModal}
                disabled={loading}
              >
                Hủy
              </button>
            </div>
            <div className="actions-right">
              <button
                className="btn-submit"
                onClick={handleCreateRequest}
                disabled={
                  loading ||
                  !newRequest.patientName ||
                  !newRequest.age ||
                  !newRequest.gender ||
                  !newRequest.bloodGroup ||
                  !newRequest.rhType ||
                  !newRequest.componentId ||
                  !newRequest.quantity ||
                  !newRequest.reason
                }
              >
                {loading ? (
                  <>
                    <FaSpinner className="spin" /> Đang tạo...
                  </>
                ) : (
                  "Tạo yêu cầu máu"
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateBloodRequestModal;
