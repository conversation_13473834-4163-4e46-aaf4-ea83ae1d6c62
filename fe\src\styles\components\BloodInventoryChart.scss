.blood-inventory-chart {
  .chart-loading {
    text-align: center;
    padding: 40px 20px;
    
    p {
      margin-top: 16px;
      color: #666;
    }
  }

  .retry-button {
    background: #1890ff;
    color: white;
    border: none;
    padding: 4px 12px;
    border-radius: 4px;
    cursor: pointer;
    
    &:hover {
      background: #40a9ff;
    }
  }

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h3 {
      margin: 0;
      color: #262626;
      font-size: 18px;
      font-weight: 600;
    }

    .chart-controls {
      display: flex;
      align-items: center;

      .ant-radio-group {
        .ant-radio-button-wrapper {
          border-radius: 6px;

          &:first-child {
            border-radius: 6px 0 0 6px;
          }

          &:last-child {
            border-radius: 0 6px 6px 0;
          }
        }
      }
    }
  }

  .chart-content {
    margin-bottom: 20px;
    
    .recharts-wrapper {
      .recharts-pie-label-text {
        font-size: 12px;
        font-weight: 600;
      }
      
      .recharts-legend-wrapper {
        .recharts-legend-item {
          margin-right: 10px;
          font-size: 12px;
        }
      }
    }
  }

  .custom-tooltip {
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 8px 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

    .tooltip-label {
      margin: 0 0 4px 0;
      font-weight: 600;
      color: #262626;
    }

    .tooltip-bags, .tooltip-volume {
      margin: 0 0 2px 0;
      color: #666;
      font-size: 12px;
    }

    .tooltip-rare {
      margin: 2px 0 0 0;
      color: #ff4d4f;
      font-size: 11px;
      font-weight: 600;
    }
  }

  .chart-summary {
    display: flex;
    justify-content: space-around;
    padding: 16px;
    background: #f5f5f5;
    border-radius: 8px;
    
    .summary-item {
      text-align: center;
      
      .summary-label {
        display: block;
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }
      
      .summary-value {
        display: block;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .chart-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
      
      h3 {
        font-size: 16px;
      }
    }
    
    .chart-summary {
      flex-direction: column;
      gap: 12px;
      
      .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .summary-label {
          margin-bottom: 0;
        }
      }
    }
  }

  @media (max-width: 576px) {
    .chart-header h3 {
      font-size: 14px;
    }
    
    .chart-summary {
      padding: 12px;
      
      .summary-item {
        .summary-label {
          font-size: 11px;
        }
        
        .summary-value {
          font-size: 14px;
        }
      }
    }
  }
}
