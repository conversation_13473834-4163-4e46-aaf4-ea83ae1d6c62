// StatusBadge.scss - Modern, professional status badge styling

.status-badge {
  display: inline-flex !important; // Override Antd
  align-items: center;
  justify-content: center;
  border-radius: 50px !important; // Làm tròn hoàn toà<PERSON>, override Antd
  font-weight: 600;
  font-size: 12px !important; // Override Antd
  line-height: 1.4;
  padding: 8px 16px !important; // Padding cố định, override Antd
  border: 1px solid transparent !important; // Override Antd
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important; // Giảm shadow, override Antd
  backdrop-filter: blur(2px); // Giảm blur từ 8px xuống 2px
  position: relative;
  overflow: hidden;
  min-width: 100px; // Tăng min-width để có kích thước c<PERSON> định
  max-width: 120px; // Thêm max-width để không quá rộng
  text-align: center; // Căn gi<PERSON>a text
  margin: 0 !important; // Override Antd margin

  // Subtle gradient overlay
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
    pointer-events: none;
    border-radius: inherit;
  }

  // Content layout
  .status-badge-content {
    display: flex;
    align-items: center;
    gap: 6px;
    position: relative;
    z-index: 1;
  }

  .status-badge-icon {
    display: flex;
    align-items: center;
    font-size: 12px;

    .anticon {
      font-size: inherit;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    }
  }

  .status-badge-text {
    font-size: inherit;
    font-weight: inherit;
    white-space: nowrap;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    letter-spacing: 0.02em;
  }
  
  // Size variations
  &.status-badge-small {
    font-size: 11px;
    padding: 6px 12px;
    border-radius: 50px; // Tròn hoàn toàn
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04); // Giảm shadow
    min-width: 85px; // Tăng min-width cho kích thước cố định
    max-width: 100px; // Thêm max-width

    .status-badge-content {
      gap: 4px;
    }

    .status-badge-icon {
      font-size: 10px;
    }
  }

  &.status-badge-large {
    font-size: 14px;
    padding: 10px 20px;
    border-radius: 50px; // Tròn hoàn toàn
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06); // Giảm shadow
    min-width: 120px; // Tăng min-width cho kích thước cố định
    max-width: 140px; // Thêm max-width

    .status-badge-content {
      gap: 8px;
    }

    .status-badge-icon {
      font-size: 14px;
    }
  }

  // Hover effects
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);

    &::before {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
    }
  }

  // Active state
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }
  
  // Status-specific styling with modern gradients
  &.status-pending {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 50%, #ffe082 100%) !important; // Override Antd
    border-color: #ffc107 !important; // Override Antd
    color: #e65100 !important; // Override Antd
    box-shadow: 0 1px 3px rgba(255, 193, 7, 0.15) !important; // Giảm shadow, override Antd

    &:hover {
      background: linear-gradient(135deg, #fff3c4 0%, #ffe082 50%, #ffd54f 100%) !important;
      box-shadow: 0 2px 6px rgba(255, 193, 7, 0.2) !important; // Giảm shadow hover
    }
  }

  &.status-approved {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c8 50%, #a5d6a7 100%) !important; // Override Antd
    border-color: #4caf50 !important; // Override Antd
    color: #1b5e20 !important; // Override Antd
    box-shadow: 0 1px 3px rgba(76, 175, 80, 0.15) !important; // Giảm shadow, override Antd

    &:hover {
      background: linear-gradient(135deg, #c8e6c8 0%, #a5d6a7 50%, #81c784 100%) !important;
      box-shadow: 0 2px 6px rgba(76, 175, 80, 0.2) !important; // Giảm shadow hover
    }
  }

  &.status-completed {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%) !important; // Override Antd
    border-color: #2196f3 !important; // Override Antd
    color: #0d47a1 !important; // Override Antd
    box-shadow: 0 1px 3px rgba(33, 150, 243, 0.15) !important; // Giảm shadow, override Antd

    &:hover {
      background: linear-gradient(135deg, #bbdefb 0%, #90caf9 50%, #64b5f6 100%) !important;
      box-shadow: 0 2px 6px rgba(33, 150, 243, 0.2) !important; // Giảm shadow hover
    }
  }

  &.status-rejected {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 50%, #ef9a9a 100%) !important; // Override Antd
    border-color: #f44336 !important; // Override Antd
    color: #b71c1c !important; // Override Antd
    box-shadow: 0 1px 3px rgba(244, 67, 54, 0.15) !important; // Giảm shadow, override Antd

    &:hover {
      background: linear-gradient(135deg, #ffcdd2 0%, #ef9a9a 50%, #e57373 100%) !important;
      box-shadow: 0 2px 6px rgba(244, 67, 54, 0.2) !important; // Giảm shadow hover
    }
  }

  &.status-cancelled {
    background: linear-gradient(135deg, #fafafa 0%, #e0e0e0 50%, #bdbdbd 100%);
    border-color: #757575;
    color: #424242;
    box-shadow: 0 2px 8px rgba(117, 117, 117, 0.25);

    &:hover {
      background: linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 50%, #9e9e9e 100%);
      box-shadow: 0 4px 16px rgba(117, 117, 117, 0.35);
    }
  }

  &.status-processing {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
    border-color: #2196f3;
    color: #0d47a1;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.25);

    &:hover {
      background: linear-gradient(135deg, #bbdefb 0%, #90caf9 50%, #64b5f6 100%);
      box-shadow: 0 4px 16px rgba(33, 150, 243, 0.35);
    }
  }

  &.status-fulfilled {
    background: linear-gradient(135deg, #f3e5f5 0%, #ce93d8 50%, #ba68c8 100%);
    border-color: #9c27b0;
    color: #4a148c;
    box-shadow: 0 2px 8px rgba(156, 39, 176, 0.25);

    &:hover {
      background: linear-gradient(135deg, #ce93d8 0%, #ba68c8 50%, #ab47bc 100%);
      box-shadow: 0 4px 16px rgba(156, 39, 176, 0.35);
    }
  }
  
  // Urgency levels
  &.urgency-normal {
    background: linear-gradient(135deg, #f6ffed, #b7eb8f);
    border-color: #52c41a;
    color: #389e0d;
  }
  
  &.urgency-urgent {
    background: linear-gradient(135deg, #fff7e6, #ffd591);
    border-color: #fa8c16;
    color: #d46b08;
    
    .status-badge-icon {
      animation: pulse 1.5s infinite;
    }
  }
  
  &.urgency-critical {
    background: linear-gradient(135deg, #fff2f0, #ffccc7);
    border-color: #ff4d4f;
    color: #cf1322;
    
    .status-badge-icon {
      animation: pulse 1s infinite;
    }
  }
  
  // Unknown status
  &.status-unknown {
    background: linear-gradient(135deg, #fafafa, #e6e6e6);
    border-color: #d9d9d9;
    color: #8c8c8c;
  }
  
  // Active/Inactive states
  &.status-active {
    background: linear-gradient(135deg, #f6ffed, #b7eb8f);
    border-color: #52c41a;
    color: #389e0d;
  }
  
  &.status-inactive {
    background: linear-gradient(135deg, #f5f5f5, #d9d9d9);
    border-color: #8c8c8c;
    color: #595959;
  }
  
  &.status-warning {
    background: linear-gradient(135deg, #fff7e6, #ffd591);
    border-color: #fa8c16;
    color: #d46b08;
  }
}

// Pulse animation for urgent statuses
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// Dark theme support
.dark-theme {
  .status-badge {
    &.status-pending {
      background: linear-gradient(135deg, #2c2416, #3c3020);
      border-color: #d48806;
      color: #fadb14;
    }
    
    &.status-approved,
    &.status-completed {
      background: linear-gradient(135deg, #162312, #1f2f1c);
      border-color: #389e0d;
      color: #52c41a;
    }
    
    &.status-rejected {
      background: linear-gradient(135deg, #2a1215, #3a1a1d);
      border-color: #cf1322;
      color: #ff4d4f;
    }
    
    &.status-cancelled {
      background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
      border-color: #595959;
      color: #8c8c8c;
    }
    
    &.status-processing {
      background: linear-gradient(135deg, #111b26, #1c2b3a);
      border-color: #0958d9;
      color: #1890ff;
    }
    
    &.status-fulfilled {
      background: linear-gradient(135deg, #1a0d26, #2a1a3a);
      border-color: #531dab;
      color: #722ed1;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .status-badge {
    font-size: 11px;
    padding: 3px 10px;
    
    &.status-badge-large {
      font-size: 13px;
      padding: 5px 14px;
    }
  }
}
