import React from "react";
import { ReloadOutlined, TeamOutlined } from "@ant-design/icons";
import DoctorLayout from "../../components/doctor/DoctorLayout";
import BloodRequestPageHeader from "../../components/shared/BloodRequestPageHeader";
import {
  DonorStatistics,
  DonorFilters,
  DonorTable,
  DonorUpdateModal,
  DonorStatusModal,
} from "../../components/doctor/donor-management";
import { useDoctorDonorManagement } from "../../hooks/useDoctorDonorManagement";
import { useDoctorDonorActions } from "../../hooks/useDoctorDonorActions";
import "../../styles/pages/DoctorDonorManagementPage.scss";
import "../../styles/components/BloodRequestPageHeader.scss";

const DoctorDonorManagementPage = () => {
  // Use custom hooks for state management and actions
  const {
    loading,
    filters,
    selectedDonor,
    showUpdateModal,
    showStatusModal,
    updateData,
    statusUpdateData,
    currentUser,
    isBloodDepartment,
    filteredDonors,
    statistics,
    setFilters,
    setShowUpdateModal,
    setShowStatusModal,
    setUpdateData,
    setStatusUpdateData,
    setSelectedDonor,
    loadDonors,
    refreshData,
    handleUpdateDonor,
    handleUpdateStatus,
    handleDeleteAppointment,
  } = useDoctorDonorManagement();

  const { handleSaveUpdate, handleSaveStatusUpdate } = useDoctorDonorActions();

  // Handle save update wrapper
  const onSaveUpdate = () => {
    handleSaveUpdate(
      selectedDonor,
      updateData,
      currentUser,
      () => {
        // Reload data after update
        loadDonors();
      },
      setShowUpdateModal,
      setSelectedDonor
    );
  };

  // Handle save status update wrapper
  const onSaveStatusUpdate = () => {
    handleSaveStatusUpdate(
      selectedDonor,
      statusUpdateData,
      currentUser,
      () => {
        // Reload data after update
        loadDonors();
      },
      setShowStatusModal,
      setSelectedDonor
    );
  };

  // Check access permission
  if (!isBloodDepartment) {
    return (
      <DoctorLayout>
        <div className="access-denied">
          <div className="access-denied-content">
            <h2>Không có quyền truy cập</h2>
            <p>Chỉ bác sĩ khoa Huyết học mới có thể truy cập trang này.</p>
          </div>
        </div>
      </DoctorLayout>
    );
  }

  return (
    <DoctorLayout>
      <div className="doctor-blood-requests-content">
        <BloodRequestPageHeader
          role="doctor"
          customTitle="Quản lý người hiến máu"
          customDescription="Quản lý thông tin và trạng thái của người hiến máu"
          icon={TeamOutlined}
          actions={[
            {
              label: "Làm mới",
              icon: <ReloadOutlined />,
              onClick: refreshData,
              loading: loading,
            },
          ]}
          isBloodDepartment={isBloodDepartment}
        />

        {/* Statistics */}
        <DonorStatistics statistics={statistics} loading={loading} />

        {/* Filters */}
        <DonorFilters
          filters={filters}
          setFilters={setFilters}
          statistics={statistics}
          loading={loading}
          onRefresh={refreshData}
        />
        
        {/* Table */}
        <div className="requests-section">
          <div className="requests-table-container">
            <DonorTable
              donors={filteredDonors}
              loading={loading}
              onUpdateDonor={handleUpdateDonor}
              onUpdateStatus={handleUpdateStatus}
              onDeleteAppointment={handleDeleteAppointment}
            />
          </div>
        </div>

        {/* Update Modal */}
        <DonorUpdateModal
          visible={showUpdateModal}
          onCancel={() => setShowUpdateModal(false)}
          onSave={onSaveUpdate}
          selectedDonor={selectedDonor}
          updateData={updateData}
          setUpdateData={setUpdateData}
        />

        {/* Status Modal */}
        <DonorStatusModal
          visible={showStatusModal}
          onCancel={() => setShowStatusModal(false)}
          onSave={onSaveStatusUpdate}
          selectedDonor={selectedDonor}
          statusUpdateData={statusUpdateData}
          setStatusUpdateData={setStatusUpdateData}
        />
      </div>
    </DoctorLayout>
  );
};

export default DoctorDonorManagementPage;
