import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Row, Col, DatePicker, Input } from "antd";
import {
  ReloadOutlined,
  CalendarOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import DoctorLayout from "../../components/doctor/DoctorLayout";
import BloodRequestPageHeader from "../../components/shared/BloodRequestPageHeader";
import BloodRequestDetailModal from "../../components/shared/BloodRequestDetailModal";
import DoctorBloodRequestsTable from "../../components/doctor/blood-requests/DoctorBloodRequestsTable";
import CreateBloodRequestModal from "../../components/doctor/blood-requests/CreateBloodRequestModal";
import BloodRequestStats from "../../components/doctor/blood-requests/BloodRequestStats";
import { useBloodRequestManagement } from "../../hooks/useBloodRequestManagement";
import { useBloodRequestCreation } from "../../hooks/useBloodRequestCreation";
import { useBloodRequestPageHeader } from "../../hooks/useBloodRequestPageHeader";
const { RangePicker } = DatePicker;
const { Search } = Input;
import "../../styles/pages/DoctorBloodRequestsPage.scss";
import "../../styles/components/BloodRequestPageHeader.scss";

/**
 * Optimized Doctor Blood Requests Page
 * Refactored with custom hooks and smaller components
 */
const DoctorBloodRequestsPage = () => {
  // Use custom hooks for state management
  const {
    requests,
    externalRequests,
    loading,
    activeTab,
    setActiveTab,
    isBloodDepartment,
    currentUser,
    loadBloodRequests,
    handleUpdateRequest,
    handleApproveExternal,
    handleRejectExternal,
    // handleCompleteRequest, // Removed - no longer needed for hematology doctors
  } = useBloodRequestManagement();

  const {
    showCreateModal,
    loading: createLoading,
    newRequest,
    setNewRequest,
    handleCreateRequest,
    openCreateModal,
    closeCreateModal,
  } = useBloodRequestCreation(loadBloodRequests);

  // Use shared header hook
  const { getTabItems, shouldShowTabs } = useBloodRequestPageHeader({
    role: "doctor",
    activeTab,
    internalRequests: requests,
    externalRequests,
    isBloodDepartment,
    currentUser,
    // Don't pass action handlers to hook, handle in component
    loading,
    createLoading,
  });

  // Local state for UI
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [dateRange, setDateRange] = useState(null);
  const [searchText, setSearchText] = useState("");

  // Helper functions
  const handleViewDetails = (request) => {
    setSelectedRequest(request);
    setShowDetailModal(true);
  };

  // Generate action buttons for doctor modal based on request status and user role
  const getDoctorModalActions = () => {
    if (!selectedRequest || !isBloodDepartment) return [];

    const actions = [];

    // Only show approve/reject buttons for external requests with pending status
    if (activeTab === "external" && selectedRequest.status === 0) {
      actions.push(
        <Button
          key="approve"
          type="primary"
          onClick={() => {
            handleApproveExternal(selectedRequest.requestId || selectedRequest.requestID);
            setShowDetailModal(false);
          }}
          style={{ backgroundColor: "#52c41a", borderColor: "#52c41a" }}
        >
          Chấp nhận
        </Button>
      );

      actions.push(
        <Button
          key="reject"
          danger
          onClick={() => {
            handleRejectExternal(selectedRequest.requestId || selectedRequest.requestID);
            setShowDetailModal(false);
          }}
        >
          Từ chối
        </Button>
      );
    }

    return actions;
  };

  // Filter requests based on date range only
  const currentRequests =
    isBloodDepartment && activeTab === "external" ? externalRequests : requests;

  const filteredRequests = currentRequests.filter((request) => {
    // Date range filter
    if (dateRange && dateRange.length === 2) {
      const requestDate = new Date(request.createdTime);
      const startDate = dateRange[0].startOf('day').toDate();
      const endDate = dateRange[1].endOf('day').toDate();

      if (requestDate < startDate || requestDate > endDate) {
        return false;
      }
    }

    // Search text filter (only for hematology department)
    if (isBloodDepartment && searchText.trim()) {
      const searchLower = searchText.toLowerCase().trim();
      const doctorName = request.doctorInfo?.name?.toLowerCase() || '';
      const patientName = request.patientInfo?.name?.toLowerCase() || '';

      if (!doctorName.includes(searchLower) && !patientName.includes(searchLower)) {
        return false;
      }
    }

    return true;
  });

  // Get page actions with doctor-specific logic
  const doctorActions = [
    {
      label: "Làm mới",
      icon: <ReloadOutlined />,
      onClick: loadBloodRequests,
      loading: loading,
    },
  ];

  // Add create button for non-blood department doctors
  if (!isBloodDepartment) {
    doctorActions.push({
      label: "Tạo yêu cầu mới",
      type: "primary",
      icon: <PlusOutlined />,
      onClick: openCreateModal,
      loading: createLoading,
    });
  }

  return (
    <DoctorLayout>
      <div className="doctor-blood-requests-content">
        {/* Shared Page Header */}
        <BloodRequestPageHeader
          role="doctor"
          activeTab={activeTab}
          icon={CalendarOutlined}
          actions={doctorActions}
          isBloodDepartment={isBloodDepartment}
        />

        {/* Tabs for blood department */}
        {shouldShowTabs && (
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={getTabItems}
            style={{ marginBottom: 16 }}
          />
        )}

        {/* Statistics */}
        <BloodRequestStats
          requests={requests}
          externalRequests={externalRequests}
          activeTab={activeTab}
          isBloodDepartment={isBloodDepartment}
        />

        {/* Filters */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={12} md={8}>
              <div style={{ marginBottom: 8 }}>
                <label style={{ fontWeight: "bold", color: "#20374E" }}>
                  Lọc theo ngày tạo:
                </label>
              </div>
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                style={{ width: "100%" }}
                format="DD/MM/YYYY"
                placeholder={["Từ ngày", "Đến ngày"]}
              />
            </Col>
            {isBloodDepartment && (
              <Col xs={24} sm={12} md={8}>
                <div style={{ marginBottom: 8 }}>
                  <label style={{ fontWeight: "bold", color: "#20374E" }}>
                    Tìm kiếm người yêu cầu/bác sĩ:
                  </label>
                </div>
                <Search
                  placeholder="Nhập tên để tìm kiếm..."
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  style={{ width: "100%" }}
                  allowClear
                />
              </Col>
            )}
            <Col xs={24} sm={12} md={4}>
              <div style={{ marginBottom: 8 }}>
                <label>&nbsp;</label>
              </div>
              <Button
                onClick={() => {
                  setDateRange(null);
                  setSearchText("");
                }}
                style={{ width: "100%" }}
              >
                Xóa bộ lọc
              </Button>
            </Col>
          </Row>
        </Card>

        {/* Table with built-in filters and sorting */}
        <DoctorBloodRequestsTable
          data={filteredRequests}
          loading={loading}
          onViewDetails={handleViewDetails}
          onApprove={handleApproveExternal}
          onReject={handleRejectExternal}
          isBloodDepartment={isBloodDepartment}
          activeTab={activeTab}
        />

        {/* Detail Modal */}
        {showDetailModal && selectedRequest && (
          <BloodRequestDetailModal
            isOpen={showDetailModal}
            onClose={() => setShowDetailModal(false)}
            request={selectedRequest}
            role="doctor"
            actions={getDoctorModalActions()}
          />
        )}

        {/* Create Request Modal - Only for non-hematology doctors */}
        <CreateBloodRequestModal
          showCreateModal={showCreateModal}
          loading={createLoading}
          newRequest={newRequest}
          setNewRequest={setNewRequest}
          handleCreateRequest={handleCreateRequest}
          closeCreateModal={closeCreateModal}
          isBloodDepartment={isBloodDepartment}
        />
      </div>
    </DoctorLayout>
  );
};

export default DoctorBloodRequestsPage;
