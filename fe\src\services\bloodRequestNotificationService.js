import axiosInstance from "./axiosInstance";
import config from "../config/environment";
import authService from "./authService";

const NOTIFICATION_API = config.api.notification;

/**
 * Blood Request Notification Service
 * Handles notifications specifically for blood request status changes
 */
export const bloodRequestNotificationService = {
  
  createBloodRequestNotification: async ({ userId, requestId, status, updatedBy }) => {
    const statusMessages = {
      0: "Yêu cầu máu của bạn đang được xem xét",
      1: "Yêu cầu máu của bạn đã được chấp nhận",
      2: "Yêu cầu máu của bạn đã hoàn thành - máu đã sẵn sàng",
      3: "Yêu cầu máu của bạn đã bị từ chối",
      4: "Yêu cầu máu của bạn đã bị hủy",
    };

    // Try different data formats to match API expectations
    const notificationDataFormats = [
      // Format 1: PascalCase (C# style)
      {
        UserId: parseInt(userId),
        Title: "Cập nhật yêu cầu máu",
        Message: statusMessages[status] || "Trạng thái yêu cầu máu đã được cập nhật",
        Type: "blood_request_update",
        RelatedId: parseInt(requestId),
        IsRead: false,
        CreatedBy: updatedBy,
        SentAt: new Date().toISOString(),
      },
      // Format 2: camelCase (JavaScript style)
      {
        userId: parseInt(userId),
        title: "Cập nhật yêu cầu máu",
        message: statusMessages[status] || "Trạng thái yêu cầu máu đã được cập nhật",
        type: "blood_request_update",
        relatedId: parseInt(requestId),
        isRead: false,
        createdBy: updatedBy,
        sentAt: new Date().toISOString(),
      },
      // Format 3: Mixed case (common in APIs)
      {
        userId: parseInt(userId),
        Title: "Cập nhật yêu cầu máu",
        Message: statusMessages[status] || "Trạng thái yêu cầu máu đã được cập nhật",
        Type: "blood_request_update",
        RelatedId: parseInt(requestId),
        IsRead: false,
        CreatedBy: updatedBy,
        SentAt: new Date().toISOString(),
      }
    ];

    const notificationData = notificationDataFormats[0];

    try {
      const response = await axiosInstance.post(NOTIFICATION_API, notificationData);

      return {
        success: true,
        data: response.data,
        message: "Thông báo đã được tạo thành công",
      };
    } catch (error) {
      if (error.response?.status === 500 && error.response?.data) {
        if (error.response.data.notificationId || error.response.data.userId) {
          return {
            success: true,
            data: error.response.data,
            message: "Thông báo đã được tạo thành công",
          };
        }
      }

      return {
        success: false,
        error: error.response?.data?.message || error.message || "Không thể tạo thông báo",
        details: error.response?.data,
        status: error.response?.status,
      };
    }
  },

  
  createBloodRequestSubmissionNotification: async ({ userId, requestId }) => {
    const notificationData = {
      userId: parseInt(userId),
      title: "Yêu cầu máu đã được gửi",
      message: "Yêu cầu máu của bạn đã được gửi thành công. Chúng tôi sẽ xem xét và phản hồi sớm nhất có thể.",
      type: "blood_request_submission",
      relatedId: parseInt(requestId),
      isRead: false,
      createdBy: "system",
      sentAt: new Date().toISOString(),
    };

    try {
      const response = await axiosInstance.post(NOTIFICATION_API, notificationData);
      
      return {
        success: true,
        data: response.data,
        message: "Thông báo đã được tạo thành công",
      };
    } catch (error) {
      
      
      return {
        success: false,
        error: error.response?.data?.message || error.message || "Không thể tạo thông báo",
        details: error.response?.data,
        status: error.response?.status,
      };
    }
  },

  
  testNotificationCreation: async (userId) => {
    
    const testData = {
      userId: parseInt(userId),
      title: "Test Notification",
      message: "This is a test notification",
      type: "test",
      isRead: false,
      createdBy: "test",
      sentAt: new Date().toISOString(),
    };

    try {
      const response = await axiosInstance.post(NOTIFICATION_API, testData);
      
      return { success: true, data: response.data };
    } catch (error) {
      
      return { 
        success: false, 
        error: error.response?.data || error.message,
        status: error.response?.status 
      };
    }
  },
};

export default bloodRequestNotificationService;
