/**
 * Security Service - Handles security settings and login attempt tracking
 */

class SecurityService {
  constructor() {
    this.LOGIN_ATTEMPTS_KEY = 'login_attempts';
    this.BLOCKED_USERS_KEY = 'blocked_users';
    this.SECURITY_SETTINGS_KEY = 'security_settings';
    
    // Default security settings
    this.defaultSettings = {
      sessionTimeout: 30,
      passwordMinLength: 6,
      maxLoginAttempts: 5,
      requireStrongPassword: true,
      blockDuration: 15 // minutes
    };
    
    this.loadSettings();
  }

  /**
   * Load security settings from localStorage
   */
  loadSettings() {
    try {
      const saved = localStorage.getItem(this.SECURITY_SETTINGS_KEY);
      this.settings = saved ? JSON.parse(saved) : this.defaultSettings;
    } catch (error) {
      console.error('Error loading security settings:', error);
      this.settings = this.defaultSettings;
    }
  }

  /**
   * Save security settings to localStorage
   * @param {Object} newSettings - New security settings
   */
  saveSettings(newSettings) {
    try {
      this.settings = { ...this.settings, ...newSettings };
      localStorage.setItem(this.SECURITY_SETTINGS_KEY, JSON.stringify(this.settings));
      return { success: true };
    } catch (error) {
      console.error('Error saving security settings:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get current security settings
   * @returns {Object} Current security settings
   */
  getSettings() {
    return this.settings;
  }

  /**
   * Track login attempt for a user
   * @param {string} email - User email
   * @param {boolean} success - Whether login was successful
   * @returns {Object} Result with attempt info
   */
  trackLoginAttempt(email, success) {
    try {
      const attempts = this.getLoginAttempts();
      const now = new Date().getTime();
      
      if (!attempts[email]) {
        attempts[email] = {
          count: 0,
          lastAttempt: now,
          blocked: false,
          blockedUntil: null
        };
      }

      const userAttempts = attempts[email];

      // Check if user is currently blocked
      if (userAttempts.blocked && userAttempts.blockedUntil > now) {
        const remainingTime = Math.ceil((userAttempts.blockedUntil - now) / (1000 * 60));
        return {
          success: false,
          blocked: true,
          remainingTime,
          message: `Tài khoản bị khóa tạm thời. Thử lại sau ${remainingTime} phút.`
        };
      }

      if (success) {
        // Reset attempts on successful login
        delete attempts[email];
        this.saveLoginAttempts(attempts);
        return { success: true, message: 'Đăng nhập thành công' };
      } else {
        // Increment failed attempts
        userAttempts.count++;
        userAttempts.lastAttempt = now;

        if (userAttempts.count >= this.settings.maxLoginAttempts) {
          // Block user
          userAttempts.blocked = true;
          userAttempts.blockedUntil = now + (this.settings.blockDuration * 60 * 1000);
          
          this.saveLoginAttempts(attempts);
          
          return {
            success: false,
            blocked: true,
            remainingTime: this.settings.blockDuration,
            message: `Quá nhiều lần đăng nhập sai. Tài khoản bị khóa ${this.settings.blockDuration} phút.`
          };
        } else {
          this.saveLoginAttempts(attempts);
          const remaining = this.settings.maxLoginAttempts - userAttempts.count;
          return {
            success: false,
            blocked: false,
            attemptsRemaining: remaining,
            message: `Đăng nhập thất bại. Còn ${remaining} lần thử.`
          };
        }
      }
    } catch (error) {
      console.error('Error tracking login attempt:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get login attempts from localStorage
   * @returns {Object} Login attempts data
   */
  getLoginAttempts() {
    try {
      const attempts = localStorage.getItem(this.LOGIN_ATTEMPTS_KEY);
      return attempts ? JSON.parse(attempts) : {};
    } catch (error) {
      console.error('Error getting login attempts:', error);
      return {};
    }
  }

  /**
   * Save login attempts to localStorage
   * @param {Object} attempts - Login attempts data
   */
  saveLoginAttempts(attempts) {
    try {
      localStorage.setItem(this.LOGIN_ATTEMPTS_KEY, JSON.stringify(attempts));
    } catch (error) {
      console.error('Error saving login attempts:', error);
    }
  }

  /**
   * Check if user is currently blocked
   * @param {string} email - User email
   * @returns {Object} Block status
   */
  isUserBlocked(email) {
    const attempts = this.getLoginAttempts();
    const userAttempts = attempts[email];
    
    if (!userAttempts || !userAttempts.blocked) {
      return { blocked: false };
    }

    const now = new Date().getTime();
    if (userAttempts.blockedUntil > now) {
      const remainingTime = Math.ceil((userAttempts.blockedUntil - now) / (1000 * 60));
      return { 
        blocked: true, 
        remainingTime,
        message: `Tài khoản bị khóa. Thử lại sau ${remainingTime} phút.`
      };
    } else {
      // Block expired, remove it
      delete attempts[email];
      this.saveLoginAttempts(attempts);
      return { blocked: false };
    }
  }

  /**
   * Manually unblock a user (admin function)
   * @param {string} email - User email
   * @returns {Object} Result
   */
  unblockUser(email) {
    try {
      const attempts = this.getLoginAttempts();
      if (attempts[email]) {
        delete attempts[email];
        this.saveLoginAttempts(attempts);
      }
      return { success: true, message: 'Đã mở khóa tài khoản' };
    } catch (error) {
      console.error('Error unblocking user:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get all blocked users (admin function)
   * @returns {Array} List of blocked users
   */
  getBlockedUsers() {
    try {
      const attempts = this.getLoginAttempts();
      const now = new Date().getTime();
      const blocked = [];

      Object.entries(attempts).forEach(([email, data]) => {
        if (data.blocked && data.blockedUntil > now) {
          const remainingTime = Math.ceil((data.blockedUntil - now) / (1000 * 60));
          blocked.push({
            email,
            attempts: data.count,
            blockedAt: new Date(data.lastAttempt).toLocaleString('vi-VN'),
            remainingTime
          });
        }
      });

      return blocked;
    } catch (error) {
      console.error('Error getting blocked users:', error);
      return [];
    }
  }

  /**
   * Clear all login attempts (admin function)
   * @returns {Object} Result
   */
  clearAllAttempts() {
    try {
      localStorage.removeItem(this.LOGIN_ATTEMPTS_KEY);
      return { success: true, message: 'Đã xóa tất cả lịch sử đăng nhập' };
    } catch (error) {
      console.error('Error clearing attempts:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Validate password according to current settings
   * @param {string} password - Password to validate
   * @returns {Object} Validation result
   */
  validatePassword(password) {
    const minLength = password.length >= this.settings.passwordMinLength;
    const hasLowerCase = /[a-z]/.test(password);
    const hasUpperCase = /[A-Z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    
    const isValid = minLength && hasLowerCase && hasUpperCase && hasNumbers && hasSpecialChar;
    
    return {
      isValid,
      minLength,
      hasLowerCase,
      hasUpperCase,
      hasNumbers,
      hasSpecialChar,
      requirements: {
        minLength: this.settings.passwordMinLength,
        requireStrongPassword: this.settings.requireStrongPassword
      }
    };
  }

  /**
   * Get password validation error message
   * @param {string} password - Password to validate
   * @returns {string} Error message or empty string
   */
  getPasswordValidationError(password) {
    if (!this.settings.requireStrongPassword) {
      return password.length >= this.settings.passwordMinLength ? "" : 
        `Mật khẩu phải có ít nhất ${this.settings.passwordMinLength} ký tự`;
    }

    const validation = this.validatePassword(password);
    if (!validation.isValid) {
      const missingRequirements = [];
      if (!validation.minLength) missingRequirements.push(`tối thiểu ${this.settings.passwordMinLength} ký tự`);
      if (!validation.hasLowerCase) missingRequirements.push("một chữ thường (a-z)");
      if (!validation.hasUpperCase) missingRequirements.push("một chữ hoa (A-Z)");
      if (!validation.hasNumbers) missingRequirements.push("một chữ số (0-9)");
      if (!validation.hasSpecialChar) missingRequirements.push("một ký tự đặc biệt (!@#$%^&*)");

      return `Mật khẩu phải bao gồm: ${missingRequirements.join(", ")}`;
    }

    return "";
  }
}

export default new SecurityService();
