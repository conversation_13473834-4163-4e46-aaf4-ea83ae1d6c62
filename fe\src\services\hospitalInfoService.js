import axiosInstance from "./axiosInstance";

const API_URL = import.meta.env.VITE_HOSPITAL_INFO_API;

/**
 * Service để lấy thông tin bệnh viện
 * Thay thế các thông tin hard-coded trong ứng dụng
 */
const hospitalInfoService = {
  /**
   * Lấy thông tin bệnh viện
   * @returns {Promise} Promise chứa thông tin bệnh viện
   */
  getHospitalInfo: async () => {
    try {
      console.log("Fetching hospital info from:", API_URL);
      const response = await axiosInstance.get(API_URL);
      console.log("Hospital info response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error fetching hospital info:", error);
      // Fallback to default data if API fails
      return {
        id: 1,
        name: "Trung Tâm Hiến Máu",
        address: "đường CMT8, Q.3, TP.HCM, Vietnam",
        phone: "02838554137",
        email: "<EMAIL>",
        workingHours: "Th<PERSON> 2 - Th<PERSON> 6: 7:00 - 17:00",
        mapImageUrl: "https://www.google.com/maps/place/10%C2%B046'30.5%22N+106%C2%B041'10.4%22E/@10.7751389,106.6862222,17z/data=!3m1!4b1!4m4!3m3!8m2!3d10.7751389!4d106.6862222?entry=ttu&g_ep=EgoyMDI1MDUyOC4wIKXMDSoASAFQAw%3D%3D",
        latitude: 10.7751237,
        longitude: 106.6862143
      };
    }
  },
};

export default hospitalInfoService;
