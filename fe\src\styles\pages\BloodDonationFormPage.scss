@use "../base/variables" as vars;
@use "../base/mixin" as mix;

.blood-donation-form-page {
  @include mix.body-base;
  min-height: 100vh;
  padding: 0;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 50%, #cbd5e1 100%);

  // Card Title Styles
  .card-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 20px;
    font-weight: bold;
    color: #1890ff;

    .title-icon {
      font-size: 24px;
    }
  }

  // Form Card Styles
  .form-card {
    max-width: 1200px;
    margin: 0 auto 32px auto;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    background: white;
    overflow: hidden;

    .ant-card-body {
      padding: 32px;

      // Personal Info Section
      .personal-info-section {
        background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
        padding: 32px;
        border-radius: 16px;
        border: 2px solid #e5e7eb;
        margin-bottom: 32px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
        position: relative;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;

          border-radius: 16px 16px 0 0;
        }

        .ant-row {
          margin-bottom: 0;

          .ant-col {
            padding-bottom: 20px;

            &:last-child {
              padding-bottom: 0;
            }
          }
        }

        // Enhanced form items in personal info
        .ant-form-item {
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  // Info Section
  .info-section {
    background: #f8fafc;
    color: #000000;
    border: 1.5px solid #000;
    border-radius: 10px;
    padding: 18px 22px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    font-size: 1.08rem;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: background 0.2s;

    .anticon {
      font-size: 24px;
      margin-right: 14px;
      flex-shrink: 0;
    }

    b {
      color: #000000;
    }

    .alert-link,
    span[style*='cursor: pointer'] {
      color: #0056b3;
      text-decoration: underline;
      cursor: pointer;
      font-weight: 600;
      margin-left: 4px;

      &:hover {
        color: #003366;
        text-decoration: underline wavy;
      }
    }
  }

  // Alert Styles
  .alert-title {
    font-weight: 600;
  }

  .alert-description {
    line-height: 1.6;
  }

  .alert-link {
    color: #1890ff;
  }

  .custom-alert {
    margin-bottom: 0;
    border: 1px solid #91d5ff;
    background-color: #f6ffed;
  }

  // Personal Info Header
  .personal-info-header {
    background: linear-gradient(135deg, #02314b);
    padding: 20px 24px;
    border-radius: 12px;
    margin-bottom: 32px;
    box-shadow: 0 8px 24px rgba(79, 70, 229, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;

    // Background decoration
    &::before {
      content: "";
      position: absolute;
      top: -50%;
      right: -20%;
      width: 200px;
      height: 200px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      filter: blur(60px);
    }

    .header-title {
      font-weight: 700;
      font-size: 22px;
      color: white;
      margin: 0;
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      letter-spacing: 0.5px;
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  // Form Labels
  .form-label {
    font-weight: 700;
    color: #1f2937;
    font-size: 15px;
    margin-bottom: 8px;
    display: block;
    letter-spacing: 0.3px;

    &::after {
      content: " *";
      color: #ef4444;
      font-weight: 600;
    }
  }

  // Disabled Inputs
  .disabled-input {
    background-color: #f1f5f9;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-weight: 600;
    color: #475569;
    font-size: 15px;
    padding: 12px 16px;

    &:hover {
      border-color: #cbd5e1;
      background-color: #f8fafc;
    }

    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      background-color: white;
    }
  }

  .disabled-datepicker {
    width: 100%;
    background-color: #f1f5f9;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-weight: 600;
    color: #475569;

    .ant-picker-input>input {
      font-weight: 600;
      color: #475569;
      font-size: 15px;
    }

    &:hover {
      border-color: #cbd5e1;
      background-color: #f8fafc;
    }
  }

  // Form Item Improvements
  .ant-form-item {
    margin-bottom: 24px;

    .ant-form-item-label {
      padding-bottom: 8px;

      >label {
        font-weight: 700;
        color: #1f2937;
        font-size: 15px;
        letter-spacing: 0.3px;

        &.ant-form-item-required::before {
          color: #ef4444;
          font-weight: 700;
          font-size: 16px;
        }
      }
    }

    .ant-form-item-control-input {

      .ant-input,
      .ant-picker {
        border-radius: 10px;
        border: 2px solid #e5e7eb;
        padding: 12px 16px;
        font-size: 15px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          border-color: #3b82f6;
          box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }

        &:focus {
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
      }
    }
  }

  // Radio Group
  .radio-group {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
    margin-top: 8px;

    .radio-item {
      font-weight: 600;
      color: #374151;
      font-size: 15px;
      padding: 8px 16px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #f3f4f6;
      }

      .ant-radio {
        margin-right: 8px;

        .ant-radio-inner {
          border-width: 2px;
          width: 18px;
          height: 18px;
        }

        &.ant-radio-checked .ant-radio-inner {
          border-color: #3b82f6;
          background-color: #3b82f6;
        }
      }
    }
  }

  // Submit Section
  .submit-section {
    text-align: center;
    margin-top: 32px;
    padding: 24px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 12px;

    .submit-button {
      height: 48px;
      font-size: 16px;
      font-weight: 600;
      border-radius: 24px;
      background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
      border: none;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
      }
    }
  }

  // Health Survey Styles
  .health-survey-card {
    max-width: 800px;
    margin: 32px auto;
  }

  .survey-description {
    display: block;
    margin-bottom: 16px;
  }

  .section-title {
    font-weight: bold;
    font-size: 18px;
  }

  .blood-type-alert {
    margin-bottom: 16px;
  }

  .health-info-alert {
    margin-bottom: 16px;

    .ant-alert-description {
      div {
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .input-number {
    width: 100%;
  }

  .datepicker-full {
    width: 100%;
  }

  .female-section-title {
    color: #ff69b4;
  }

  .survey-submit-section {
    text-align: center;
    margin-top: 32px;
  }

  // Appointment Styles
  .appointment-card {
    max-width: 800px;
    margin: 32px auto;
  }

  .appointment-description {
    display: block;
    margin-bottom: 24px;
  }

  .appointment-alert {
    margin-bottom: 24px;
  }

  .time-radio-group {
    width: 100%;

    .time-radio-button {
      width: 50%;
      text-align: center;
    }
  }

  // Hospital Card
  .hospital-card {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #0ea5e9;
    margin-bottom: 24px;

    .hospital-icon {
      text-align: center;

      .icon {
        font-size: 48px;
      }
    }

    .hospital-title {
      margin: 0;
      color: #0369a1;
    }

    .hospital-address {
      display: block;
      margin-top: 8px;
    }

    .hospital-department {
      display: block;
      margin-top: 4px;
    }

    .hospital-note {
      display: block;
      margin-top: 8px;
      font-size: 14px;
    }
  }

  .appointment-submit-section {
    text-align: center;
    margin-top: 32px;
  }

  .registration-content {
    padding: 40px 20px;
    min-height: calc(100vh - 200px);
    display: flex;
    align-items: center;
    justify-content: center;

    // For form pages, use different layout
    &:not(.result-page) {
      max-width: 1200px;
      margin: 0 auto;
      padding: 32px 24px;
      display: block;
      min-height: auto;

      // Ensure consistent width for all content
      >* {
        max-width: 100%;
      }
    }

    .page-header {
      margin-bottom: 48px;
      display: flex;
      align-items: stretch;
      gap: 32px;

      // Hero Section
      .hero-section {
        // flex: 0 0 45%;
        // background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
        padding: 48px 32px;
        border-radius: 20px;
        margin-bottom: 0;
        box-shadow: 0 20px 40px rgba(220, 38, 38, 0.3);
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;

        // Background decoration
        &::before {
          content: "";
          position: absolute;
          top: -50%;
          right: -20%;
          width: 300px;
          height: 300px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 50%;
          filter: blur(100px);
        }

        &::after {
          content: "";
          position: absolute;
          bottom: -30%;
          left: -10%;
          width: 200px;
          height: 200px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 50%;
          filter: blur(80px);
        }

        .hero-content {
          position: relative;
          z-index: 1;
          width: 100%;

          h1 {
            text-align: center;
            color: white;
            margin-bottom: 12px;
            font-size: 32px;
            font-weight: bold;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.5px;
            line-height: 1.2;
          }

          .hero-subtitle {
            display: block;
            text-align: center;
            font-size: 18px;
            margin-bottom: 16px;
            color: rgba(255, 255, 255, 0.95);
            font-weight: 500;
            line-height: 1.4;
          }

          .hero-quote {
            display: block;
            text-align: center;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            font-style: italic;
            margin-bottom: 0;
            line-height: 1.3;
          }
        }
      }

      // Steps Navigation
      .steps-navigation {
        flex: 0 0 55%;
        background: white;
        padding: 40px 32px;
        border-radius: 20px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        display: flex;
        align-items: center;

        .ant-steps {
          width: 100%;
          margin: 0;

          .ant-steps-item {
            padding-inline-start: 0 !important;
          }

          .ant-steps-item-title {
            font-weight: 600 !important;
            font-size: 15px !important;
            line-height: 1.3 !important;
          }

          .ant-steps-item-description {
            font-size: 13px !important;
            color: #6b7280 !important;
            line-height: 1.2 !important;
            margin-top: 4px !important;
          }

          .ant-steps-item-icon {
            font-size: 18px !important;
          }
        }
      }

      .progress-steps {
        @include mix.flex-between;
        margin-top: vars.$spacing-xl;
        position: relative;
        padding: 0 vars.$spacing-xl;

        &::before {
          content: "";
          position: absolute;
          top: 20px;
          left: vars.$spacing-xl;
          right: vars.$spacing-xl;
          height: 2px;
          background: vars.$border-light;
          z-index: 1;
        }

        .step {
          position: relative;
          z-index: 2;
          text-align: center;
          flex: 1;

          .step-number {
            @include mix.flex-center;
            width: 40px;
            height: 40px;
            border-radius: vars.$border-radius-full;
            background: vars.$background-card;
            border: 2px solid vars.$border-light;
            margin: 0 auto vars.$spacing-sm;
            font-weight: vars.$font-weight-semibold;
            color: vars.$text-secondary;
            transition: all 0.3s;
          }

          .step-text {
            @include mix.text(vars.$font-size-sm, vars.$text-secondary);
          }

          &.active {
            .step-number {
              background: vars.$primary-color;
              border-color: vars.$primary-color;
              color: vars.$white;
            }

            .step-text {
              color: vars.$primary-color;
              font-weight: vars.$font-weight-semibold;
            }
          }

          &.completed {
            .step-number {
              background: vars.$success-color;
              border-color: vars.$success-color;
              color: vars.$white;
            }
          }
        }
      }
    }

    .form-card {
      background: white;
      border-radius: 16px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      border: 1px solid #e5e7eb;
      padding: 32px;
      margin: 0 auto 32px auto;
      max-width: 1200px;

      .card-title {
        font-size: 20px;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 24px;
        text-align: center;
      }

      // Personal Info Styling
      .personal-info-section {
        .section-title {
          font-weight: bold;
          font-size: 18px;
          margin-bottom: 24px;
        }

        .ant-form-item-label>label {
          font-weight: bold;
          font-size: 14px;
          color: #374151;
        }

        .ant-input[disabled] {
          font-weight: bold;
          font-size: 15px;
          color: #1890ff;
          background-color: #f8fafc;
          border-color: #e2e8f0;
        }

        .ant-picker[disabled] {
          font-weight: bold;
          font-size: 15px;
          color: #1890ff;
          background-color: #f8fafc;
          border-color: #e2e8f0;
        }

        .ant-radio-wrapper {
          font-weight: bold;
          font-size: 15px;
        }
      }

      // Health Survey Styling
      .health-survey-section {
        .section-title {
          font-weight: bold;
          font-size: 18px;
          margin-bottom: 24px;
        }

        .basic-info-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 16px;
          margin-bottom: 32px;
        }

        .question-card {
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 12px;
          padding: 24px;
          margin-bottom: 24px;

          .question-title {
            font-weight: 600;
            font-size: 16px;
            color: #1f2937;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
          }

          .ant-checkbox-wrapper {
            margin-bottom: 12px;
            line-height: 1.6;
            font-weight: 500;
            font-size: 15px;
          }

          .ant-radio-wrapper {
            margin-bottom: 12px;
            line-height: 1.6;
            font-weight: 500;
            font-size: 15px;
          }

          .conditional-input {
            margin-top: 12px;
            margin-left: 24px;
          }
        }

        .alert-info {
          background: #e0f2fe;
          border: 1px solid #0891b2;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 24px;

          .alert-content {
            display: flex;
            align-items: flex-start;
            gap: 12px;

            .alert-icon {
              color: #0891b2;
              font-size: 20px;
              margin-top: 2px;
            }

            .alert-text {
              flex: 1;

              .alert-title {
                font-weight: 600;
                color: #0891b2;
                margin-bottom: 8px;
              }

              .alert-details {
                color: #0369a1;
                line-height: 1.5;

                strong {
                  font-weight: 600;
                }
              }
            }
          }
        }
      }

      .form-section {
        margin-bottom: vars.$spacing-xl;

        h3 {
          @include mix.heading(vars.$font-size-lg, vars.$font-weight-semibold);
          margin-bottom: vars.$spacing-md;
        }

        .form-row {
          @include mix.flex-between;
          gap: vars.$spacing-md;
          margin-bottom: vars.$spacing-md;

          @include mix.tablet {
            flex-direction: column;
            gap: vars.$spacing-sm;
          }
        }

        .form-group {
          flex: 1;

          label {
            @include mix.form-label;
          }

          input,
          select {
            @include mix.form-input;
          }
        }
      }

      .checkbox-list,
      .radio-group {
        display: flex;
        flex-direction: column;
        gap: vars.$spacing-sm;

        .checkbox-item,
        .radio-item {
          @include mix.flex-align(flex-start, center);
          gap: vars.$spacing-sm;
          cursor: pointer;

          input[type="checkbox"],
          input[type="radio"] {
            width: 18px;
            height: 18px;
            cursor: pointer;
          }

          span {
            @include mix.text(vars.$font-size-base, vars.$text-primary);
          }
        }
      }

      .form-actions {
        @include mix.flex-between;
        margin-top: vars.$spacing-xl;
        gap: vars.$spacing-md;

        .btn {
          flex: 1;
          max-width: 200px;

          &.btn-primary {
            @include mix.button-primary;
          }

          &.btn-secondary {
            @include mix.button-secondary;
          }
        }
      }
    }

    .result-section {
      width: 100%;
      max-width: 800px;
      margin: 0 auto;

      .result-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        padding: 48px 32px;
        text-align: center;
        transition: all 0.3s ease;
        border: 2px solid transparent;

        .result-icon {
          font-size: 4rem;
          margin-bottom: 24px;
          display: inline-block;
          padding: 24px;
          border-radius: 50%;
          width: 120px;
          height: 120px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 24px auto;
        }

        .result-content {
          h2 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 16px;
            text-transform: uppercase;
            letter-spacing: 1px;
          }

          p {
            font-size: 1.125rem;
            color: #6b7280;
            margin-bottom: 32px;
            line-height: 1.6;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
          }
        }

        // Failed state styling
        &.failed {
          border-color: #ef4444;
          background: linear-gradient(135deg,
              rgba(239, 68, 68, 0.05) 0%,
              rgba(239, 68, 68, 0.1) 100%);

          .result-icon {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
            animation: shake 0.5s ease-in-out;
          }

          h2 {
            color: #dc2626;
          }
        }

        // Error state styling
        &.error {
          border-color: #ef4444;
          background: linear-gradient(135deg,
              rgba(239, 68, 68, 0.05) 0%,
              rgba(239, 68, 68, 0.1) 100%);

          .result-icon {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
            animation: shake 0.5s ease-in-out;
          }

          h2 {
            color: #dc2626;
          }
        }

        // Success/Scheduled state styling
        &.scheduled {
          border-color: #10b981;
          background: linear-gradient(135deg,
              rgba(16, 185, 129, 0.05) 0%,
              rgba(16, 185, 129, 0.1) 100%);

          .result-icon {
            color: #10b981;
            background: rgba(16, 185, 129, 0.1);
            animation: bounce 0.6s ease-in-out;
          }

          h2 {
            color: #059669;
          }

          .appointment-summary {
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
            border: 2px solid #bbf7d0;
            border-radius: 16px;
            padding: 32px;
            margin: 32px 0;
            text-align: left;

            .summary-title {
              display: flex;
              align-items: center;
              font-weight: 700;
              color: #059669;
              margin-bottom: 24px;
              font-size: 1.25rem;

              .me-2 {
                margin-right: 8px;
              }
            }

            .appointment-details {
              .detail-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 0;
                border-bottom: 1px solid #d1fae5;

                &:last-child {
                  border-bottom: none;
                }

                strong {
                  font-weight: 600;
                  color: #374151;
                }

                .value {
                  font-weight: 500;
                  color: #1f2937;
                }
              }
            }
          }
        }

        .result-actions {
          display: flex;
          justify-content: center;
          gap: 16px;
          margin-top: 32px;
          padding-top: 32px;
          border-top: 1px solid #e5e7eb;

          .btn {
            min-width: 180px;
            padding: 14px 28px;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;

            .me-2 {
              margin-right: 0 !important;
            }

            &.btn-primary {
              background: linear-gradient(135deg, #10b981 0%, #059669 100%);
              border: none;
              color: white;

              &:hover {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
                transform: translateY(-2px);
                box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
              }
            }

            &.btn-outline-primary {
              border: 2px solid #3b82f6;
              color: #3b82f6;
              background: transparent;

              &:hover {
                background: #3b82f6;
                color: white;
                transform: translateY(-2px);
                box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
              }
            }
          }
        }
        // Đã xoá dấu đóng ngoặc thừa ở đây
      }
    }
  }
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-5px);
  }

  75% {
    transform: translateX(5px);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -15px, 0);
  }
  70% {
    transform: translate3d(0, -7px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

// Desktop layout adjustments
@media (min-width: 1024px) {
  .blood-donation-form-page {
    .registration-content {
      .page-header {
        .hero-section {
          min-height: 240px;
        }

        .steps-navigation {
          min-height: 240px;
        }
      }
    }
  }
}

// Medium desktop adjustments
@media (min-width: 768px) and (max-width: 1023px) {
  .blood-donation-form-page {
    .registration-content {
      .page-header {
        .hero-section {
          flex: 0 0 40%;

          .hero-content h1 {
            font-size: 28px !important;
          }

          .hero-content .hero-subtitle {
            font-size: 16px !important;
          }
        }

        .steps-navigation {
          flex: 0 0 60%;
          padding: 32px 24px;
        }
      }
    }
  }
}

// Responsive adjustments
@include mix.tablet {
  .blood-donation-form-page {
    .registration-content {
      .page-header {
        flex-direction: column;
        gap: 24px;

        .progress-steps {
          padding: 0 vars.$spacing-md;
        }
      }

      .form-card {
        padding: vars.$spacing-lg;
      }
    }

    .result-section {
      .result-card {
        padding: 32px 24px;

        .result-icon {
          font-size: 3rem;
          width: 100px;
          height: 100px;
          padding: 20px;
        }

        .result-content {
          h2 {
            font-size: 1.5rem;
          }

          p {
            font-size: 1rem;
          }
        }

        .appointment-summary {
          padding: 24px;
          margin: 24px 0;

          .summary-title {
            font-size: 1.125rem;
          }

          .appointment-details {
            .detail-item {
              flex-direction: column;
              align-items: flex-start;
              gap: 8px;
              text-align: left;
            }
          }
        }

        .result-actions {
          flex-direction: column;
          gap: 12px;

          .btn {
            width: 100%;
            min-width: auto;
          }
        }
      }
    }
  }
}

@include mix.mobile {
  .blood-donation-form-page {
    .registration-content {
      .page-header {
        .progress-steps {
          padding: 0 vars.$spacing-sm;

          .step {
            .step-text {
              font-size: vars.$font-size-xs;
            }
          }
        }
      }

      .form-card {
        padding: vars.$spacing-md;
      }
    }
  }

  // Global Ant Design Overrides
  .ant-card {
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border: 1px solid #e5e7eb;

    .ant-card-head {
      border-bottom: 2px solid #e5e7eb;
      border-radius: 16px 16px 0 0;

      .ant-card-head-title {
        font-size: 20px;
        font-weight: 700;
        color: #1f2937;
      }
    }

    .ant-card-body {
      padding: 32px;
    }
  }

  .ant-form-item-label>label {
    font-weight: 600;
    color: #374151;
    font-size: 14px;
  }

  .ant-input,
  .ant-input-number,
  .ant-picker {
    border-radius: 8px;
    border: 1px solid #d1d5db;

    &:hover {
      border-color: #3b82f6;
    }

    &:focus {
      border-color: #2563eb;
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }
  }

  .ant-checkbox-wrapper {
    margin-bottom: 12px;
    line-height: 1.6;
    font-weight: 500;

    .ant-checkbox+span {
      font-size: 15px;
    }
  }

  .ant-radio-wrapper {
    margin-bottom: 12px;
    line-height: 1.6;
    font-weight: 500;
    font-size: 15px;
  }

  .ant-btn-primary {
    border-radius: 8px;
    font-weight: 600;
    height: auto;
    padding: 12px 24px;
    font-size: 16px;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 8px 16px rgba(24, 144, 255, 0.3);
    }
  }

  // Form Actions
  .form-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;

    .ant-btn {
      border-radius: 8px;
      font-weight: 600;
      height: auto;
      padding: 12px 24px;
      font-size: 16px;
      min-width: 120px;

      &.ant-btn-primary {
        background: #1890ff;
        border-color: #1890ff;

        &:hover {
          background: #40a9ff;
          border-color: #40a9ff;
          transform: translateY(-1px);
          box-shadow: 0 8px 16px rgba(24, 144, 255, 0.3);
        }
      }

      &.ant-btn-default {
        &:hover {
          border-color: #40a9ff;
          color: #40a9ff;
        }
      }
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    .registration-content {
      padding: 20px 16px;

      &:not(.result-page) {
        padding: 16px;
      }
    }

    .page-header {
      margin-bottom: 24px !important;
      flex-direction: column !important;
      gap: 24px !important;

      .hero-section {
        padding: 32px 20px !important;

        .hero-content h1 {
          font-size: 28px !important;
        }
      }

      .steps-navigation {
        padding: 20px !important;
      }
    }

    .form-card {
      margin: 16px auto !important;
      padding: 20px !important;
    }
  }

  @media (max-width: 480px) {
    .page-header {
      .hero-section {
        padding: 24px 16px !important;

        .hero-content {
          h1 {
            font-size: 24px !important;
          }

          .hero-subtitle {
            font-size: 16px !important;
          }
        }
      }
    }

    .form-card {
      padding: 16px !important;
    }
  }

  // Retry button styling
  .retry-button {
    background: transparent !important;
    border: 2px solid #3b82f6 !important;
    color: #3b82f6 !important;
    border-radius: 10px !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    letter-spacing: 0.3px !important;
    min-width: 160px;
    padding: 10px 20px !important;
    margin-left: 12px !important;
    box-shadow: 0 3px 12px rgba(59, 130, 246, 0.2) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
          transparent,
          rgba(59, 130, 246, 0.1),
          transparent);
      transition: left 0.5s;
    }

    &:hover {
      background: #3b82f6 !important;
      color: white !important;
      border-color: #3b82f6 !important;
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4) !important;

      &::before {
        left: 100%;
      }

      .anticon {
        transform: rotate(360deg);
      }
    }

    &:active {
      transform: translateY(0) !important;
      box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2) !important;
    }

    &:focus {
      box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2),
        0 0 0 3px rgba(59, 130, 246, 0.2) !important;
    }

    .anticon {
      font-size: 18px;
      transition: transform 0.3s ease;
    }
  }

  // Extra small mobile devices
  @media (max-width: 480px) {
    .registration-content {
      .result-section {
        .result-card {
          padding: 24px 16px;

          .result-icon {
            font-size: 2.5rem;
            width: 80px;
            height: 80px;
            padding: 16px;
          }

          .result-content {
            h2 {
              font-size: 1.25rem;
            }
          }
        }
      }
    }
  }
}