import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useParams } from "react-router-dom";
import DoctorLayout from "../../components/doctor/DoctorLayout";
import { toast } from "../../utils/toastUtils";
import ProseMirrorEditor from "../../components/shared/ProseMirrorEditor";
import {
  uploadImage,
  uploadImageForDoctorArticle,
} from "../../services/uploadService";
import {
  createArticle,
  updateArticle,
  getBloodArticleDetail,
} from "../../services/bloodArticleService";
import "../../styles/pages/BlogEditor.scss";

const BlogEditor = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditMode = Boolean(id);

  const [formData, setFormData] = useState({
    title: "",
    category: "<PERSON><PERSON><PERSON> l<PERSON>",
    contentType: "document", // document, news, announcement
    excerpt: "",
    content: "",
    featuredImage: null,
    tags: "",
    status: "draft",
    targetAudience: "public", // public (Guest/Member), internal (staff only)
  });

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [imagePreview, setImagePreview] = useState(null);
  const editorRef = useRef(null);

  // Load blog data if editing
  useEffect(() => {
    if (isEditMode) {
      setLoading(true);
      // Load real blog data
      getBloodArticleDetail(id)
        .then((blogData) => {
          setFormData({
            title: blogData.title || "",
            category: blogData.category || "Tài liệu",
            contentType: blogData.contentType || "document",
            excerpt: blogData.excerpt || "",
            content: blogData.content || "",
            featuredImage: blogData.imgUrl || null,
            tags: Array.isArray(blogData.tags)
              ? blogData.tags
                  .map((tag) => tag.tagName || tag.name || tag)
                  .join(", ")
              : blogData.tags || "",
            status: blogData.status || "draft",
            targetAudience: blogData.targetAudience || "public",
          });

          // Set image preview if exists
          if (blogData.imgUrl) {
            setImagePreview(blogData.imgUrl);
          }

          setLoading(false);
        })
        .catch((error) => {
          console.error("Error loading blog:", error);
          toast.error(" Không thể tải bài viết");
          setLoading(false);
        });
    }
  }, [id, isEditMode]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleImageChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error(" Kích thước ảnh không được vượt quá 5MB");
        return;
      }

      // Validate file type
      if (!file.type.startsWith("image/")) {
        toast.error(" Vui lòng chọn file ảnh (JPG, PNG, GIF)");
        return;
      }

      setUploading(true);

      try {
        // Create preview immediately
        const reader = new FileReader();
        reader.onload = (e) => {
          setImagePreview(e.target.result);
        };
        reader.readAsDataURL(file);

        // Upload to server using doctor article API
        const uploadResult = await uploadImageForDoctorArticle(file);

        if (uploadResult.success) {
          setFormData((prev) => ({
            ...prev,
            featuredImage: uploadResult.url, // Store API URL or base64 fallback
          }));
          toast.success("📸 Upload ảnh thành công!");
          if (uploadResult.isLocal) {
            toast.info(" Upload API không khả dụng, sử dụng base64 tạm thời");
          }
        } else {
          throw new Error(uploadResult.message || "Upload thất bại");
        }
      } catch (error) {
        console.error("Error uploading image:", error);
        toast.error(
          error.message || " Không thể upload ảnh. Vui lòng thử lại."
        );
        // Reset preview on error
        setImagePreview(null);
        setFormData((prev) => ({
          ...prev,
          featuredImage: null,
        }));
      } finally {
        setUploading(false);
      }
    }
  };

  const handleContentChange = (content) => {
    setFormData((prev) => ({
      ...prev,
      content: content,
    }));
  };

  const handleSave = async (status = "draft") => {
    setSaving(true);

    try {
      // Get current user info
      const currentUser = localStorage.getItem("currentUser");
      let userId = null;

      if (currentUser) {
        try {
          const userData = JSON.parse(currentUser);
          userId = userData.id || userData.userId || userData.userID;
        } catch (error) {
          console.error("Error parsing currentUser:", error);
        }
      }

      if (!userId) {
        toast.error(
          "Không tìm thấy thông tin người dùng. Vui lòng đăng nhập lại."
        );
        return;
      }

      // Prepare article data for API
      const articleData = {
        title: formData.title,
        content: formData.content,
        imgUrl: formData.featuredImage || "",
        tags: formData.tags
          ? formData.tags
              .split(",")
              .map((tag) => tag.trim())
              .filter((tag) => tag !== "")
          : [],
        userId: userId,
      };

      let result;
      if (isEditMode) {
        // Update existing article
        result = await updateArticle(id, articleData);
      } else {
        // Create new article
        result = await createArticle(articleData);
      }

      if (result) {
        toast.success(
          isEditMode
            ? " Bài viết đã được cập nhật thành công!"
            : " Bài viết đã được tạo thành công!"
        );

        // Navigate back to blog management
        navigate("/doctor/blog");
      }
    } catch (error) {
      console.error("Error saving blog:", error);
      toast.error(
        error.response?.data?.message ||
          error.message ||
          " Có lỗi xảy ra khi lưu bài viết"
      );
    } finally {
      setSaving(false);
    }
  };

  const handleSubmitForReview = () => {
    if (!formData.title.trim() || !formData.content.trim()) {
      toast.error(" Vui lòng nhập đầy đủ tiêu đề và nội dung bài viết");
      return;
    }
    handleSave("published"); // Doctor tự động publish
  };

  const handleSaveDraft = () => {
    handleSave("draft");
  };

  if (loading) {
    return (
      <DoctorLayout pageTitle="Chỉnh sửa Blog">
        <div className="doctor-content">
          <div className="loading-spinner">
            <div className="spinner"></div>
            <p>Đang tải dữ liệu...</p>
          </div>
        </div>
      </DoctorLayout>
    );
  }

  return (
    <DoctorLayout pageTitle="Chỉnh sửa Blog">
      <div className="doctor-blog-editor-content">
        <div className="blog-editor">
          <div className="editor-header">
            <div className="header-content">
              <h1>{isEditMode ? "Chỉnh sửa bài viết" : "Tạo bài viết mới"}</h1>
              <p>Chia sẻ kiến thức và kinh nghiệm về hiến máu</p>
            </div>

            <div className="header-actions">
              <button
                className="btn-secondary"
                onClick={() => navigate("/doctor/blog")}
                disabled={saving}
              >
                <i className="fas fa-arrow-left"></i>
                Quay lại
              </button>

              <button
                className="btn-outline"
                onClick={handleSaveDraft}
                disabled={saving}
              >
                {saving ? (
                  <>
                    <i className="fas fa-spinner fa-spin"></i>
                    Đang lưu...
                  </>
                ) : (
                  <>
                    <i className="fas fa-save"></i>
                    Lưu nháp
                  </>
                )}
              </button>

              <button
                className="btn-primary"
                onClick={handleSubmitForReview}
                disabled={saving}
              >
                {saving ? (
                  <>
                    <i className="fas fa-spinner fa-spin"></i>
                    Đang đăng...
                  </>
                ) : (
                  <>
                    <i className="fas fa-globe"></i>
                    Đăng ngay
                  </>
                )}
              </button>
            </div>
          </div>

          <div className="editor-content">
            <div className="editor-main">
              <div className="form-group">
                <label htmlFor="title">Tiêu đề bài viết *</label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  placeholder="Nhập tiêu đề bài viết..."
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="excerpt">Tóm tắt</label>
                <textarea
                  id="excerpt"
                  name="excerpt"
                  value={formData.excerpt}
                  onChange={handleInputChange}
                  placeholder="Nhập tóm tắt ngắn gọn về bài viết..."
                  rows="3"
                />
              </div>

              <div className="form-group">
                <label htmlFor="content">Nội dung bài viết *</label>
                <ProseMirrorEditor
                  ref={editorRef}
                  value={formData.content}
                  onChange={handleContentChange}
                  height={400}
                  placeholder="Nhập nội dung bài viết..."
                />
              </div>
            </div>

            <div className="editor-sidebar">
              <div className="sidebar-section">
                <h3>Cài đặt bài viết</h3>

                <div className="form-group">
                  <label htmlFor="category">Danh mục</label>
                  <select
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                  >
                    <option value="Tài liệu">
                      Tài liệu (Hiển thị trên Guest/Member)
                    </option>
                    <option value="Tin tức">
                      Tin tức (Hiển thị trên Guest/Member)
                    </option>
                    <option value="Thông báo">Thông báo nội bộ</option>
                  </select>
                  <small>
                    Bài viết của Doctor khoa máu sẽ được tự động duyệt và đăng
                  </small>
                </div>

                <div className="form-group">
                  <label htmlFor="targetAudience">Đối tượng xem</label>
                  <select
                    id="targetAudience"
                    name="targetAudience"
                    value={formData.targetAudience}
                    onChange={handleInputChange}
                  >
                    <option value="public">Công khai (Guest & Member)</option>
                    <option value="internal">Nội bộ (Chỉ nhân viên)</option>
                  </select>
                  <small>
                    Bài viết công khai sẽ hiển thị trên trang Guest và Member
                  </small>
                </div>

                <div className="form-group">
                  <label htmlFor="tags">Thẻ tag</label>
                  <input
                    type="text"
                    id="tags"
                    name="tags"
                    value={formData.tags}
                    onChange={handleInputChange}
                    placeholder="Nhập các tag, cách nhau bằng dấu phẩy"
                  />
                  <small>Ví dụ: hiến máu, kinh nghiệm, sức khỏe</small>
                </div>
              </div>

              <div className="sidebar-section">
                <h3>Ảnh đại diện</h3>

                <div className="image-upload">
                  <input
                    type="file"
                    id="featuredImage"
                    accept="image/*"
                    onChange={handleImageChange}
                    style={{ display: "none" }}
                  />

                  {imagePreview ? (
                    <div className="image-preview">
                      <img src={imagePreview} alt="Preview" />
                      <button
                        type="button"
                        className="remove-image"
                        onClick={() => {
                          setImagePreview(null);
                          setFormData((prev) => ({
                            ...prev,
                            featuredImage: null,
                          }));
                        }}
                      >
                        <i className="fas fa-times"></i>
                      </button>
                    </div>
                  ) : (
                    <label
                      htmlFor="featuredImage"
                      className="upload-placeholder"
                    >
                      <i className="fas fa-cloud-upload-alt"></i>
                      <span>Chọn ảnh đại diện</span>
                      <small>JPG, PNG tối đa 5MB</small>
                    </label>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DoctorLayout>
  );
};

export default BlogEditor;
