import React from "react";
import { <PERSON>, Button } from "antd";
import AdminLayout from "../../components/admin/AdminLayout";
import AdminPageHeader from "../../components/admin/AdminPageHeader";
import AdminCard from "../../components/admin/shared/AdminCard";
import AdminTable from "../../components/admin/shared/AdminTable";
import AdminFilterBar from "../../components/admin/shared/AdminFilterBar";
import DeleteConfirmModal from "../../components/admin/shared/DeleteConfirmModal";
import UserFormModal from "../../components/admin/users/UserFormModal";
import UserTableColumns from "../../components/admin/users/UserTableColumns";
import { useUserManagement } from "../../hooks/useUserManagement";
import { TeamOutlined, ReloadOutlined } from "@ant-design/icons";

const UserManagement = () => {
  const [form] = Form.useForm();

  const {
    users,
    loading,
    searchTerm,
    showModal,
    editingUser,
    deleteModalVisible,
    STATUS_OPTIONS,
    DEPARTMENTS,
    setSearchTerm,
    setDeleteModalVisible,
    handleEditUser,
    handleCreateUser,
    handleDeleteUser,
    handleModalOk,
    handleDeleteClick,
    handleModalCancel,
    handleStatusChange,
    forceReloadUsers,
  } = useUserManagement();

  // Removed role and status filters - only keep search

  const columns = UserTableColumns({
    onEdit: handleEditUser,
    onDelete: handleDeleteClick,
    onStatusChange: handleStatusChange,
  });

  const handleModalSubmit = () => {
    form
      .validateFields()
      .then((values) => handleModalOk(form, values))
      .catch(() => {});
  };

  return (
    <AdminLayout>
      <AdminPageHeader
        title="Quản lý người dùng"
        icon={<TeamOutlined />}
        subtitle="Thêm, chỉnh sửa, tìm kiếm và phân quyền người dùng trong hệ thống"
      />

      <AdminCard>
        <AdminFilterBar
          searchTerm={searchTerm}
          onSearchChange={(e) => setSearchTerm(e.target.value)}
          searchPlaceholder="Tìm kiếm theo tên hoặc email"
          onAdd={handleCreateUser}
          addButtonText="Thêm người dùng"
          extraActions={
            <Button
              onClick={forceReloadUsers}
              loading={loading}
              icon={<ReloadOutlined />}
            >
              Tải lại
            </Button>
          }
        />

        <AdminTable
          columns={columns}
          data={users}
          loading={loading}
          rowKey="id"
        />
      </AdminCard>

      <UserFormModal
        visible={showModal}
        onOk={handleModalSubmit}
        onCancel={handleModalCancel}
        editingUser={editingUser}
        form={form}
        statusOptions={STATUS_OPTIONS}
        departments={DEPARTMENTS}
      />

      <DeleteConfirmModal
        visible={deleteModalVisible}
        onOk={handleDeleteUser}
        onCancel={() => setDeleteModalVisible(false)}
        title="Xác nhận xoá người dùng"
        content="Bạn có chắc chắn muốn xoá người dùng này không?"
      />
    </AdminLayout>
  );
};

export default UserManagement;
