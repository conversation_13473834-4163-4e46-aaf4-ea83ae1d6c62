import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import {
  validateEmail,
  getPasswordValidationError,
  validatePasswordConfirmation,
  validateFullName,
} from "../../utils/validation";
import "../../styles/components/RegisterForm.scss";
import authService from "../../services/authService";
import { FaEye, FaEyeSlash } from "react-icons/fa";

export default function RegisterForm() {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    fullName: "",
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [showPasswords, setShowPasswords] = useState({
    password: false,
    confirmPassword: false,
  });
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const navigate = useNavigate();

  const validateForm = () => {
    const newErrors = {};

    // Validate full name
    const fullNameError = validateFullName(formData.fullName);
    if (fullNameError) {
      newErrors.fullName = fullNameError;
    }

    // Validate email
    if (!formData.email) {
      newErrors.email = "Email là bắt buộc";
    } else if (!validateEmail(formData.email)) {
      newErrors.email = "Email không đúng định dạng";
    }

    // Validate password
    const passwordError = getPasswordValidationError(formData.password);
    if (passwordError) {
      newErrors.password = passwordError;
    }

    // Validate password confirmation
    const confirmPasswordError = validatePasswordConfirmation(
      formData.password,
      formData.confirmPassword
    );
    if (confirmPasswordError) {
      newErrors.confirmPassword = confirmPasswordError;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const togglePasswordVisibility = (fieldName) => {
    setShowPasswords(prev => ({
      ...prev,
      [fieldName]: !prev[fieldName]
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      const registerData = {
        email: formData.email,
        password: formData.password,
        fullName: formData.fullName
      };

      const result = await authService.register(registerData);

      if (result.success) {
        // After successful registration, automatically login the user
        const loginResult = await authService.login(formData.email, formData.password);

        if (loginResult.success) {
          // Get the appropriate homepage based on user role
          const redirectPath = authService.getRedirectPath();
          navigate(redirectPath);
        } else {
          // If auto-login fails, redirect to login page with success message
          navigate("/login", {
            state: {
              message: "Đăng ký thành công! Vui lòng đăng nhập để tiếp tục.",
              email: formData.email
            }
          });
        }
      } else {
        setErrors({
          submit: result.error || "Đăng ký không thành công. Vui lòng thử lại sau."
        });
      }
    } catch (error) {
      console.error("Registration failed:", error);
      setErrors({
        submit: "Đăng ký không thành công. Vui lòng thử lại sau."
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setIsGoogleLoading(true);
    setErrors({});

    try {
      const result = await authService.googleLogin();

      if (!result.success) {
        setErrors({ submit: result.error || "Không thể khởi tạo đăng nhập Google" });
      }
      // If successful, user will be redirected to Google OAuth page
    } catch (error) {
      console.error("Google login error:", error);
      setErrors({ submit: "Đã xảy ra lỗi khi khởi tạo đăng nhập Google" });
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <div className="register-form__container">
      <div className="register-form__box">
        <div className="register-form__logo">XIN CHÀO</div>
        <div className="register-form__welcome">
          CHÀO MỪNG BẠN ĐẾN VỚI CHÚNG TÔI
        </div>

        {/* Google Login Button */}
        <button
          className="register-form__google-btn"
          type="button"
          onClick={handleGoogleLogin}
          disabled={isGoogleLoading || isLoading}
        >
          <div className="register-form__google-icon">
            <svg width="18" height="18" viewBox="0 0 18 18">
              <path fill="#4285F4" d="M16.51 8H8.98v3h4.3c-.18 1-.74 1.48-1.6 2.04v2.01h2.6a7.8 7.8 0 0 0 2.38-5.88c0-.57-.05-.66-.15-1.18z" />
              <path fill="#34A853" d="M8.98 17c2.16 0 3.97-.72 5.3-1.94l-2.6-2.04a4.8 4.8 0 0 1-7.18-2.53H1.83v2.07A8 8 0 0 0 8.98 17z" />
              <path fill="#FBBC05" d="M4.5 10.49a4.8 4.8 0 0 1 0-3.07V5.35H1.83a8 8 0 0 0 0 7.28l2.67-2.14z" />
              <path fill="#EA4335" d="M8.98 4.72c1.16 0 2.23.4 3.06 1.2l2.3-2.3A8 8 0 0 0 1.83 5.35L4.5 7.42a4.77 4.77 0 0 1 4.48-2.7z" />
            </svg>
          </div>
          {isGoogleLoading ? "ĐANG XỬ LÝ..." : "Đăng ký với Google"}
        </button>

        {/* Divider */}
        <div className="register-form__divider">
          <span></span>
          <span>hoặc</span>
          <span></span>
        </div>

        <form className="register-form__form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label className="register-form__label">HỌ VÀ TÊN</label>
            <input
              className={`register-form__input ${errors.fullName ? "error" : ""
                }`}
              type="text"
              name="fullName"
              value={formData.fullName}
              onChange={handleChange}
              placeholder="Nhập họ và tên đầy đủ"
              required
            />
            {errors.fullName && (
              <div className="register-form__error">{errors.fullName}</div>
            )}
          </div>

          <div className="form-group">
            <label className="register-form__label">EMAIL</label>
            <input
              className={`register-form__input ${errors.email ? "error" : ""}`}
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Nhập địa chỉ email"
              required
            />
            {errors.email && (
              <div className="register-form__error">{errors.email}</div>
            )}
          </div>

          <div className="form-group">
            <label className="register-form__label">MẬT KHẨU</label>
            <div className="password-input-container">
              <input
                className={`register-form__input ${errors.password ? "error" : ""
                  }`}
                type={showPasswords.password ? "text" : "password"}
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="Tối thiểu 6 ký tự bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt"
                required
              />
              <button
                type="button"
                className={`password-toggle-btn ${showPasswords.password ? 'showing' : ''}`}
                onClick={() => togglePasswordVisibility('password')}
                title={showPasswords.password ? 'Ẩn mật khẩu' : 'Hiển thị mật khẩu'}
              >
                {showPasswords.password ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>
            {errors.password && (
              <div className="register-form__error">{errors.password}</div>
            )}
          </div>

          <div className="form-group">
            <label className="register-form__label">XÁC NHẬN MẬT KHẨU</label>
            <div className="password-input-container">
              <input
                className={`register-form__input ${errors.confirmPassword ? "error" : ""
                  }`}
                type={showPasswords.confirmPassword ? "text" : "password"}
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                placeholder="Nhập lại mật khẩu để xác nhận"
                required
              />
              <button
                type="button"
                className={`password-toggle-btn ${showPasswords.confirmPassword ? 'showing' : ''}`}
                onClick={() => togglePasswordVisibility('confirmPassword')}
                title={showPasswords.confirmPassword ? 'Ẩn mật khẩu' : 'Hiển thị mật khẩu'}
              >
                {showPasswords.confirmPassword ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>
            {errors.confirmPassword && (
              <div className="register-form__error">
                {errors.confirmPassword}
              </div>
            )}
          </div>

          {errors.submit && (
            <div className="register-form__error">{errors.submit}</div>
          )}

          <button
            className="register-form__submit"
            type="submit"
            disabled={isLoading}
          >
            {isLoading ? "ĐANG ĐĂNG KÝ..." : "ĐĂNG KÝ"}
          </button>
        </form>

        <div className="register-form__login">
          BẠN ĐÃ CÓ TÀI KHOẢN?{" "}
          <Link to="/login">
            <span>ĐĂNG NHẬP</span>
          </Link>
        </div>
      </div>
    </div>
  );
}
