import React from "react";
import { Form, DatePicker, Button, Space, Row, Col } from "antd";
import AdminLayout from "../../components/admin/AdminLayout";
import AdminPageHeader from "../../components/admin/AdminPageHeader";
import AdminCard from "../../components/admin/shared/AdminCard";
import AdminTable from "../../components/admin/shared/AdminTable";
import BlogTableColumns from "../../components/admin/blogs/BlogTableColumns";
import BlogDetailModal from "../../components/admin/blogs/BlogDetailModal";
import BlogEditModal from "../../components/admin/blogs/BlogEditModal";
import { useBlogApproval } from "../../hooks/useBlogApproval";
import { Tabs } from "antd";
import {
  FileTextOutlined,
  NotificationOutlined,
  HistoryOutlined,
  CalendarOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import authService from "../../services/authService";

const { RangePicker } = DatePicker;

const BlogApproval = () => {
  const [form] = Form.useForm();

  // Lấy currentUser từ authService, nếu null thì lấy từ localStorage
  let currentUser = authService.getCurrentUser();
  if (!currentUser) {
    try {
      const userData = localStorage.getItem("currentUser");
      if (userData) {
        currentUser = JSON.parse(userData);
      }
    } catch (error) {
      console.error(
        "Error loading currentUser from localStorage in Admin:",
        error
      );
    }
  }

  const {
    activeTab,
    dateFilter,
    dateRange,
    selectedBlog,
    showModal,
    editMode,
    editImage,
    userMap,
    searchTerm,
    filteredItems,
    currentLoading,
    CATEGORY_OPTIONS,
    tags,
    tagsLoading,
    setActiveTab,
    setDateFilter,
    setDateRange,
    setEditImage,
    handleEditBlog,
    handleDeleteBlog,
    handleEditSubmit,
    handleViewBlog,
    handleCloseModal,
    setSearchTerm,
  } = useBlogApproval(currentUser);

  const handleModalSubmit = () => {
    handleEditSubmit(form);
  };

  const columns = BlogTableColumns({
    activeTab,
    userMap,
    onView: handleViewBlog,
    onEdit: handleEditBlog,
    onDelete: handleDeleteBlog,
    currentUser,
  });

  return (
    <AdminLayout>
      <AdminPageHeader
        title="Quản lý Blog"
        icon={<FileTextOutlined />}
        description="Xem, chỉnh sửa, xóa các bài viết tài liệu và tin tức của hệ thống"
      />

      <AdminCard>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={CATEGORY_OPTIONS.map((cat) => ({
            key: cat.value,
            label: (
              <span>
                {cat.value === "Tài liệu" ? (
                  <FileTextOutlined />
                ) : cat.value === "Tin tức" ? (
                  <NotificationOutlined />
                ) : (
                  <HistoryOutlined />
                )}{" "}
                {cat.label}
              </span>
            ),
          }))}
        />

        <div style={{ marginBottom: 16 }}>
          <Row gutter={[16, 8]} align="middle">
            <Col>
              <input
                type="text"
                placeholder={
                  activeTab === "Theo dõi hoạt động"
                    ? "Tìm kiếm hoạt động..."
                    : "Tìm kiếm bài viết..."
                }
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  width: 300,
                  padding: "8px 12px",
                  border: "1px solid #d9d9d9",
                  borderRadius: 6,
                }}
              />
            </Col>
            {(activeTab === "Tài liệu" || activeTab === "Tin tức") && (
              <Col>
                <Space align="center">
                  <CalendarOutlined style={{ color: "#1890ff" }} />
                  <span style={{ fontSize: "14px", color: "#666" }}>
                    Lọc theo ngày:
                  </span>
                  <RangePicker
                    value={dateRange}
                    onChange={setDateRange}
                    placeholder={["Từ ngày", "Đến ngày"]}
                    format="DD/MM/YYYY"
                    style={{ width: 260 }}
                    presets={[
                      {
                        label: "Hôm nay",
                        value: [dayjs(), dayjs()],
                      },
                      {
                        label: "7 ngày qua",
                        value: [dayjs().subtract(7, "day"), dayjs()],
                      },
                      {
                        label: "30 ngày qua",
                        value: [dayjs().subtract(30, "day"), dayjs()],
                      },
                      {
                        label: "Tháng này",
                        value: [
                          dayjs().startOf("month"),
                          dayjs().endOf("month"),
                        ],
                      },
                      {
                        label: "Tháng trước",
                        value: [
                          dayjs().subtract(1, "month").startOf("month"),
                          dayjs().subtract(1, "month").endOf("month"),
                        ],
                      },
                    ]}
                  />
                  {dateRange && (
                    <Button
                      size="small"
                      onClick={() => setDateRange(null)}
                      type="link"
                      style={{ padding: 0 }}
                    >
                      Xóa lọc ngày
                    </Button>
                  )}
                </Space>
              </Col>
            )}
            <Col>
              <Button
                size="small"
                onClick={() => {
                  setSearchTerm("");
                  setDateRange(null);
                }}
                style={{ marginLeft: 8 }}
                disabled={!searchTerm && !dateRange}
              >
                Xóa bộ lọc
              </Button>
            </Col>
          </Row>
        </div>

        <AdminTable
          key={activeTab}
          columns={columns}
          data={filteredItems}
          loading={currentLoading}
          rowKey={
            activeTab === "Tài liệu"
              ? "articleId"
              : activeTab === "Tin tức"
              ? "postId"
              : "logId"
          }
        />
      </AdminCard>

      {/* Detail Modal */}
      <BlogDetailModal
        visible={showModal && !editMode}
        selectedBlog={selectedBlog}
        activeTab={activeTab}
        userMap={userMap}
        onClose={handleCloseModal}
        onDelete={handleDeleteBlog}
      />

      {/* Edit Modal */}
      <BlogEditModal
        visible={showModal && editMode}
        selectedBlog={selectedBlog}
        activeTab={activeTab}
        editImage={editImage}
        tags={tags}
        tagsLoading={tagsLoading}
        onCancel={handleCloseModal}
        onSubmit={handleModalSubmit}
        onImageChange={setEditImage}
        form={form}
      />
    </AdminLayout>
  );
};

export default BlogApproval;
