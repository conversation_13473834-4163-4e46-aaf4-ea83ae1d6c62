import { apiClient } from "./axiosInstance";
import config from "../config/environment";

const API_URL = config.api.bloodInventory;

export const fetchBloodInventory = async () => {
  const response = await apiClient.get(API_URL);
  return response.data;
};

export const checkInBloodInventory = async (payload) => {
  const response = await apiClient.post(`${API_URL}/check-in`, payload);
  return response.data;
};

export const checkOutBloodInventory = async (payload) => {
  const response = await apiClient.post(`${API_URL}/check-out`, payload);
  return response.data;
};
