/* Medical Blood Request Detail Modal - Professional Healthcare Design */

.blood-request-detail-modal {
  /* Modal Container Styling */
  .ant-modal-content {
    background: #fafbfc;
    border-radius: 12px;
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    border: 1px solid #e8eaed;
  }

  /* Modal Header - Clean Professional Look */
  .ant-modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 2px solid #dee2e6;
    padding: 24px 32px 20px;
    margin: 0;

    .ant-modal-title {
      color: #2c3e50 !important;
      font-size: 20px;
      font-weight: 600;
      line-height: 1.3;
      display: flex;
      align-items: center;
      gap: 12px;

      .anticon {
        color: #95a5a6 !important;
        font-size: 18px;
      }

      h4 {
        color: #2c3e50 !important;
        margin: 0;
        font-family: "Segoe UI", "Helvetica Neue", sans-serif;
      }
    }
  }

  /* Close Button Styling */
  .ant-modal-close {
    top: 20px;
    right: 24px;
    color: #6c757d;
    font-size: 16px;
    transition: all 0.2s ease;

    &:hover {
      color: #495057;
      transform: scale(1.1);
    }
  }

  /* Modal Body - Clean Layout */
  .ant-modal-body {
    padding: 28px 32px;
    background: #fafbfc;
    font-family: "Segoe UI", system-ui, sans-serif;
    line-height: 1.6;
  }

  /* Modal Footer */
  .ant-modal-footer {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 20px 32px;

    .ant-btn {
      height: 40px;
      padding: 0 24px;
      border-radius: 6px;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.2s ease;

      &.ant-btn-primary {
        background: #007bff;
        border-color: #007bff;

        &:hover {
          background: #0056b3;
          border-color: #0056b3;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }
      }
    }
  }
}

/* Status Badge Container */
.status-section {
  text-align: center;
  margin-bottom: 24px;

  .ant-tag {
    font-size: 12px;
    font-weight: 600;
    padding: 6px 16px;
    border-radius: 12px;
    border: 1px solid;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    background: white;

    /* Soft status colors */
    &[color="orange"] {
      background: #fff8e1;
      color: #f57c00;
      border-color: #ffcc02;
    }

    &[color="green"] {
      background: #e8f5e8;
      color: #2e7d32;
      border-color: #81c784;
    }

    &[color="blue"] {
      background: #e3f2fd;
      color: #1976d2;
      border-color: #64b5f6;
    }

    &[color="red"] {
      background: #ffebee;
      color: #d32f2f;
      border-color: #ef5350;
    }

    &[color="default"] {
      background: #f5f5f5;
      color: #616161;
      border-color: #e0e0e0;
    }
  }

  /* Rejection Note Styling */
  .rejection-note {
    text-align: left;
    padding: 16px 20px;
    background: linear-gradient(135deg, #fff2f0 0%, #ffebe8 100%);
    border: 1px solid #ffccc7;
    border-radius: 8px;
    margin-top: 16px;
    box-shadow: 0 2px 8px rgba(255, 77, 79, 0.12);
    border-left: 4px solid #ff4d4f;

    .ant-typography {
      margin: 0;
      line-height: 1.5;
      font-size: 14px;
    }

    
  }
}

/* Information Cards - Medical Theme */
.request-info-card,
.medical-info-card,
.hospital-info-card,
.user-info-card,
.medical-file-card {
  background: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 10px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
  overflow: hidden;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  /* Card Header */
  .ant-card-head {
    background: linear-gradient(135deg, #f1f3f4 0%, #f8f9fa 100%);
    border-bottom: 1px solid #e8eaed;
    padding: 18px 24px;
    min-height: auto;

    .ant-card-head-title {
      font-size: 16px;
      font-weight: 600;
      color: #343a40;
      padding: 0;
      display: flex;
      align-items: center;
      gap: 10px;

      .anticon {
        color: #95a5a6;
        font-size: 16px;
        opacity: 0.8;
      }
    }
  }

  /* Card Body */
  .ant-card-body {
    padding: 24px;
    background: #ffffff;
  }
}

/* Information Field Layout */
.info-field-group {
  margin-bottom: 20px;

  .field-label {
    display: block;
    color: #495057;
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
  }

  .field-content {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px 16px;
    min-height: 44px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;

    &:hover {
      background: #f1f3f4;
      border-color: #ced4da;
    }

    .anticon {
      color: #adb5bd;
      font-size: 14px;
      flex-shrink: 0;
      opacity: 0.7;
    }

    .ant-typography {
      margin: 0;
      color: #343a40;
      font-weight: 500;
      flex: 1;
    }
  }
}

/* Special Tags for Different Information Types */
.priority-tag {
  &.urgent {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    color: white !important;
    font-weight: 600 !important;
  }

  &.normal {
    background: linear-gradient(135deg, #28a745 0%, #218838 100%) !important;
    color: white !important;
    font-weight: 600 !important;
  }
}

.blood-group-tag {
  background: #ffe6e6 !important;
  color: #d73527 !important;
  border: 1px solid #ffcccc !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  padding: 6px 12px !important;
  border-radius: 12px !important;
}

.quantity-tag {
  background: #e8f4fd !important;
  color: #1976d2 !important;
  border: 1px solid #bbdefb !important;
  font-weight: 600 !important;
  padding: 6px 12px !important;
  border-radius: 12px !important;
}

.component-tag {
  background: #f3e5f5 !important;
  color: #7b1fa2 !important;
  border: 1px solid #e1bee7 !important;
  font-weight: 600 !important;
  padding: 6px 12px !important;
  border-radius: 12px !important;
}

/* Medical Report PDF Section */
.pdf-document-section {
  margin-top: 24px;

  .pdf-container {
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    border: 2px solid #cce7ff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
  }

  .pdf-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;

    .pdf-icon-container {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
      box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }

    .pdf-info {
      flex: 1;

      .pdf-title {
        color: #2c3e50;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 4px;
      }

      .pdf-description {
        color: #6c757d;
        font-size: 13px;
        margin: 0;
      }
    }
  }

  .pdf-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;

    .ant-btn {
      height: 42px;
      padding: 0 20px;
      border-radius: 8px;
      font-weight: 500;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.2s ease;

      &.ant-btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        }
      }

      &:not(.ant-btn-primary) {
        background: white;
        border: 1px solid #ced4da;
        color: #495057;

        &:hover {
          border-color: #adb5bd;
          color: #343a40;
          transform: translateY(-1px);
        }
      }
    }
  }
}

/* Contact Information Special Styling */
.contact-phone {
  .field-content {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border-color: #c3e6c3;

    .phone-number {
      background: linear-gradient(135deg, #28a745 0%, #218838 100%);
      color: white;
      padding: 6px 12px;
      border-radius: 16px;
      font-weight: 600;
      font-size: 13px;
      box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3);
    }
  }
}

/* Reason Field Special Styling */
.reason-field {
  .field-content {
    min-height: auto;
    padding: 16px;
    align-items: flex-start;
    background: #f8f9fa;
    border: 1px solid #e9ecef;

    .ant-typography {
      line-height: 1.6;
      color: #495057;
    }
  }
}

/* Loading State */
.loading-state {
  text-align: center;
  padding: 60px 20px;

  .ant-spin .ant-spin-dot i {
    background-color: #007bff;
  }

  .loading-message {
    margin-top: 16px;
    color: #6c757d;
    font-size: 15px;
  }
}

/* Emergency Priority Indicator */
.emergency-priority {
  .medical-info-card {
    border-left: 4px solid #dc3545;

    .ant-card-head {
      background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    }
  }
}

/* Responsive Design */
@media (max-width: 992px) {
  .blood-request-detail-modal {
    .ant-modal-content {
      margin: 16px;
      border-radius: 8px;
    }

    .ant-modal-body {
      padding: 20px 24px;
    }

    .medical-info-card .ant-card-body {
      padding: 20px;
    }
  }
}

@media (max-width: 768px) {
  .blood-request-detail-modal {
    .ant-modal-header {
      padding: 20px 24px 16px;
    }

    .ant-modal-body {
      padding: 16px 20px;
    }

    .pdf-document-section {
      .pdf-header {
        flex-direction: column;
        text-align: center;
        gap: 12px;
      }

      .pdf-actions {
        justify-content: center;

        .ant-btn {
          flex: 1;
          min-width: 120px;
        }
      }
    }
  }
}

/* Smooth Animations */
.blood-request-detail-modal {
  .ant-modal-content {
    animation: modalFadeIn 0.3s ease-out;
  }
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Typography Improvements */
.blood-request-detail-modal {
  font-family: "Segoe UI", "Helvetica Neue", "Arial", sans-serif;

  .ant-typography {
    font-family: inherit;
  }

  h4,
  h5,
  h6 {
    font-family: inherit;
    font-weight: 600;
  }
}

/* Grid System Improvements */
.ant-row {
  margin-left: -8px !important;
  margin-right: -8px !important;
}

.ant-col {
  padding-left: 8px !important;
  padding-right: 8px !important;
}

/* Override Ant Design Tag Colors - Soft Palette */
.blood-request-detail-modal {
  .ant-tag {
    border-radius: 12px !important;

    /* Soft color overrides for all tags */
    &.ant-tag-blue {
      background: #e3f2fd;
      color: #1976d2;
      border-color: #90caf9;
    }

    &.ant-tag-green {
      background: #e8f5e8;
      color: #2e7d32;
      border-color: #81c784;
    }

    &.ant-tag-red {
      background: #ffebee;
      color: #d32f2f;
      border-color: #ef5350;
    }

    &.ant-tag-purple {
      background: #f3e5f5;
      color: #7b1fa2;
      border-color: #ce93d8;
    }

    &.ant-tag-cyan {
      background: #e0f2f1;
      color: #00695c;
      border-color: #4db6ac;
    }

    &.ant-tag-geekblue {
      background: #e8eaf6;
      color: #3f51b5;
      border-color: #9fa8da;
    }

    &.ant-tag-default {
      background: #f5f5f5;
      color: #616161;
      border-color: #e0e0e0;
    }
  }

  /* Medical File Card Specific Styling */
  .medical-file-card {

    .ant-card-head {
      background: linear-gradient(135deg, #fff8f0 0%, #ffffff 100%);
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 24px;

      .ant-card-head-title {
        color: #d4380d;
        font-weight: 600;
        font-size: 16px;

        .anticon {
          color: #ff4d4f;
          margin-right: 8px;
        }
      }
    }

    .ant-card-body {
      padding: 24px;
    }

    .medical-file-content {
      .file-info {
        padding: 16px;
        background: #fafafa;
        border-radius: 8px;
        border: 1px solid #f0f0f0;
        margin-bottom: 16px;
      }

      .file-actions {
        .ant-btn {
          margin-right: 8px;
          border-radius: 6px;
          font-weight: 500;

          &.ant-btn-primary {
            background: #1890ff;
            border-color: #1890ff;

            &:hover {
              background: #40a9ff;
              border-color: #40a9ff;
            }
          }

          &:not(.ant-btn-primary) {
            border-color: #d9d9d9;
            color: #595959;

            &:hover {
              border-color: #40a9ff;
              color: #1890ff;
            }
          }
        }
      }
    }
  }
}
