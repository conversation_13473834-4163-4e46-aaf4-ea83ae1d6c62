@use '../base/variables' as vars;
@use '../base/mixin' as mix;

.scroll-to-top-btn {
  position: fixed;
  bottom: 40px;
  right: 40px;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #2b6cb0 0%, #4299e1 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: vars.$white;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(43, 108, 176, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(43, 108, 176, 0.5);
    background: linear-gradient(135deg, #22548f 0%, #3182ce 100%);
  }

  svg {
    color: vars.$white;
  }

  @include mix.tablet {
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
}