import React, { useState, useEffect } from "react";
import { Spin, Alert, Button, Space } from "antd";
import { ReloadOutlined, DownloadOutlined } from "@ant-design/icons";
import ManagerLayout from "../../components/manager/ManagerLayout";
import WelcomeBanner from "../../components/manager/dashboard/WelcomeBanner";
import StatisticsCards from "../../components/manager/dashboard/StatisticsCards";

import BloodInventoryStatistics from "../../components/common/BloodInventoryStatistics";
import authService from "../../services/authService";
import { getUserName } from "../../utils/userUtils";
import managerDashboardService from "../../services/managerDashboardService";
import excelExportService from "../../services/excelExportService";
import { toast } from "../../utils/toastUtils";
import "../../styles/base/manager-design-system.scss";

const ManagerDashboard = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [exporting, setExporting] = useState(false);
  const [dashboardData, setDashboardData] = useState({
    totalDonors: 0,
    totalRecipients: 0,
    totalBloodUnits: 0,
    totalRequests: 0,
    bloodInventory: [],
    bloodGroupData: [],
    monthlyRequestsData: [],
    notifications: [],
    recentRequests: [],
    criticalInventory: [],
    lowInventory: [],
  });

  useEffect(() => {
    const currentUser = authService.getCurrentUser();
    if (currentUser) {
      setUser(currentUser);
      loadDashboardData();
    }
  }, []);

  // Refresh dashboard data
  const refreshDashboard = () => {
    loadDashboardData();
  };

  // Export dashboard data to Excel
  const handleExportExcel = async () => {
    try {
      setExporting(true);

      const result = excelExportService.exportManagerDashboard(dashboardData);

      if (result.success) {
        toast.success(`Xuất báo cáo Excel thành công: ${result.filename}`);
      } else {
        toast.error(result.error || "Có lỗi xảy ra khi xuất báo cáo Excel");
      }
    } catch (error) {
     
      toast.error("Có lỗi xảy ra khi xuất báo cáo Excel");
    } finally {
      setExporting(false);
    }
  };

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get dashboard data from service
      const result = await managerDashboardService.getDashboardData();

      if (result.success) {
        const data = result.data;
        
        setDashboardData({
          totalDonors: data.totalDonors,
          totalRecipients: data.totalRecipients,
          totalBloodUnits: data.totalBloodUnits,
          totalRequests: data.totalRequests,
          pendingRequests: data.pendingRequests, // Đảm bảo truyền đúng
          bloodInventory: data.bloodInventory,
          bloodGroupData: data.bloodGroupData,
          monthlyRequestsData: data.monthlyRequestsData,
          notifications: [], // Will use default notifications from component
          recentRequests: data.recentRequests,
          criticalInventory: data.criticalInventory,
          lowInventory: data.lowInventory,
        });
      } else {
        throw new Error(result.error || "Không thể tải dữ liệu dashboard");
      }
    } catch (error) {
      
      setError(error.message || "Có lỗi xảy ra khi tải dữ liệu dashboard");
    } finally {
      setLoading(false);
    }
  };

  // Handle loading state
  if (loading) {
    return (
      <ManagerLayout>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            minHeight: "400px",
          }}
        >
          <Spin size="large" />
          <span style={{ marginLeft: 16 }}>Đang tải dữ liệu dashboard...</span>
        </div>
      </ManagerLayout>
    );
  }

  // Handle error state
  if (error) {
    return (
      <ManagerLayout>
        <Alert
          message="Lỗi tải dữ liệu"
          description={error}
          type="error"
          showIcon
          action={
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={loadDashboardData}
            >
              Thử lại
            </Button>
          }
          style={{ marginBottom: 24 }}
        />
      </ManagerLayout>
    );
  }

  if (!user) {
    return <div>Loading...</div>;
  }

  const managerName = getUserName();

  return (
    <ManagerLayout>
      <div className="dashboard-content">
        {/* Welcome Banner */}
        <WelcomeBanner managerName={managerName} />

        {/* Action Buttons */}
        <div style={{ marginBottom: 16, textAlign: "right" }}>
          <Space>
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExportExcel}
              loading={exporting}
              type="primary"
              style={{ backgroundColor: "#52c41a", borderColor: "#52c41a" }}
            >
              Xuất Excel
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={refreshDashboard}
              loading={loading}
            >
              Làm mới dữ liệu
            </Button>
          </Space>
        </div>

        {/* Statistics Cards */}
        <StatisticsCards
          statistics={{
           
            totalDonors: dashboardData.totalDonors,
            totalRequests: dashboardData.totalRequests,
            pendingRequests: dashboardData.pendingRequests,
          }}
        />

        {/* Blood Inventory Statistics */}
        <div style={{ marginBottom: 24 }}>
          <BloodInventoryStatistics />
        </div>

       
      </div>
    </ManagerLayout>
  );
};

export default ManagerDashboard;
