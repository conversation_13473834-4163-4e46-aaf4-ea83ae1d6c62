import { useState, useEffect, useCallback } from "react";
import { bloodRequestService } from "../services/bloodRequestService";
import { isPendingRequest, isCompletedStatus } from "../utils/bloodRequestStatusUtils";
import authService from "../services/authService";
import bloodRequestEvents, { BLOOD_REQUEST_EVENTS } from "../utils/bloodRequestEvents";

/**
 * Custom hook to fetch blood request counts for sidebar display
 * - For blood department doctors: Shows PENDING requests (need action)
 * - For other department doctors: Shows COMPLETED requests (their own requests that are ready)
 * - For managers: Shows APPROVED requests (need "Nhập kho" action)
 * Badge only disappears when there are no more requests to handle, not when viewed
 */
export const useBloodRequestCounts = () => {
  const [counts, setCounts] = useState({
    internal: 0, // Internal requests count
    external: 0, // External requests count
    total: 0, // Total relevant requests
    loading: true,
    error: null,
  });

  const currentUser = authService.getCurrentUser();
  const isBloodDepartment = currentUser?.departmentID === 1;
  const isManager = currentUser?.role === "3"; // STAFF_BLOOD_MANAGER

  // Helper function to determine what requests to count based on user role
  const getRelevantRequests = (allRequests) => {
    if (isManager) {
      // Manager: Show APPROVED requests (status = 1) that need "Nhập kho" action
      return allRequests.filter((request) => request.status === 1);
    } else if (isBloodDepartment) {
      // Blood department doctors: Show PENDING requests that need action
      const internalRequests = allRequests.filter(
        (request) =>
          (request.requestSource === "INTERNAL" ||
            request.source === "internal" ||
            request.userType === "DOCTOR" ||
            !request.requestSource) && // Default to internal if not specified
          isPendingRequest(request)
      );

      const externalRequests = allRequests.filter(
        (request) =>
          (request.requestSource === "EXTERNAL" ||
            request.source === "external" ||
            request.userType === "MEMBER") &&
          isPendingRequest(request)
      );

      return { internal: internalRequests, external: externalRequests };
    } else {
      // Other department doctors: Show COMPLETED requests (their own requests that are ready)
      const doctorId = parseInt(currentUser?.id);
      if (!doctorId) return [];

      return allRequests.filter((request) => {
        const requestUserId = parseInt(request.userId || request.userID);
        const isOwnRequest = requestUserId === doctorId;
        const isCompleted = isCompletedStatus(request.status);
        return isOwnRequest && isCompleted;
      });
    }
  };

  const fetchCounts = useCallback(async () => {
    try {
      setCounts((prev) => ({ ...prev, loading: true, error: null }));

      // Fetch all blood requests
      const response = await bloodRequestService.getAllBloodRequests();

      // Check if the response is successful
      if (!response.success) {
        throw new Error(response.error || "Failed to fetch blood requests");
      }

      const allRequests = response.data || [];

      let relevantRequests = [];
      let internalCount = 0;
      let externalCount = 0;

      const relevantData = getRelevantRequests(allRequests);

      if (isManager) {
        // Manager: Simple count of approved requests
        relevantRequests = relevantData;
        internalCount = relevantRequests.length;
        externalCount = 0;
      } else if (isBloodDepartment) {
        // Blood department doctors: Separate internal/external counts
        internalCount = relevantData.internal.length;
        externalCount = relevantData.external.length;
        relevantRequests = [...relevantData.internal, ...relevantData.external];
      } else {
        // Other department doctors: Simple count of completed requests
        relevantRequests = relevantData;
        internalCount = relevantRequests.length;
        externalCount = 0;
      }

      setCounts({
        internal: internalCount,
        external: externalCount,
        total: relevantRequests.length,
        loading: false,
        error: null,
      });
    } catch (error) {
      console.error("Error fetching blood request counts:", error);
      setCounts((prev) => ({
        ...prev,
        loading: false,
        error: error.message || "Failed to fetch counts",
      }));
    }
  }, [currentUser?.id, isBloodDepartment, isManager]);

  useEffect(() => {
    fetchCounts();

    // Listen for blood request status updates
    const unsubscribeStatusUpdate = bloodRequestEvents.subscribe(
      BLOOD_REQUEST_EVENTS.STATUS_UPDATED,
      (eventData) => {
        console.log('Blood request status updated, refetching counts:', eventData);
        fetchCounts();
      }
    );

    // Listen for blood request creation
    const unsubscribeCreated = bloodRequestEvents.subscribe(
      BLOOD_REQUEST_EVENTS.REQUEST_CREATED,
      (eventData) => {
        console.log('Blood request created, refetching counts:', eventData);
        fetchCounts();
      }
    );

    // Listen for blood request deletion
    const unsubscribeDeleted = bloodRequestEvents.subscribe(
      BLOOD_REQUEST_EVENTS.REQUEST_DELETED,
      (eventData) => {
        console.log('Blood request deleted, refetching counts:', eventData);
        fetchCounts();
      }
    );

    // Optional: Set up polling as fallback (reduced frequency since we have events)
    const interval = setInterval(fetchCounts, 60000); // Update every 60 seconds as fallback

    return () => {
      clearInterval(interval);
      unsubscribeStatusUpdate();
      unsubscribeCreated();
      unsubscribeDeleted();
    };
  }, [fetchCounts]); // Re-fetch when fetchCounts changes

  return {
    ...counts,
    refetch: fetchCounts,
  };
};
