import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Button, 
  Space, 
  Tag, 
  Modal, 
  Form, 
  Input, 
  Select, 
  DatePicker, 
  Tooltip,
  Popconfirm
} from 'antd';
import { toast } from '../../utils/toastUtils';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  PlayCircleOutlined, 
  PauseCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import RemindService from '../../services/remindService';
import dayjs from 'dayjs';

const { Option } = Select;
const { TextArea } = Input;

/**
 * Component quản lý reminders cho Manager
 */
const RemindManagement = () => {
  const [reminders, setReminders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingReminder, setEditingReminder] = useState(null);
  const [form] = Form.useForm();

  const REMINDER_TYPES = {
    appointment_reminder: 'Nhắc nhở lịch hẹn',
    donation_reminder: 'Nhắc nhở hiến máu',
    manual_reminder: 'Nhắc nhở thủ công',
    system_reminder: 'Nhắc nhở hệ thống'
  };

  useEffect(() => {
    loadReminders();
  }, []);

  const loadReminders = async () => {
    setLoading(true);
    try {
      // TODO: Get current user ID from auth service
      const currentUserId = 1; // Placeholder
      const response = await RemindService.getUserReminders(currentUserId);
      setReminders(response.data || []);
    } catch (error) {
      console.error('Error loading reminders:', error);
      toast.error('Không thể tải danh sách nhắc nhở!');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateReminder = () => {
    setEditingReminder(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditReminder = (reminder) => {
    setEditingReminder(reminder);
    form.setFieldsValue({
      ...reminder,
      scheduledTime: reminder.scheduledTime ? dayjs(reminder.scheduledTime) : null
    });
    setModalVisible(true);
  };

  const handleDeleteReminder = async (reminderId) => {
    try {
      await RemindService.deleteReminder(reminderId);
      toast.success('Đã xóa nhắc nhở!');
      loadReminders();
    } catch (error) {
      console.error('Error deleting reminder:', error);
      toast.error('Không thể xóa nhắc nhở!');
    }
  };

  const handleToggleReminder = async (reminder) => {
    try {
      if (reminder.isEnabled) {
        await RemindService.disableReminder(reminder.id);
        toast.success('Đã tắt nhắc nhở!');
      } else {
        await RemindService.enableReminder(reminder.id);
        toast.success('Đã bật nhắc nhở!');
      }
      loadReminders();
    } catch (error) {
      console.error('Error toggling reminder:', error);
      toast.error('Không thể thay đổi trạng thái nhắc nhở!');
    }
  };

  const handleSubmit = async (values) => {
    try {
      const reminderData = {
        ...values,
        scheduledTime: values.scheduledTime?.toISOString(),
        userId: values.userId || 1 // TODO: Get from selected user
      };

      if (editingReminder) {
        // TODO: Add update reminder API
        toast.info('Chức năng cập nhật nhắc nhở đang được phát triển');
      } else {
        await RemindService.createReminder(reminderData);
        toast.success('Đã tạo nhắc nhở thành công!');
      }

      setModalVisible(false);
      loadReminders();
    } catch (error) {
      console.error('Error saving reminder:', error);
      toast.error('Không thể lưu nhắc nhở!');
    }
  };

  const columns = [
    {
      title: 'Tiêu đề',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
    },
    {
      title: 'Loại',
      dataIndex: 'type',
      key: 'type',
      width: 150,
      render: (type) => (
        <Tag color="blue">
          {REMINDER_TYPES[type] || type}
        </Tag>
      ),
    },
    {
      title: 'Người nhận',
      dataIndex: 'userId',
      key: 'userId',
      width: 120,
      render: (userId) => `User ${userId}`,
    },
    {
      title: 'Thời gian gửi',
      dataIndex: 'scheduledTime',
      key: 'scheduledTime',
      width: 180,
      render: (time) => time ? dayjs(time).format('DD/MM/YYYY HH:mm') : 'Chưa đặt',
    },
    {
      title: 'Trạng thái',
      dataIndex: 'isEnabled',
      key: 'isEnabled',
      width: 100,
      render: (isEnabled) => (
        <Tag color={isEnabled ? 'green' : 'red'}>
          {isEnabled ? 'Bật' : 'Tắt'}
        </Tag>
      ),
    },
    {
      title: 'Hành động',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Tooltip title="Chỉnh sửa">
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditReminder(record)}
            />
          </Tooltip>
          <Tooltip title={record.isEnabled ? 'Tắt' : 'Bật'}>
            <Button
              type="default"
              size="small"
              icon={record.isEnabled ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={() => handleToggleReminder(record)}
            />
          </Tooltip>
          <Popconfirm
            title="Bạn có chắc chắn muốn xóa nhắc nhở này?"
            onConfirm={() => handleDeleteReminder(record.id)}
            okText="Xóa"
            cancelText="Hủy"
          >
            <Tooltip title="Xóa">
              <Button
                type="primary"
                danger
                size="small"
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="remind-management">
      <div className="page-header">
        <h2>Quản lý nhắc nhở</h2>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateReminder}
          >
            Tạo nhắc nhở
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadReminders}
            loading={loading}
          >
            Làm mới
          </Button>
        </Space>
      </div>

      <Table
        columns={columns}
        dataSource={reminders}
        rowKey="id"
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `Tổng ${total} nhắc nhở`,
        }}
      />

      <Modal
        title={editingReminder ? 'Chỉnh sửa nhắc nhở' : 'Tạo nhắc nhở mới'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="title"
            label="Tiêu đề"
            rules={[{ required: true, message: 'Vui lòng nhập tiêu đề!' }]}
          >
            <Input placeholder="Nhập tiêu đề nhắc nhở" />
          </Form.Item>

          <Form.Item
            name="message"
            label="Nội dung"
            rules={[{ required: true, message: 'Vui lòng nhập nội dung!' }]}
          >
            <TextArea 
              rows={4} 
              placeholder="Nhập nội dung nhắc nhở"
            />
          </Form.Item>

          <Form.Item
            name="type"
            label="Loại nhắc nhở"
            rules={[{ required: true, message: 'Vui lòng chọn loại nhắc nhở!' }]}
          >
            <Select placeholder="Chọn loại nhắc nhở">
              {Object.entries(REMINDER_TYPES).map(([key, label]) => (
                <Option key={key} value={key}>{label}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="scheduledTime"
            label="Thời gian gửi"
            rules={[{ required: true, message: 'Vui lòng chọn thời gian gửi!' }]}
          >
            <DatePicker
              showTime
              format="DD/MM/YYYY HH:mm"
              placeholder="Chọn thời gian gửi"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="userId"
            label="Người nhận (User ID)"
            rules={[{ required: true, message: 'Vui lòng nhập ID người nhận!' }]}
          >
            <Input 
              type="number" 
              placeholder="Nhập ID người nhận"
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingReminder ? 'Cập nhật' : 'Tạo mới'}
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                Hủy
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default RemindManagement;
