import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Row,
  Col,
  Tag,
  Button,
  Spin,
  Typography,
  Space,
} from "antd";
import {
  UserOutlined,
  DropboxOutlined,
  MedicineBoxOutlined,
  CalendarOutlined,
  PhoneOutlined,
  MailOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
  FilePdfOutlined,
  EyeOutlined,
  PrinterOutlined,
  DownloadOutlined,
} from "@ant-design/icons";
import { DOCTOR_TYPES } from "../../constants/systemConstants";
import authService from "../../services/authService";
import { bloodRequestService } from "../../services/bloodRequestService";
import userInfoService from "../../services/userInfoService";
import { getGenderText } from "../../utils/genderUtils";
import { RequestStatusBadge, UrgencyBadge } from "../common/StatusBadge";
import { useBloodRequestDepartment } from "../../hooks/useBloodRequestDepartment";

const { Text, Title } = Typography;

/**
 * Shared Blood Request Detail Modal
 * Used by both Manager and Doctor with different action buttons
 */
const BloodRequestDetailModal = ({
  request,
  isOpen,
  onClose,
  onUpdate,
  role = "manager", // "manager" or "doctor" (admin removed)
  actions = [], // Custom action buttons
}) => {
  const [requestDetail, setRequestDetail] = useState(null);
  const [detailLoading, setDetailLoading] = useState(false);
  const [userInfo, setUserInfo] = useState(null);
  const [userLoading, setUserLoading] = useState(false);
  const [isFromMember, setIsFromMember] = useState(false);

  const currentUser = authService.getCurrentUser();

  // Use the new hook for department info
  const {
    department: departmentName,
    loading: departmentLoading,
    shouldShowDepartment
  } = useBloodRequestDepartment(requestDetail || request, isFromMember);

  useEffect(() => {
    if (isOpen && (request?.requestId || request?.requestID)) {
      setRequestDetail(null);
      setUserInfo(null);
      setIsFromMember(false);
      loadRequestDetail();
    } else if (!isOpen) {
      setRequestDetail(null);
      setUserInfo(null);
      setIsFromMember(false);
    }
  }, [isOpen, request?.requestId, request?.requestID]);

  const loadRequestDetail = async () => {
    try {
      setDetailLoading(true);

      const requestIdToUse = request.requestId || request.requestID;

      const response = await bloodRequestService.getBloodRequestById(
        requestIdToUse
      );

      if (response.success) {
        if (response.data.requestId == requestIdToUse) {
          setRequestDetail(response.data);
          await determineRequestSource(response.data);
        } else {
          setRequestDetail(request);
          await determineRequestSource(request);
        }
      } else {
        setRequestDetail(request);
        await determineRequestSource(request);
      }
    } catch (error) {
      console.error("Error loading request detail:", error);
      setRequestDetail(request);
      await determineRequestSource(request);
    } finally {
      setDetailLoading(false);
    }
  };

  const determineRequestSource = async (requestData) => {
    try {
      const hasPatientId = requestData.patientId && requestData.patientId !== null && requestData.patientId !== 0;
      const isDoctorRelationship = requestData.relationship === "Bác sĩ phụ trách";
      const isMemberRelationship = [
        "Gia đình", 
        "gia đình", 
        "Bản thân", 
        "bạn bè", 
        "Chính bản thân tôi",
        "Other"
      ].includes(requestData.relationship);
      
      let isFromMember;
      if (isMemberRelationship) {
        isFromMember = true;
      } else if (isDoctorRelationship || hasPatientId) {
        isFromMember = false;
      } else {
        isFromMember = true;
      }
      
      setIsFromMember(isFromMember);
      
      if (isFromMember) {
        const userId = requestData.userID || requestData.userId || requestData.UserID;

        if (userId && userId !== 0) {
          setUserLoading(true);
          try {
            const userResponse = await userInfoService.getUserInfo(userId);
            if (userResponse) {
              setUserInfo(userResponse);
            }
          } catch (error) {
            console.error("Error loading user info:", error);
          } finally {
            setUserLoading(false);
          }
        }
      }
    } catch (error) {
      console.error("Error determining request source:", error);
      
      const isMemberRelationship = [
        "Gia đình", 
        "gia đình", 
        "Bản thân", 
        "bạn bè", 
        "Chính bản thân tôi",
        "Other"
      ].includes(requestData.relationship);
      
      const isFromMember = isMemberRelationship || requestData.relationship !== "Bác sĩ phụ trách";
      setIsFromMember(isFromMember);
    }
  };





  const formatDateTime = (dateString) => {
    if (!dateString) return "Không có";
    try {
      return new Date(dateString).toLocaleString("vi-VN");
    } catch {
      return dateString;
    }
  };

  const displayData = requestDetail || request || {};

  if (!isOpen) return null;

  return (
    <Modal
      title={
        <Space>
          <DropboxOutlined />
          <Title level={4} style={{ margin: 0 }}>
            Chi tiết yêu cầu máu #{displayData.requestId || displayData.requestID || displayData.id}
          </Title>
        </Space>
      }
      open={isOpen}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          Đóng
        </Button>,
        ...actions, // Custom action buttons passed as props
      ]}
      width={900}
      className="blood-request-detail-modal"
    >
      {detailLoading ? (
        <div className="loading-state">
          <Spin size="large" />
          <div className="loading-message">Đang tải chi tiết yêu cầu...</div>
        </div>
      ) : (
        <div className={displayData.priority === 1 ? "emergency-priority" : ""}>
          {/* Status Badge */}
          <div className="status-section">
            <RequestStatusBadge
              status={displayData.status}
              size="default"
            />

            {/* Rejection Note - Show when status is rejected (3) and note exists */}
            {displayData.status === 3 && (() => {
              // Only use note field for doctor's rejection reason, NOT reason field
              const rejectionNote = displayData.note ||
                                   displayData.notes ||
                                   displayData.rejectionReason ||
                                   displayData.rejectReason;

              if (rejectionNote) {
                return (
                  <div className="rejection-note">
                    <Text type="danger" strong>Lý do từ chối: </Text>
                    <Text type="danger">{rejectionNote}</Text>
                  </div>
                );
              }
              return null;
            })()}
          </div>

          {/* Basic Request Information */}
          <Card
            title={
              <Space>
                <DropboxOutlined />
                <span>Thông tin yêu cầu máu</span>
              </Space>
            }
            className="request-info-card"
          >
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <div className="info-field-group">
                  <span className="field-label">Nhóm máu cần</span>
                  <div className="field-content">
                    <Tag className="blood-group-tag">
                      {displayData.bloodTypeDisplay || 
                       `${displayData.bloodGroup || "O"}${displayData.rhType || "+"}`}
                    </Tag>
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div className="info-field-group">
                  <span className="field-label">Số lượng</span>
                  <div className="field-content">
                    <Text strong>{displayData.quantity || 0} ml</Text>
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div className="info-field-group">
                  <span className="field-label">Thành phần máu</span>
                  <div className="field-content">
                    <Text>{displayData.componentType || "Toàn phần"}</Text>
                  </div>
                </div>
              </Col>
              
              <Col span={12}>
                <div className="info-field-group">
                  <span className="field-label">Thời gian tạo</span>
                  <div className="field-content">
                    <CalendarOutlined />
                    <Text style={{ marginLeft: 8 }}>
                      {formatDateTime(displayData.createdTime)}
                    </Text>
                  </div>
                </div>
              </Col>
              <Col span={24}>
                <div className="info-field-group">
                  <span className="field-label">Lý do y tế</span>
                  <div className="field-content">
                    <FileTextOutlined />
                    <Text style={{ marginLeft: 8 }}>
                      {displayData.reason || "Không có"}
                    </Text>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>

          {/* Medical File Section - Show if medicalFile or medicalReportUrl exists */}
          {(displayData.medicalFile || displayData.medicalReportUrl) && (
            <Card
              title={
                <Space>
                  <FilePdfOutlined />
                  <span>Hồ sơ y tế đính kèm</span>
                </Space>
              }
              className="medical-file-card"
            >
              <div className="medical-file-content">
                <Space direction="vertical" size="middle" style={{ width: "100%" }}>
                  <div className="file-info">
                    <Space>
                      <FilePdfOutlined style={{ fontSize: "24px", color: "#ff4d4f" }} />
                      <div>
                        <Text strong>Hồ sơ y tế</Text>
                        <br />
                        <Text type="secondary">File PDF đính kèm</Text>
                      </div>
                    </Space>
                  </div>

                  <div className="file-actions">
                    <Space>
                      <Button
                        type="primary"
                        icon={<EyeOutlined />}
                        onClick={() => {
                          const fileUrl = displayData.medicalReportUrl || displayData.medicalFile;
                          if (fileUrl) {
                            window.open(fileUrl, '_blank');
                          }
                        }}
                      >
                        Xem file
                      </Button>

                     

                      <Button
                        icon={<DownloadOutlined />}
                        onClick={() => {
                          const fileUrl = displayData.medicalReportUrl || displayData.medicalFile;
                          if (fileUrl) {
                            const link = document.createElement('a');
                            link.href = fileUrl;
                            link.download = `ho-so-y-te-${displayData.requestId || displayData.requestID}.pdf`;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                          }
                        }}
                      >
                        Tải xuống
                      </Button>
                    </Space>
                  </div>
                </Space>
              </div>
            </Card>
          )}

          {/* Patient Information */}
          <Card
            title={
              <Space>
                <UserOutlined />
                <span>Thông tin bệnh nhân</span>
              </Space>
            }
            className="medical-info-card"
          >
            <Row gutter={[16, 16]}>
              {!isFromMember && (
                <Col span={12}>
                  <div className="info-field-group">
                    <span className="field-label">Mã bệnh nhân</span>
                    <div className="field-content">
                      <InfoCircleOutlined />
                      <Text>{displayData.patientId || "Không có"}</Text>
                    </div>
                  </div>
                </Col>
              )}
              <Col span={12}>
                <div className="info-field-group">
                  <span className="field-label">Tên bệnh nhân</span>
                  <div className="field-content">
                    <UserOutlined />
                    <Text strong>{displayData.patientName || "Không có"}</Text>
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div className="info-field-group">
                  <span className="field-label">Tuổi</span>
                  <div className="field-content">
                    <Tag color="blue">{displayData.age || "N/A"} tuổi</Tag>
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div className="info-field-group">
                  <span className="field-label">Giới tính</span>
                  <div className="field-content">
                    <Tag color="purple">
                      {getGenderText(displayData.gender)}
                    </Tag>
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div className="info-field-group">
                  <span className="field-label">Mối quan hệ</span>
                  <div className="field-content">
                    <Text>{displayData.relationship || "Không có"}</Text>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>

          {/* Hospital Information */}
          <Card
            title={
              <Space>
                <MedicineBoxOutlined />
                <span>Thông tin bệnh viện</span>
              </Space>
            }
            className="hospital-info-card"
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div className="info-field-group">
                  <span className="field-label">Tên bác sĩ</span>
                  <div className="field-content">
                    <UserOutlined />
                    <Text strong>{displayData.doctorName || "Không có"}</Text>
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div className="info-field-group">
                  <span className="field-label">Số điện thoại bác sĩ</span>
                  <div className="field-content">
                    <PhoneOutlined />
                    {displayData.doctorPhone && displayData.doctorPhone !== "Không có" ? (
                      <a href={`tel:${displayData.doctorPhone}`}>
                        <Text strong style={{ color: '#1890ff' }}>{displayData.doctorPhone}</Text>
                      </a>
                    ) : (
                      <Text>{displayData.doctorPhone || "Không có"}</Text>
                    )}
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div className="info-field-group">
                  <span className="field-label">Tên bệnh viện</span>
                  <div className="field-content">
                    <MedicineBoxOutlined />
                    <Text strong>{displayData.facilityName || "Không có"}</Text>
                  </div>
                </div>
              </Col>
              {shouldShowDepartment && (
                <Col span={12}>
                  <div className="info-field-group">
                    <span className="field-label">Khoa</span>
                    <div className="field-content">
                      <MedicineBoxOutlined />
                      <Text strong style={{ marginLeft: 8, color: '#1890ff' }}>
                        {departmentLoading ? "Đang tải..." : departmentName}
                      </Text>
                    </div>
                  </div>
                </Col>
              )}
            </Row>
          </Card>

          {isFromMember && (
            <Card
              title={
                <Space>
                  <UserOutlined />
                  <span>Thông tin người điền form</span>
                </Space>
              }
              className="user-info-card"
            >
              {userLoading ? (
                <div className="loading-state">
                  <Spin />
                  <div className="loading-message">Đang tải thông tin người dùng...</div>
                </div>
              ) : userInfo ? (
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <div className="info-field-group">
                      <span className="field-label">Họ và tên</span>
                      <div className="field-content">
                        <UserOutlined />
                        <Text strong style={{ marginLeft: 8 }}>
                          {userInfo.name || userInfo.fullName || "Không có"}
                        </Text>
                      </div>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div className="info-field-group">
                      <span className="field-label">Số điện thoại</span>
                      <div className="field-content">
                        <PhoneOutlined />
                        <Text style={{ marginLeft: 8 }}>{userInfo.phone || "Không có"}</Text>
                      </div>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div className="info-field-group">
                      <span className="field-label">Email</span>
                      <div className="field-content">
                        <MailOutlined />
                        <Text style={{ marginLeft: 8 }}>{userInfo.email || "Không có"}</Text>
                      </div>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div className="info-field-group">
                      <span className="field-label">CCCD/CMND</span>
                      <div className="field-content">
                        <Text>{userInfo.idCard || "Không có"}</Text>
                      </div>
                    </div>
                  </Col>
                  
                  <Col span={12}>
                    <div className="info-field-group">
                      <span className="field-label">Tuổi</span>
                      <div className="field-content">
                        <Tag color="geekblue">{userInfo.age || "N/A"} tuổi</Tag>
                      </div>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div className="info-field-group">
                      <span className="field-label">Giới tính</span>
                      <div className="field-content">
                        <Tag color="purple">
                          {getGenderText(userInfo.gender)}
                        </Tag>
                      </div>
                    </div>
                  </Col>
                  
                  <Col span={24}>
                    <div className="info-field-group">
                      <span className="field-label">Địa chỉ</span>
                      <div className="field-content">
                        <Text>
                          {userInfo.address ||
                            `${userInfo.ward || ""} ${
                              userInfo.district || ""
                            } ${userInfo.city || ""}`.trim() ||
                            "Không có"}
                        </Text>
                      </div>
                    </div>
                  </Col>
                </Row>
              ) : (
                <div className="error-state">
                  <InfoCircleOutlined />
                  <div>Không thể tải thông tin người dùng</div>
                </div>
              )}
            </Card>
          )}
        </div>
      )}
    </Modal>
  );
};

export default BloodRequestDetailModal;
