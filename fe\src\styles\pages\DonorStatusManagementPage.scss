@use "../base/variables" as vars;

.donor-status-management {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .donor-status-content {
    flex: 1;
    margin-left: 280px;
    padding: 2rem;
    transition: margin-left 0.3s ease;

    .page-header {
      margin-bottom: 2rem;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      padding: 2rem;
      border-radius: 24px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .header-content {
        h1 {
          background: linear-gradient(135deg, #667eea, #764ba2);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-bottom: 0.5rem;
          font-size: 2.5rem;
          font-weight: 700;
        }

        p {
          color: #6c757d;
          font-size: 1.2rem;
          margin: 0;
          font-weight: 500;
        }
      }

      .btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 16px;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &.btn-primary {
          background: linear-gradient(135deg, #667eea, #764ba2);
          color: white;
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }
    }

    .stats-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;

      .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        padding: 2rem;
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        text-align: center;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 20px 45px rgba(0, 0, 0, 0.15);
        }

        .stat-number {
          font-size: 2.5rem;
          font-weight: 800;
          margin: 0 0 0.5rem 0;
          background: linear-gradient(135deg, #667eea, #764ba2);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .stat-label {
          color: #6c757d;
          font-size: 1rem;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        &.registered {
          border-left: 4px solid #ffc107;
        }

        &.screening {
          border-left: 4px solid #17a2b8;
        }

        &.donated {
          border-left: 4px solid #fd7e14;
        }

        &.completed {
          border-left: 4px solid #28a745;
        }
      }
    }

    .donors-section {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 24px;
      padding: 2rem;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);

      h2 {
        background: linear-gradient(135deg, #667eea, #764ba2);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 1.5rem;
        font-size: 1.8rem;
        font-weight: 700;
      }

      .loading-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;

        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 4px solid rgba(102, 126, 234, 0.3);
          border-top: 4px solid #667eea;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 1rem;
        }

        p {
          font-size: 1.1rem;
          margin: 0;
        }
      }

      .empty-state {
        text-align: center;
        padding: 4rem;
        color: #6c757d;

        .empty-icon {
          font-size: 5rem;
          display: block;
          margin-bottom: 1.5rem;
        }

        h3 {
          margin: 0 0 1rem 0;
          color: #495057;
          font-size: 1.5rem;
        }

        p {
          margin: 0;
          font-size: 1.1rem;
          line-height: 1.5;
        }
      }

      .donors-table-container {
        overflow-x: auto;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

        .donors-table {
          width: 100%;
          border-collapse: collapse;
          background: white;

          thead {
            background: linear-gradient(135deg, #667eea, #764ba2);

            th {
              padding: 1rem;
              text-align: left;
              font-weight: 700;
              color: white;
              font-size: 0.9rem;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              border: none;

              &:first-child {
                border-radius: 12px 0 0 0;
              }

              &:last-child {
                border-radius: 0 12px 0 0;
              }
            }
          }

          tbody {
            tr {
              transition: all 0.3s ease;
              border-bottom: 1px solid rgba(0, 0, 0, 0.05);

              &:hover {
                background: rgba(102, 126, 234, 0.05);
                transform: translateY(-1px);
              }

              &:last-child {
                border-bottom: none;
              }

              td {
                padding: 1rem;
                vertical-align: middle;

                .donor-id {
                  font-weight: 700;
                  color: #667eea;
                  font-family: "Courier New", monospace;
                }

                .donor-info {
                  .name {
                    font-weight: 600;
                    color: #495057;
                    margin-bottom: 0.25rem;
                  }

                  .contact {
                    font-size: 0.9rem;
                    color: #6c757d;
                  }
                }

                .blood-type-badge {
                  background: linear-gradient(135deg, #dc3545, #fd7e14);
                  color: white;
                  padding: 0.5rem 0.75rem;
                  border-radius: 8px;
                  font-weight: 700;
                  font-size: 0.9rem;
                  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
                }

                .time-slot {
                  background: rgba(102, 126, 234, 0.1);
                  color: #667eea;
                  padding: 0.5rem 0.75rem;
                  border-radius: 8px;
                  font-weight: 600;
                  font-size: 0.9rem;
                }

                .status-badge {
                  color: white;
                  padding: 0.5rem 0.75rem;
                  border-radius: 8px;
                  font-weight: 700;
                  font-size: 0.9rem;
                  display: inline-block;
                }

                .btn {
                  padding: 0.5rem 1rem;
                  border: none;
                  border-radius: 8px;
                  font-weight: 600;
                  cursor: pointer;
                  transition: all 0.3s ease;
                  font-size: 0.9rem;

                  &.btn-primary {
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    color: white;
                    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

                    &:hover {
                      transform: translateY(-2px);
                      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
                    }
                  }

                  &.btn-sm {
                    padding: 0.375rem 0.75rem;
                    font-size: 0.8rem;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .update-modal {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 24px;
      max-width: 800px;
      width: 95%;
      max-height: 90vh;
      overflow-y: auto;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);

      .modal-header {
        padding: 1.5rem 2rem;
        background: linear-gradient(
          135deg,
          rgba(102, 126, 234, 0.1),
          rgba(118, 75, 162, 0.1)
        );
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 24px 24px 0 0;

        h3 {
          margin: 0;
          background: linear-gradient(135deg, #667eea, #764ba2);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-size: 1.3rem;
          font-weight: 700;
        }

        .close-btn {
          background: linear-gradient(135deg, #dc3545, #c82333);
          border: none;
          color: white;
          font-size: 1.5rem;
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          &:hover {
            transform: rotate(90deg) scale(1.1);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
          }
        }
      }

      .modal-body {
        padding: 2rem;

        .donor-summary {
          background: rgba(102, 126, 234, 0.05);
          padding: 1.5rem;
          border-radius: 12px;
          margin-bottom: 2rem;
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 1rem;

          .summary-item {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;

            label {
              font-weight: 600;
              color: #6c757d;
              font-size: 0.9rem;
              text-transform: uppercase;
              letter-spacing: 0.5px;
            }

            span {
              font-weight: 700;
              color: #495057;

              &.blood-type-badge {
                background: linear-gradient(135deg, #dc3545, #fd7e14);
                color: white;
                padding: 0.5rem 0.75rem;
                border-radius: 8px;
                font-size: 0.9rem;
                display: inline-block;
                width: fit-content;
              }

              &.status-badge {
                color: white;
                padding: 0.5rem 0.75rem;
                border-radius: 8px;
                font-size: 0.9rem;
                display: inline-block;
                width: fit-content;
              }
            }
          }
        }

        .form-section {
          margin-bottom: 2rem;

          label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #495057;
          }

          select,
          textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;

            &:focus {
              outline: none;
              border-color: #667eea;
              box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            }
          }
        }

        .health-check-section {
          background: rgba(102, 126, 234, 0.05);
          padding: 1.5rem;
          border-radius: 12px;
          margin-bottom: 2rem;

          h4 {
            margin: 0 0 1rem 0;
            color: #667eea;
            font-weight: 700;
          }

          .health-form {
            .form-row {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 1rem;
              margin-bottom: 1rem;
            }

            .form-group {
              label {
                font-size: 0.9rem;
                margin-bottom: 0.25rem;
              }

              input {
                width: 100%;
                padding: 0.5rem;
                border: 2px solid rgba(102, 126, 234, 0.2);
                border-radius: 6px;
                font-size: 0.9rem;

                &:focus {
                  outline: none;
                  border-color: #667eea;
                  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }
              }
            }
          }
        }
      }

      .modal-footer {
        padding: 1.5rem 2rem;
        background: rgba(102, 126, 234, 0.05);
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        border-radius: 0 0 24px 24px;

        .btn {
          padding: 0.75rem 1.5rem;
          border: none;
          border-radius: 8px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;

          &.btn-secondary {
            background: #6c757d;
            color: white;

            &:hover {
              background: #5a6268;
              transform: translateY(-1px);
            }
          }

          &.btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            }
          }
        }
      }
    }
  }
}

// Animations
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Responsive
@media (max-width: 768px) {
  .donor-status-management {
    .donor-status-content {
      margin-left: 0;
      padding: 1rem;

      .page-header {
        padding: 1.5rem;
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;

        .header-content h1 {
          font-size: 2rem;
        }
      }

      .stats-section {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;

        .stat-card {
          padding: 1.5rem;

          .stat-number {
            font-size: 2rem;
          }
        }
      }

      .donors-section {
        padding: 1.5rem;

        .donors-table-container {
          .donors-table {
            font-size: 0.9rem;

            th,
            td {
              padding: 0.75rem 0.5rem;
            }
          }
        }
      }
    }

    .modal-overlay .update-modal {
      width: 98%;
      max-height: 95vh;

      .modal-body {
        padding: 1.5rem;

        .donor-summary {
          grid-template-columns: 1fr;
        }

        .health-check-section .health-form .form-row {
          grid-template-columns: 1fr;
        }
      }

      .modal-footer {
        flex-direction: column;

        .btn {
          width: 100%;
        }
      }
    }
  }
}
