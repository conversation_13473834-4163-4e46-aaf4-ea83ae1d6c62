import React, { useState, useCallback } from "react";
import { apiClient } from "../services/axiosInstance";

const useRequest = (apiFn, deps = [], options = {}) => {
  const { forceArray = false } = options;
  const [data, setData] = useState(forceArray ? [] : null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const requestData = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      let result;
      if (typeof apiFn === "string") {
        // Nếu truyền vào là url string, tự động dùng apiClient.get
        const res = await apiClient.get(apiFn);
        result = res.data;
      } else {
        // Nếu là function, gọi nh<PERSON> cũ (có thể trả về promise từ axios)
        result = await apiFn();
        // Nếu trả về response của axios, lấy .data
        if (result && result.data !== undefined) result = result.data;
      }

      // Set data based on options
      if (forceArray) {
        setData(Array.isArray(result) ? result : []);
      } else {
        setData(result);
      }
    } catch (err) {
      setError(err?.message || "Đã xảy ra lỗi khi tải dữ liệu");
    } finally {
      setLoading(false);
    }
  }, deps);

  // Tự động fetch khi deps thay đổi
  React.useEffect(() => {
    requestData();
  }, deps);

  return { data, loading, error, refetch: requestData };
};

export default useRequest;
