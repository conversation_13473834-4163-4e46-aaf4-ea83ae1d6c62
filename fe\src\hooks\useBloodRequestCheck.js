import { useState, useCallback } from "react";
import bloodRequestService from "../services/bloodRequestService";
import authService from "../services/authService";

const useBloodRequestCheck = () => {
  const [isChecking, setIsChecking] = useState(false);
  const [pendingRequest, setPendingRequest] = useState(null);
  const [error, setError] = useState(null);

  const checkPendingRequest = useCallback(async () => {
    const currentUser = authService.getCurrentUser();

    if (!currentUser?.id) {
      console.error("No current user found");
      return {
        success: false,
        hasPendingRequest: false,
        error: "Không tìm thấy thông tin người dùng",
      };
    }

    try {
      setIsChecking(true);
      setError(null);

      const result = await bloodRequestService.checkUserPendingRequest(
        currentUser.id
      );

      if (result.success) {
        if (result.hasPendingRequest) {
          setPendingRequest(result.pendingRequest);
          return {
            success: true,
            hasPendingRequest: true,
            pendingRequest: result.pendingRequest,
            message: result.message,
          };
        } else {
          setPendingRequest(null);
          return {
            success: true,
            hasPendingRequest: false,
            message: "Có thể tạo đơn đăng ký mới",
          };
        }
      } else {
        setError(result.error);
        return {
          success: false,
          hasPendingRequest: false,
          error: result.error,
        };
      }
    } catch (error) {
     
      const errorMessage = "Có lỗi xảy ra khi kiểm tra trạng thái đơn đăng ký";
      setError(errorMessage);
      return {
        success: false,
        hasPendingRequest: false,
        error: errorMessage,
      };
    } finally {
      setIsChecking(false);
    }
  }, []);

  /**
   * Clear pending request state
   */
  const clearPendingRequest = useCallback(() => {
    setPendingRequest(null);
    setError(null);
  }, []);

  return {
    isChecking,
    pendingRequest,
    error,
    checkPendingRequest,
    clearPendingRequest,
  };
};

export default useBloodRequestCheck;
