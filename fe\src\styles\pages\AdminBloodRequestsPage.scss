// Admin Blood Requests Page Styles
// Extends shared BloodRequestPageHeader styles

@use "../base/variables" as *;
@use "../base/mixin" as *;
@use "../base/admin-design-system.scss" as admin;

.admin-blood-requests-content {
  padding: $spacing-lg;
  max-width: 1400px;
  margin: 0 auto;
  background: $background-main;
  min-height: calc(100vh - 64px);

  // Ensure shared header styles work with admin theme
  .blood-request-page-header.admin-page-header {
    background: white;
    border: 1px solid rgba($primary-color, 0.15);
    box-shadow: 0 2px 8px rgba($primary-color, 0.08);

    .header-icon {
      background: linear-gradient(
        135deg,
        $primary-color 0%,
        $primary-hover 100%
      );
      box-shadow: 0 4px 12px rgba($primary-color, 0.25);

      &:hover {
        box-shadow: 0 6px 16px rgba($primary-color, 0.35);
      }
    }

    .header-title {
      color: $text-primary !important;
      font-weight: 600;
    }

    .header-description {
      color: $text-secondary !important;
    }

    .header-actions {
      .ant-btn {
        &:not(.ant-btn-primary) {
          border-color: rgba($primary-color, 0.3);
          color: $primary-color;

          &:hover {
            border-color: $primary-color;
            color: $primary-color;
            background: rgba($primary-color, 0.05);
          }
        }

        &.ant-btn-primary {
          background: $primary-color;
          border-color: $primary-color;

          &:hover {
            background: $primary-hover;
            border-color: $primary-hover;
          }
        }
      }
    }
  }

  // Tabs styling for admin theme
  .ant-tabs {
    .ant-tabs-tab {
      color: $text-secondary;
      font-weight: 500;

      &:hover {
        color: $primary-color;
      }

      &.ant-tabs-tab-active {
        color: $primary-color;

        .ant-tabs-tab-btn {
          color: $primary-color;
          font-weight: 600;
        }
      }
    }

    .ant-tabs-ink-bar {
      background: $primary-color;
    }
  }

  // Statistics cards for admin theme
  .ant-card {
    border: 1px solid rgba($primary-color, 0.1);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba($primary-color, 0.05);

    .ant-card-head {
      border-bottom-color: rgba($primary-color, 0.1);
    }

    .ant-statistic-title {
      color: $text-secondary;
      font-weight: 500;
    }

    .ant-statistic-content-value {
      color: $text-primary;
      font-weight: 600;
    }
  }

  // Filters section
  .doctor-blood-requests-filters {
    background: white;
    border: 1px solid rgba($primary-color, 0.1);
    border-radius: 8px;
    padding: $spacing-lg;
    margin-bottom: $spacing-lg;
    box-shadow: 0 2px 4px rgba($primary-color, 0.05);

    .ant-form-item-label > label {
      color: $text-primary;
      font-weight: 500;
    }

    .ant-select,
    .ant-input {
      border-color: rgba($primary-color, 0.2);

      &:hover {
        border-color: $primary-color;
      }

      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
      }
    }
  }

  // Table styling for admin theme
  .ant-table {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba($primary-color, 0.05);

    .ant-table-thead > tr > th {
      background: rgba($primary-color, 0.05);
      color: $text-primary;
      font-weight: 600;
      border-bottom: 2px solid rgba($primary-color, 0.1);
    }

    .ant-table-tbody > tr {
      &:hover > td {
        background: rgba($primary-color, 0.02);
      }

      > td {
        border-bottom-color: rgba($primary-color, 0.08);
      }
    }

    .ant-btn {
      &.ant-btn-link {
        color: $primary-color;

        &:hover {
          color: $primary-hover;
        }
      }
    }

    .ant-tag {
      border-radius: 12px;
      font-weight: 500;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: $spacing-md;

    .blood-request-page-header.admin-page-header {
      padding: $spacing-md;
      margin-bottom: $spacing-md;
    }

    .doctor-blood-requests-filters {
      padding: $spacing-md;
    }
  }

  @media (max-width: 480px) {
    padding: $spacing-sm;

    .blood-request-page-header.admin-page-header {
      padding: $spacing-sm;

      .header-info .header-title-section {
        gap: $spacing-sm;

        .header-icon {
          width: 36px;
          height: 36px;
          font-size: 18px;
        }

        .header-text {
          .header-title {
            font-size: 1.2rem;
          }

          .header-description {
            font-size: 0.8rem;
          }
        }
      }
    }
  }
}

// Loading states for admin theme
.admin-blood-requests-content {
  .ant-spin-container {
    .ant-spin {
      .ant-spin-dot-item {
        background-color: $primary-color;
      }
    }
  }

  .ant-empty {
    .ant-empty-description {
      color: $text-secondary;
    }
  }
}

// Dark mode support (future enhancement)
@media (prefers-color-scheme: dark) {
  .admin-blood-requests-content {
    background: $dark-blue;

    .blood-request-page-header.admin-page-header {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba($primary-color, 0.3);

      .header-title {
        color: rgba(255, 255, 255, 0.9) !important;
      }

      .header-description {
        color: rgba(255, 255, 255, 0.7) !important;
      }
    }

    .ant-card {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba($primary-color, 0.2);
    }

    .doctor-blood-requests-filters {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba($primary-color, 0.2);
    }

    .ant-table {
      background: rgba(255, 255, 255, 0.1);

      .ant-table-thead > tr > th {
        background: rgba($primary-color, 0.15);
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }
}
