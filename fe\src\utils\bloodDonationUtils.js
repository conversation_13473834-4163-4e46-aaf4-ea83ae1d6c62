
export const calculateAge = (dateOfBirth) => {
  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }

  return age;
};


export const isEligibleAge = (dateOfBirth) => {
  const age = calculateAge(dateOfBirth);
  return age >= 18 && age <= 60;
};


export const isEligibleWeight = (weight, gender) => {
  const minWeight = gender === "female" ? 42 : 45;
  return parseFloat(weight) >= minWeight;
};


export const getMinWeight = (gender) => {
  return gender === "female" ? 42 : 45;
};


export const checkEligibility = (personalInfo, healthSurvey) => {
  const {
    weight,
    hasCurrentMedicalConditions,
    hasPreviousSeriousConditions,
    otherPreviousConditions,
    hadMalariaSyphilisTuberculosis,
    hadBloodTransfusion,
    hadVaccination,
    last12MonthsNone,
    hadTyphoidSepsis,
    unexplainedWeightLoss,
    persistentLymphNodes,
    invasiveMedicalProcedures,
    tattoosPiercings,
    drugUse,
    bloodExposure,
    livedWithHepatitisB,
    sexualContactWithInfected,
    sameSexContact,
    last6MonthsNone,
    hadUrinaryInfection,
    visitedEpidemicArea,
    last1MonthNone,
    hadFluSymptoms,
    last14DaysNone,
    otherSymptoms,
    tookAntibiotics,
    last7DaysNone,
    otherMedications,
    isPregnantOrNursing,
    hadPregnancyTermination,
    womenQuestionsNone,
  } = healthSurvey;

  // Age check
  const age = calculateAge(personalInfo.dateOfBirth);
  if (age < 18 || age > 60) {
    return {
      eligible: false,
      reason: "Tuổi không đủ điều kiện (18-60 tuổi)",
    };
  }

  // Weight check based on gender
  const minWeight = personalInfo.gender === "female" ? 42 : 45;
  if (parseFloat(weight) < minWeight) {
    return { eligible: false, reason: `Cân nặng dưới ${minWeight}kg` };
  }

  // Question 2: Current Medical Conditions
  if (hasCurrentMedicalConditions === true) {
    return { eligible: false, reason: "Có bệnh lý hiện tại" };
  }

  // Question 3: Previous Serious Conditions
  if (
    hasPreviousSeriousConditions === true ||
    hasPreviousSeriousConditions === "other"
  ) {
    return { eligible: false, reason: "Có tiền sử bệnh nghiêm trọng" };
  }

  // Question 4: Last 12 Months
  if (
    !last12MonthsNone &&
    (hadMalariaSyphilisTuberculosis || hadBloodTransfusion || hadVaccination)
  ) {
    return { eligible: false, reason: "Có yếu tố rủi ro trong 12 tháng qua" };
  }

  // Question 5: Last 6 Months
  if (
    !last6MonthsNone &&
    (hadTyphoidSepsis ||
      unexplainedWeightLoss ||
      persistentLymphNodes ||
      invasiveMedicalProcedures ||
      tattoosPiercings ||
      drugUse ||
      bloodExposure ||
      livedWithHepatitisB ||
      sexualContactWithInfected ||
      sameSexContact)
  ) {
    return { eligible: false, reason: "Có yếu tố rủi ro trong 6 tháng qua" };
  }

  // Question 6: Last 1 Month
  if (!last1MonthNone && (hadUrinaryInfection || visitedEpidemicArea)) {
    return { eligible: false, reason: "Có yếu tố rủi ro trong 1 tháng qua" };
  }

  // Question 7: Last 14 Days
  if (!last14DaysNone && (hadFluSymptoms || otherSymptoms)) {
    return {
      eligible: false,
      reason: "Có triệu chứng bệnh trong 14 ngày qua",
    };
  }

  // Question 8: Last 7 Days
  if (!last7DaysNone && (tookAntibiotics || otherMedications)) {
    return { eligible: false, reason: "Đã sử dụng thuốc trong 7 ngày qua" };
  }

  // Question 9: Women Only
  if (
    personalInfo.gender === "female" &&
    !womenQuestionsNone &&
    (isPregnantOrNursing || hadPregnancyTermination)
  ) {
    return { eligible: false, reason: "Không đủ điều kiện về thai sản" };
  }

  return { eligible: true, reason: "" };
};


export const validatePersonalInfo = (personalInfo) => {
  const errors = [];

  if (!personalInfo.fullName?.trim()) {
    errors.push("Vui lòng nhập họ và tên");
  }

  if (!personalInfo.phone?.trim()) {
    errors.push("Vui lòng nhập số điện thoại");
  }

  if (!personalInfo.dateOfBirth) {
    errors.push("Vui lòng chọn ngày sinh");
  }

  if (!personalInfo.gender) {
    errors.push("Vui lòng chọn giới tính");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};


export const formatBloodType = (bloodGroup, rhType) => {
  if (!bloodGroup || !rhType) return "";

  // Chuẩn hóa ký hiệu Rh
  const rhSymbol =
    rhType === "Rh+" || rhType === "+"
      ? "+"
      : rhType === "Rh-" || rhType === "-"
        ? "-"
        : rhType;

  return `${bloodGroup}${rhSymbol}`;
};


export const createFullAddress = (addressComponents) => {
  const {
    address,
    houseNumber,
    street,
    wardName,
    districtName,
    provinceName,
  } = addressComponents;

  // Tạo địa chỉ cơ bản
  const baseAddress = address || `${houseNumber || ''} ${street || ''}`.trim();

  // Kiểm tra xem baseAddress đã chứa thông tin địa chỉ đầy đủ chưa
  const isFullAddressInBase = baseAddress && (
    (wardName && baseAddress.includes(wardName)) ||
    (districtName && baseAddress.includes(districtName)) ||
    (provinceName && baseAddress.includes(provinceName))
  );

  if (isFullAddressInBase) {
    // Nếu baseAddress đã chứa thông tin đầy đủ, chỉ sử dụng nó
    return baseAddress;
  } else {
    // Nếu chưa, kết hợp các thành phần
    return [baseAddress, wardName, districtName, provinceName]
      .filter(Boolean)
      .join(", ");
  }
};


export const validateAppointmentData = (appointmentData, healthSurvey) => {
  const errors = [];

  if (!appointmentData.preferredDate) {
    errors.push("Vui lòng chọn ngày đặt lịch");
  }

  if (!appointmentData.timeSlot) {
    errors.push("Vui lòng chọn khung giờ đặt lịch");
  }

  if (!healthSurvey.weight) {
    errors.push("Vui lòng nhập cân nặng");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};
