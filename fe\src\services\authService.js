import { API_ROLES, DOCTOR_TYPES, DEPARTMENT_IDS } from "../constants/systemConstants";
import { apiClient } from "./axiosInstance";
import config from "../config/environment";
import { clearSensitiveDataForNonPrivilegedUsers, clearAllSensitiveData } from "../utils/securityUtils";

const AUTH_API = config.api.auth;
const INFORMATION_API = config.api.information;
const GOOGLE_LOGIN_API = config.api.googleLogin;
const GOOGLE_CALLBACK_API = config.api.googleCallback;



// Authentication service for managing user sessions
class AuthService {
  constructor() {
    this.currentUser = null;
    this.isAuthenticated = false;
    this.loadUserFromStorage();
 
  }

  
  loadUserFromStorage() {
    try {
      
      const userData = localStorage.getItem("currentUser");
      
      if (userData) {
        this.currentUser = JSON.parse(userData);
        this.isAuthenticated = true;
        
        // Clear sensitive data if user doesn't have admin/manager privileges
        clearSensitiveDataForNonPrivilegedUsers(this.currentUser.role);
       
      } else {
       
        this.currentUser = null;
        this.isAuthenticated = false;
        // Clear all sensitive data when no user is logged in
        clearAllSensitiveData();
      }
    } catch (error) {
      
      this.logout();
    }
  }

  // Save user to localStorage
  saveUserToStorage(user) {
    try {
      localStorage.setItem("currentUser", JSON.stringify(user));
    } catch (error) {
      console.error("Error saving user to storage:", error);
    }
  }

  // Login with email and password
  async login(email, password) {
    try {

      const endpoint = AUTH_API ? `${AUTH_API}/login` : '/Auth/login';

      const response = await apiClient.post(endpoint, {
        email: email,
        password: password,
      });

      if (response.status === 200 && response.data) {
        const token = response.data;
        if (!token) {
          return {
            success: false,
            error: "Không nhận được token xác thực",
          };
        }

        // Use the common token processing method
        return await this.processAuthToken(token);
      } else {
        return {
          success: false,
          error: response.data?.message || "Đăng nhập không thành công",
        };
      }
    } catch (error) {
      console.error("Login error:", error);

      if (error.response) {
       
        let errorMessage;
        let errorType = "unknown";
        const status = error.response.status;
        const responseData = error.response.data;

        if (status === 500) {
          errorMessage = "Máy chủ đang gặp sự cố. Vui lòng thử lại sau.";
          errorType = "server_error";
        } else if (status === 400) {
          // 400 Bad Request - Invalid credentials (wrong email/password)
          errorMessage = "Email hoặc mật khẩu không đúng. Vui lòng kiểm tra lại thông tin đăng nhập.";
          errorType = "invalid_credentials";
        } else if (status === 401) {
          // 401 Unauthorized - Account suspended/inactive (status = 0)
          errorMessage = "Tài khoản đã bị đình chỉ hoạt động. Vui lòng liên hệ quản trị viên để được hỗ trợ.";
          errorType = "account_suspended";
        } else {
          errorMessage =
            responseData?.message ||
            responseData?.error ||
            "Thông tin đăng nhập không chính xác";
          errorType = "login_failed";
        }

        return {
          success: false,
          error: errorMessage,
          errorType: errorType,
          statusCode: status
        };
      } else if (error.request) {
        // The request was made but no response was received
        return {
          success: false,
          error: "Không thể kết nối đến máy chủ. Vui lòng thử lại sau.",
        };
      } else {
        // Something happened in setting up the request that triggered an Error
        return {
          success: false,
          error: "Đã xảy ra lỗi hệ thống. Vui lòng thử lại sau.",
        };
      }
    }
  }

  // Register new user
  async register(userData) {
    try {
      // Use the full API path since AUTH_API might be empty
      const endpoint = AUTH_API ? `${AUTH_API}/Register` : '/Auth/Register';

      const response = await apiClient.post(endpoint, {
        ...userData,
        roleId: 1, // Set roleId to 1 for Member role
        status: 1, // Active status
      });

      if (response.status === 200 || response.status === 201) {
        return {
          success: true,
          message: "Đăng ký thành công! Vui lòng kiểm tra email để xác thực.",
        };
      } else {
        return {
          success: false,
          error: response.data?.message || "Đăng ký không thành công",
        };
      }
    } catch (error) {
      console.error("Register error:", error);

      if (error.response) {
        const errorMessage =
          error.response.data?.message ||
          error.response.data?.error ||
          "Đăng ký không thành công";
        return { success: false, error: errorMessage };
      } else if (error.request) {
        return {
          success: false,
          error: "Không thể kết nối đến hệ thống. Vui lòng thử lại sau",
        };
      } else {
        return {
          success: false,
          error: "Đã xảy ra lỗi hệ thống. Vui lòng thử lại sau",
        };
      }
    }
  }

  // Forgot Password - Send reset password email
  async forgotPassword(email) {
    try {
      // Store email for error handling
      const requestEmail = email;
      
      const endpoint = AUTH_API ? `${AUTH_API}/forgot-password` : '/Auth/forgot-password';

      console.log("AUTH_API value:", AUTH_API);
      console.log("Forgot password endpoint:", endpoint);
      console.log("Forgot password payload:", { email });

      // Try different possible request formats that the backend might expect
      const requestPayloads = [
        { email: email },                    // lowercase email
        { Email: email },                    // uppercase Email
        { emailAddress: email },             // emailAddress
        { EmailAddress: email },             // EmailAddress
        { userEmail: email },                // userEmail
        { UserEmail: email },                // UserEmail
        email,                               // just the email string
        { data: { email: email } },          // nested in data object
        { request: { email: email } },       // nested in request object
        { forgotPasswordRequest: { email: email } }, // nested in forgotPasswordRequest
        { Email: email, ClientURI: window.location.origin }, // with ClientURI for redirect
        { Email: email, ClientURI: `${window.location.origin}/reset-password` } // with specific reset URL
      ];

      let response;
      let lastError;

      // Try different request payload formats
      for (let i = 0; i < requestPayloads.length; i++) {
        const payload = requestPayloads[i];
        try {
          console.log(`Trying payload format ${i + 1}:`, payload);
          response = await apiClient.post(endpoint, payload);
          console.log(`Payload format ${i + 1} succeeded:`, payload);
          break;
        } catch (payloadError) {
          console.log(`Payload format ${i + 1} failed:`, payload, payloadError.response?.status);
          lastError = payloadError;
          continue;
        }
      }

      // If all payload formats fail, try alternative endpoints
      if (!response) {
        // If the first endpoint fails, try alternative endpoint names
        console.log("First endpoint failed, trying alternatives...");

        const alternativeEndpoints = [
          AUTH_API ? `${AUTH_API}/ForgotPassword` : '/Auth/ForgotPassword',
          AUTH_API ? `${AUTH_API}/forgotPassword` : '/Auth/forgotPassword',
          AUTH_API ? `${AUTH_API}/forgotpassword` : '/Auth/forgotpassword',
          AUTH_API ? `${AUTH_API}/forgot_password` : '/Auth/forgot_password',
          AUTH_API ? `${AUTH_API}/send-reset-email` : '/Auth/send-reset-email',
          AUTH_API ? `${AUTH_API}/request-password-reset` : '/Auth/request-password-reset'
        ];

        for (const altEndpoint of alternativeEndpoints) {
          try {
            console.log("Trying alternative endpoint:", altEndpoint);
            response = await apiClient.post(altEndpoint, { email: email });
            console.log("Alternative endpoint succeeded:", altEndpoint);
            break;
          } catch (altError) {
            console.log("Alternative endpoint failed:", altEndpoint, altError.response?.status);
            continue;
          }
        }

        // If all alternatives fail, throw the last error
        if (!response) {
          throw lastError || new Error("All endpoint attempts failed");
        }
      }

      console.log("Forgot password response status:", response.status);
      console.log("Forgot password response data:", response.data);

      if (response.status === 200 || response.status === 201) {
        console.log("Forgot password succeeded!");
        return {
          success: true,
          message: "Email đặt lại mật khẩu đã được gửi.",
        };
      } else {
        console.log("Forgot password failed with status:", response.status);
        return {
          success: false,
          error:
            response.data?.message || "Không thể gửi email đặt lại mật khẩu",
        };
      }
    } catch (error) {
      console.error("Forgot password error:", error);

      if (error.response) {
        // Handle validation errors specifically
        if (error.response.status === 400 && error.response.data?.errors) {
          const validationErrors = error.response.data.errors;
          console.log("Detailed validation errors:", JSON.stringify(validationErrors, null, 2));
          const errorMessages = [];

          // Extract validation error messages
          Object.keys(validationErrors).forEach(field => {
            console.log(`Field '${field}' errors:`, validationErrors[field]);
            if (Array.isArray(validationErrors[field])) {
              const fieldErrors = validationErrors[field].join(', ');
              errorMessages.push(field === '$' ? fieldErrors : `${field}: ${fieldErrors}`);
            } else {
              const fieldError = validationErrors[field];
              errorMessages.push(field === '$' ? fieldError : `${field}: ${fieldError}`);
            }
          });

          const finalError = errorMessages.length > 0 ? errorMessages.join('; ') : "Dữ liệu không hợp lệ";
          console.log("Final error message:", finalError);

          return {
            success: false,
            error: finalError
          };
        }

        // Check if the error is about email not found
        const errorMessage = error.response.data?.message ||
                            error.response.data?.error ||
                            error.response.data?.title ||
                            "";

        // Also check the full response body for error indicators
        const fullResponseText = JSON.stringify(error.response.data || {}).toLowerCase();

        // Handle specific case when email doesn't exist
        if (error.response.status === 400 ||
            error.response.status === 404 ||
            error.response.status === 500 || // Sometimes backend returns 500 for user not found
            errorMessage.toLowerCase().includes("not found") ||
            errorMessage.toLowerCase().includes("không tìm thấy") ||
            errorMessage.toLowerCase().includes("user not found") ||
            errorMessage.toLowerCase().includes("email not found") ||
            errorMessage.includes("JSON value could not be converted") ||
            errorMessage.includes("email field is required") ||
            fullResponseText.includes("not found") ||
            fullResponseText.includes("user not found") ||
            fullResponseText.includes("email not found") ||
            fullResponseText.includes("json value could not be converted") ||
            fullResponseText.includes("email field is required")) {

          return {
            success: false,
            error: `Tài khoản với email ${requestEmail} không tồn tại`
          };
        }

        return {
          success: false,
          error: errorMessage || "Không thể gửi email đặt lại mật khẩu"
        };
      } else if (error.request) {
        return {
          success: false,
          error: "Không thể kết nối đến hệ thống. Vui lòng thử lại sau",
        };
      } else {
        return {
          success: false,
          error: "Đã xảy ra lỗi hệ thống. Vui lòng thử lại sau",
        };
      }
    }
  }

  // Reset Password - Reset password with token
  async resetPassword(resetData) {
    try {
      // Use the full API path since AUTH_API might be empty
      const endpoint = AUTH_API ? `${AUTH_API}/reset-password` : '/Auth/reset-password';

      console.log("Reset password endpoint:", endpoint);
      console.log("Reset password data:", resetData);

      // Try to decode the token to see what's inside (for debugging)
      if (resetData.token) {
        try {
          const tokenParts = resetData.token.split('.');
          if (tokenParts.length === 3) {
            const payload = JSON.parse(atob(tokenParts[1]));
            console.log("Token payload:", payload);
          }
        } catch (e) {
          console.log("Could not decode token:", e);
        }
      }

      // Based on validation error, backend expects exactly: Token + NewPassword
      // Validate that we have all required data
      if (!resetData.token) {
        return {
          success: false,
          error: "Token không hợp lệ hoặc bị thiếu",
        };
      }

      if (!resetData.newPassword || resetData.newPassword.trim() === '') {
        return {
          success: false,
          error: "Mật khẩu mới không được để trống",
        };
      }

      // Let's try the correct format directly first
      const correctPayload = {
        Token: resetData.token.trim(),
        NewPassword: resetData.newPassword.trim(),
      };

      console.log("Trying correct payload format:", correctPayload);
      console.log("Token length:", resetData.token.length);
      console.log("NewPassword length:", resetData.newPassword.length);
      console.log("NewPassword value:", JSON.stringify(resetData.newPassword));

      // Try to identify the issue - let's also try with a simple password for testing
      console.log("Note: If this fails, try with a simple password like '123456' to test if special characters are the issue");

      try {
        const response = await apiClient.post(endpoint, correctPayload);
        console.log("Reset password succeeded with correct format!");

        if (response.status === 200 || response.status === 201) {
          return {
            success: true,
            message: "Mật khẩu đã được đặt lại thành công.",
          };
        } else {
          return {
            success: false,
            error: response.data?.message || "Không thể đặt lại mật khẩu",
          };
        }
      } catch (directError) {
        console.log("Direct format failed:", directError.response?.data);
        // If direct format fails, throw the error to be handled by outer catch
        throw directError;
      }
    } catch (error) {
      console.error("Reset password error:", error);
      console.error("Reset password error response:", error.response?.data);

      if (error.response) {
        // Handle validation errors specifically
        if (error.response.status === 400 && error.response.data?.errors) {
          const validationErrors = error.response.data.errors;
          console.log("Reset password validation errors:", JSON.stringify(validationErrors, null, 2));
          const errorMessages = [];

          // Extract validation error messages
          Object.keys(validationErrors).forEach(field => {
            console.log(`Reset password field '${field}' errors:`, validationErrors[field]);
            if (Array.isArray(validationErrors[field])) {
              const fieldErrors = validationErrors[field].join(', ');
              errorMessages.push(field === '$' ? fieldErrors : `${field}: ${fieldErrors}`);
            } else {
              const fieldError = validationErrors[field];
              errorMessages.push(field === '$' ? fieldError : `${field}: ${fieldError}`);
            }
          });

          const finalError = errorMessages.length > 0 ? errorMessages.join('; ') : "Dữ liệu không hợp lệ";
          console.log("Reset password final error message:", finalError);

          return {
            success: false,
            error: finalError
          };
        }

        const errorMessage =
          error.response.data?.message ||
          error.response.data?.error ||
          error.response.data?.title ||
          "Không thể đặt lại mật khẩu";
        return { success: false, error: errorMessage };
      } else if (error.request) {
        return {
          success: false,
          error: "Không thể kết nối đến hệ thống. Vui lòng thử lại sau",
        };
      } else {
        return {
          success: false,
          error: "Đã xảy ra lỗi hệ thống. Vui lòng thử lại sau",
        };
      }
    }
  }

  // Change Password - Change password for authenticated user
  async changePassword(changePasswordData) {
    try {
      const token = localStorage.getItem("authToken");
      if (!token) {
        return {
          success: false,
          error: "Không tìm thấy token xác thực",
        };
      }

      // Get user ID from current user
      const userId = this.currentUser?.id || this.currentUser?.userID;
      if (!userId) {
        return {
          success: false,
          error: "Không tìm thấy thông tin người dùng",
        };
      }

      // Use the Auth API endpoint for changing password
      const endpoint = AUTH_API ? `${AUTH_API}/change-password/${userId}` : `/Auth/change-password/${userId}`;

      console.log("Change password endpoint:", endpoint);
      console.log("Change password payload:", {
        currentPassword: changePasswordData.currentPassword,
        newPassword: changePasswordData.newPassword,
      });

      const response = await apiClient.patch(
        endpoint,
        {
          currentPassword: changePasswordData.currentPassword,
          newPassword: changePasswordData.newPassword,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status === 200 || response.status === 201) {
        return {
          success: true,
          message: "Mật khẩu đã được thay đổi thành công.",
        };
      } else {
        return {
          success: false,
          error: response.data?.message || "Không thể thay đổi mật khẩu",
        };
      }
    } catch (error) {
      console.error("Change password error:", error);

      if (error.response) {
        // Handle validation errors specifically
        if (error.response.status === 400 && error.response.data?.errors) {
          const validationErrors = error.response.data.errors;
          const errorMessages = [];

          // Extract validation error messages
          Object.keys(validationErrors).forEach(field => {
            if (Array.isArray(validationErrors[field])) {
              errorMessages.push(...validationErrors[field]);
            } else {
              errorMessages.push(validationErrors[field]);
            }
          });

          return {
            success: false,
            error: errorMessages.length > 0 ? errorMessages.join(', ') : "Dữ liệu không hợp lệ"
          };
        }

        const errorMessage =
          error.response.data?.message ||
          error.response.data?.error ||
          error.response.data?.title ||
          "Không thể thay đổi mật khẩu";
        return { success: false, error: errorMessage };
      } else if (error.request) {
        return {
          success: false,
          error: "Không thể kết nối đến hệ thống. Vui lòng thử lại sau",
        };
      } else {
        return {
          success: false,
          error: "Đã xảy ra lỗi hệ thống. Vui lòng thử lại sau",
        };
      }
    }
  }

  // Google Login - Initiate Google OAuth flow
  async googleLogin() {
    try {
      // Direct redirect to backend Google login endpoint
      // Backend will handle the redirect to Google OAuth
      window.location.href = GOOGLE_LOGIN_API;

      return {
        success: true,
        message: "Đang chuyển hướng đến Google...",
      };
    } catch (error) {
      console.error("Google login error:", error);
      return {
        success: false,
        error: "Đã xảy ra lỗi khi khởi tạo đăng nhập Google",
      };
    }
  }

  // Google Callback - Handle Google OAuth callback
  async googleCallback(code, state) {
    try {
      const response = await apiClient.post(
        GOOGLE_CALLBACK_API,
        {
          code: code,
          state: state,
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status === 200 && response.data) {
        const token = response.data.token || response.data;
        console.log("Google login response (token):", token); // Debug log

        if (!token) {
          console.error("No token received from Google callback");
          return {
            success: false,
            error: "Không nhận được token xác thực từ Google",
          };
        }

        // Process the token similar to regular login
        return await this.processAuthToken(token);
      } else {
        return {
          success: false,
          error: response.data?.message || "Xác thực Google không thành công",
        };
      }
    } catch (error) {
      console.error("Google callback error:", error);

      if (error.response) {
        const errorMessage =
          error.response.data?.message ||
          error.response.data?.error ||
          "Xác thực Google không thành công";
        return { success: false, error: errorMessage };
      } else if (error.request) {
        return {
          success: false,
          error: "Không thể kết nối đến máy chủ. Vui lòng thử lại sau.",
        };
      } else {
        return {
          success: false,
          error: "Đã xảy ra lỗi hệ thống. Vui lòng thử lại sau.",
        };
      }
    }
  }

  // Process authentication token (shared logic for regular and Google login)
  async processAuthToken(token) {
    try {
      // Decode JWT token
      const tokenParts = token.split(".");
      if (tokenParts.length !== 3) {
        console.error("Invalid token format");
        return {
          success: false,
          error: "Token không hợp lệ",
        };
      }

      const payload = JSON.parse(atob(tokenParts[1]));
      console.log("Decoded token payload:", payload); // Debug log

      // Map role string/number from token to standardized role code
      function mapRole(rawRole) {
        if (rawRole === "Admin" || rawRole === 4 || rawRole === "4")
          return API_ROLES.ADMIN;
        if (rawRole === "Member" || rawRole === 1 || rawRole === "1")
          return API_ROLES.MEMBER;
        if (rawRole === "Staff_Doctor" || rawRole === 2 || rawRole === "2")
          return API_ROLES.STAFF_DOCTOR;
        if (
          rawRole === "Staff_Blood_Manager" ||
          rawRole === 3 ||
          rawRole === "3"
        )
          return API_ROLES.STAFF_BLOOD_MANAGER;
        return API_ROLES.GUEST;
      }

      // Extract user information from token payload
      console.log("JWT payload:", payload);
      const userId = payload[
        "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier"
      ];
      console.log("Extracted user ID from JWT:", userId);

      // Validate user ID
      if (!userId || userId === "0" || isNaN(parseInt(userId))) {
        console.error("Invalid user ID from JWT token:", userId);
        return {
          success: false,
          error: "Token không hợp lệ - không tìm thấy ID người dùng",
        };
      }

      const user = {
        id: userId,
        email:
          payload[
          "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
          ],
        name:
          payload[
          "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"
          ] ||
          payload[
            "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
          ].split("@")[0],
        role: mapRole(
          payload[
          "http://schemas.microsoft.com/ws/2008/06/identity/claims/role"
          ]
        ),
        status: 1,
        profile: {
          email:
            payload[
            "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
            ],
          fullName:
            payload[
            "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"
            ] ||
            payload[
              "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
            ].split("@")[0],
          phone: payload["phone"] || "",
          address: payload["address"] || "",
          bloodType: payload["bloodType"] || "",
          dateOfBirth: payload["dateOfBirth"] || "",
          gender: payload["gender"] || "",
          department: payload["department"] || "",
        },
        isFirstLogin: true,
      };

      // Save token
      localStorage.setItem("authToken", token);

      // Fetch additional user details from API
      try {
        console.log("Fetching user details for ID:", user.id);
        const infoResponse = await apiClient.get(`${INFORMATION_API}/`, {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        });

        if (infoResponse.data) {
          // Find the user's information
          const userInfo = infoResponse.data.find(
            (info) => info.userID === parseInt(user.id)
          );
          console.log("Found user info:", userInfo);

          if (userInfo) {
            // Ensure departmentID is copied to user object if present (support both departmentID and departmentId)
            if (userInfo.departmentID || userInfo.departmentId) {
              user.departmentID =
                userInfo.departmentID || userInfo.departmentId;
            }
            // Check if user account is suspended
            if (userInfo.status === 0) {
              return {
                success: false,
                suspended: true,
                error: "Tài khoản của bạn đã bị đình chỉ hoạt động",
                message: "Tài khoản của bạn hiện đang bị đình chỉ hoạt động. Bạn không thể đăng nhập vào hệ thống. Vui lòng liên hệ quản trị viên để được hỗ trợ.",
              };
            }

            // Update user profile with details from database
            user.profile = {
              ...user.profile,
              ...userInfo,
              // Đảm bảo có trường name từ database
              name: userInfo.name || user.profile.fullName,
            };

            // Add status to user object
            user.status = userInfo.status;

            // For doctors, determine doctor type based on department
            if (user.role === API_ROLES.STAFF_DOCTOR) {
              // Ưu tiên lấy departmentID nếu có, nếu không thì lấy tên khoa
              let departmentID = user.departmentID || null;
              let departmentName = userInfo.department || user.department || "";
              if (!departmentID && departmentName) {
                departmentID = DEPARTMENT_IDS[departmentName.trim()] || null;
              }
              user.departmentID = departmentID;
              user.department = departmentName;
              // Gán doctorType dựa vào departmentID
              if (parseInt(departmentID) === 1) {
                user.doctorType = DOCTOR_TYPES.BLOOD_DEPARTMENT;
              } else {
                user.doctorType = DOCTOR_TYPES.OTHER_DEPARTMENT;
              }
            }

            // Save user info to localStorage for easy access
            localStorage.setItem("memberInfo", JSON.stringify(userInfo));
          }
        }
      } catch (error) {

        // Continue with login even if API call fails
        if (user.role === API_ROLES.STAFF_DOCTOR) {
          user.doctorType = "other_department";
        }
      }



      // Save authentication data
      this.currentUser = user;
      this.isAuthenticated = true;

      // Save user to localStorage
      this.saveUserToStorage(user);

      // Clear sensitive data if user doesn't have admin/manager privileges
      clearSensitiveDataForNonPrivilegedUsers(user.role);

      return {
        success: true,
        user: user,
        token: token,
      };
    } catch (decodeError) {
      console.error("Error processing auth token:", decodeError);
      return {
        success: false,
        error: "Không thể xác thực token",
      };
    }
  }

  // Alias methods for backward compatibility
  async handleGoogleCallback(code, state) {
    return await this.googleCallback(code, state);
  }

  async processTokenAndCreateSession(token) {
    return await this.processAuthToken(token);
  }

  // Check if current user is still active (not suspended)
  async checkUserStatus() {
    try {
      const currentUser = this.getCurrentUser();
      if (!currentUser || !currentUser.id) {
        return { active: false, reason: "No user logged in" };
      }

      // Get user info from API to check current status
      const response = await apiClient.get(`/Information/${currentUser.id}`);

      if (response.data && response.data.status === 0) {
        // User is suspended, force logout
        await this.logout();
        return {
          active: false,
          suspended: true,
          reason: "Account has been suspended",
          message:
            "Tài khoản của bạn đã bị đình chỉ hoạt động. Bạn sẽ được đăng xuất khỏi hệ thống.",
        };
      }

      return { active: true };
    } catch (error) {
      console.error("Error checking user status:", error);
      return { active: true }; // Assume active if check fails
    }
  }

  // Logout
  async logout() {
    try {
      // Call API to invalidate token if token exists
      const token = localStorage.getItem("authToken");
      if (token) {
        try {
          await apiClient.post(
            "/api/Auth/logout",
            {},
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );
          console.log("Logout API call successful");
        } catch (apiError) {
          console.warn(
            "Logout API call failed, but continuing with local logout:",
            apiError.message
          );
          // Don't throw error, just log it and continue with local logout
        }
      }
    } catch (error) {
      console.error("Logout error:", error);
      // Continue with local logout even if API call fails
    } finally {
      // Always clear local data and sensitive information
      this.currentUser = null;
      this.isAuthenticated = false;
      localStorage.removeItem("currentUser");
      localStorage.removeItem("authToken");
      localStorage.removeItem("memberInfo");
      
      // Clear sensitive user data that should only be accessible to admin/manager
      clearAllSensitiveData();
      
      console.log("Local logout completed - all sensitive data cleared");
    }

    return { success: true };
  }

  // Get current user
  getCurrentUser() {
    return this.currentUser;
  }

  // Get authentication token
  getToken() {
    return localStorage.getItem("authToken");
  }

  // Check if user is authenticated
  isUserAuthenticated() {
    return this.isAuthenticated && this.currentUser !== null;
  }

  // Get user role
  getUserRole() {
    return this.currentUser?.role || API_ROLES.GUEST;
  }

  // Update user profile
  async updateUserProfile(profileData) {
    try {
      const token = localStorage.getItem("authToken");
      if (!token) {
        return {
          success: false,
          error: "Không tìm thấy token xác thực",
        };
      }

      const response = await apiClient.put("/api/users/profile", profileData, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        // Update local user data
        const updatedUser = { ...this.currentUser, ...profileData };
        this.currentUser = updatedUser;
        localStorage.setItem("currentUser", JSON.stringify(updatedUser));

        return {
          success: true,
          data: updatedUser,
          message: "Cập nhật thông tin thành công",
        };
      } else {
        return {
          success: false,
          error:
            response.data?.message || "Cập nhật thông tin không thành công",
        };
      }
    } catch (error) {
      console.error("Update profile error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Cập nhật thông tin không thành công",
      };
    }
  }

  // Get user status
  getUserStatus() {
    return this.currentUser?.status || null;
  }

  // Check if user has specific role
  hasRole(role) {
    return this.getUserRole() === role;
  }

  // Check if user has any of the specified roles
  hasAnyRole(roles) {
    return roles.includes(this.getUserRole());
  }

  // Get redirect path based on user role and status
  getRedirectPath() {
    if (!this.isAuthenticated) {
      return "/";
    }

    // Check if it's first login for member
    // if (
    //   this.currentUser?.role === API_ROLES.MEMBER &&
    //   this.currentUser?.isFirstLogin
    // ) {
    //   return "/member";
    // }

    const role = this.getUserRole();

    switch (role) {
      case API_ROLES.MEMBER:
        return "/member";

      case API_ROLES.STAFF_DOCTOR:
        // Check doctor type to determine correct dashboard
        if (this.currentUser?.doctorType === "blood_department") {
          return "/doctor"; // Blood department doctors use manager dashboard
        } else {
          return "/doctor"; // Other department doctors use doctor dashboard
        }

      case API_ROLES.STAFF_BLOOD_MANAGER:
        return "/manager";

      case API_ROLES.ADMIN:
        return "/admin/dashboard";

      default:
        return "/";
    }
  }

  // Update user profile
  updateProfile(profileData) {
    if (this.currentUser) {
      this.currentUser.profile = {
        ...this.currentUser.profile,
        ...profileData,
      };
      localStorage.setItem("currentUser", JSON.stringify(this.currentUser));
    }
  }

  // Update user status
  updateStatus(newStatus) {
    if (this.currentUser) {
      this.currentUser.status = newStatus;
      this.saveUserToStorage(this.currentUser);
      return true;
    }
    return false;
  }
}

// Create singleton instance
const authService = new AuthService();

export default authService;

// Export for use in components
export { authService };

// Helper functions for route protection
export const requireAuth = () => {
  return authService.isUserAuthenticated();
};

export const requireRole = (allowedRoles) => {
  if (!authService.isUserAuthenticated()) {
    return false;
  }
  return allowedRoles.includes(authService.getUserRole());
};

// Removed requireMemberType - no longer needed
