// Shared Blood Request Page Header Styles
// Supports multiple roles: admin, doctor, manager
// Medical color scheme: #D93E4C, #20374E, #DECCAA, #D91022

@use "../base/variables" as *;
@use "../base/mixin" as *;
@use "../base/admin-design-system.scss" as admin;
@use "sass:color";

.blood-request-page-header {
  background: $manager-bg;
  padding: $spacing-lg;
  border-radius: 12px;
  margin-bottom: $spacing-lg;
  box-shadow: 0 2px 8px $manager-shadow;
  border: 1px solid $manager-border;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: $spacing-md;
  transition: all 0.3s ease;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: $spacing-md;
    padding: $spacing-md;
  }

  .header-info {
    flex: 1;
    min-width: 0; // Prevent flex item from overflowing

    .header-title-section {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      gap: $spacing-md;

      @media (max-width: 576px) {
        flex-direction: column;
        gap: $spacing-sm;
      }

      .header-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        background: linear-gradient(
          135deg,
          $primary-color 0%,
          color.adjust($primary-color, $lightness: -10%) 100%
        );
        border-radius: 12px;
        color: white;
        font-size: 24px;
        flex-shrink: 0;
        box-shadow: 0 4px 12px rgba($primary-color, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba($primary-color, 0.4);
        }

        @media (max-width: 576px) {
          width: 40px;
          height: 40px;
          font-size: 20px;
        }
      }

      .header-text {
        flex: 1;
        min-width: 0;

        .header-title {
          margin: 0 0 4px 0 !important;
          font-size: 1.8rem;
          font-weight: 600;
          color: $manager-text;
          font-family: $font-manager;
          line-height: 1.2;
          word-wrap: break-word;
          display: flex;
          align-items: center;
          gap: $spacing-sm;

          @media (max-width: 768px) {
            font-size: 1.5rem;
            flex-direction: column;
            align-items: flex-start;
            gap: $spacing-xs;
          }

          @media (max-width: 576px) {
            font-size: 1.3rem;
          }
        }

        .header-description {
          margin: 0;
          color: $manager-text-light;
          font-size: 1rem;
          font-family: $font-manager;
          line-height: 1.4;
          word-wrap: break-word;

          @media (max-width: 768px) {
            font-size: 0.9rem;
          }

          @media (max-width: 576px) {
            font-size: 0.85rem;
          }
        }
      }
    }
  }

  .header-actions {
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;

    @media (max-width: 768px) {
      width: 100%;
      justify-content: flex-start;
    }

    .ant-space {
      @media (max-width: 576px) {
        width: 100%;

        .ant-space-item {
          flex: 1;

          .ant-btn {
            width: 100%;
          }
        }
      }
    }
  }
}

// Role-specific styles
.admin-page-header {
  border: 1px solid rgba($primary-color, 0.2);
  box-shadow: 0 2px 8px rgba($primary-color, 0.1);

  .header-icon {
    background: linear-gradient(135deg, $primary-color 0%, $primary-hover 100%);
    box-shadow: 0 4px 12px rgba($primary-color, 0.3);

    &:hover {
      box-shadow: 0 6px 16px rgba($primary-color, 0.4);
    }
  }

  .header-title {
    color: $text-primary !important;
  }

  .header-description {
    color: $text-secondary !important;
  }
}

.doctor-page-header {
  // Inherits base styles - already optimized for doctor role
  border: 1px solid rgba($primary-color, 0.2);

  .header-icon {
    background: linear-gradient(135deg, $primary-color 0%, $primary-hover 100%);
  }
}

.manager-page-header {
  // Inherits base styles - already optimized for manager role
  border: 1px solid rgba($manager-primary, 0.2);

  .header-icon {
    background: linear-gradient(
      135deg,
      $manager-primary 0%,
      $manager-dark 100%
    );
  }
}

// Tab-specific badge styles
.blood-request-page-header {
  .ant-badge {
    .ant-badge-count {
      font-size: 12px;
      font-weight: 600;
      min-width: 20px;
      height: 20px;
      line-height: 18px;
      border-radius: 10px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }
}

// Responsive improvements
@media (max-width: 480px) {
  .blood-request-page-header {
    padding: $spacing-md $spacing-sm;
    margin-bottom: $spacing-md;

    .header-info .header-title-section {
      gap: $spacing-sm;

      .header-icon {
        width: 36px;
        height: 36px;
        font-size: 18px;
      }

      .header-text {
        .header-title {
          font-size: 1.2rem;
        }

        .header-description {
          font-size: 0.8rem;
        }
      }
    }
  }
}

// Loading state
.blood-request-page-header.loading {
  .header-title {
    opacity: 0.6;
  }

  .header-description {
    opacity: 0.4;
  }

  .header-actions {
    .ant-btn {
      opacity: 0.7;
    }
  }
}

// Animation for tab switching
.blood-request-page-header {
  .header-title,
  .header-description {
    transition: all 0.3s ease;
  }
}

// Dark mode support (future enhancement)
@media (prefers-color-scheme: dark) {
  .blood-request-page-header {
    background: color.adjust($manager-bg, $lightness: -20%);
    border-color: color.adjust($manager-border, $lightness: -15%);
    color: color.adjust($manager-text, $lightness: 20%);

    .header-title {
      color: color.adjust($manager-text, $lightness: 25%) !important;
    }

    .header-description {
      color: color.adjust($manager-text-light, $lightness: 30%) !important;
    }
  }
}
