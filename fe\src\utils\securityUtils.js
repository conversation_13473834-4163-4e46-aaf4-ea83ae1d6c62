/**
 * Security utilities for managing sensitive data in localStorage
 */

const SENSITIVE_DATA_KEYS = [
  'allUsers',
  'cachedUsers', 
  'userManagementCache'
];

/**
 * Clear sensitive data from localStorage for users without proper privileges
 * @param {string|number} userRole - Current user's role
 */
export const clearSensitiveDataForNonPrivilegedUsers = (userRole) => {
  // Only admin (4) and manager (3) should have access to sensitive data
  if (userRole !== "4" && userRole !== "3" && userRole !== 4 && userRole !== 3) {
    SENSITIVE_DATA_KEYS.forEach(key => {
      localStorage.removeItem(key);
    });
    console.log("Cleared sensitive data for non-privileged user with role:", userRole);
  }
};

/**
 * Clear all sensitive data from localStorage (used during logout)
 */
export const clearAllSensitiveData = () => {
  SENSITIVE_DATA_KEYS.forEach(key => {
    localStorage.removeItem(key);
  });
  console.log("Cleared all sensitive data from localStorage");
};

/**
 * Check if current user has permission to access sensitive data
 * @param {string|number} userRole - Current user's role
 * @returns {boolean} - True if user has permission
 */
export const hasDataAccessPermission = (userRole) => {
  return userRole === "4" || userRole === "3" || userRole === 4 || userRole === 3;
};

/**
 * Get current user role from localStorage
 * @returns {string|number|null} - Current user's role or null if not found
 */
export const getCurrentUserRole = () => {
  try {
    const currentUser = JSON.parse(localStorage.getItem("currentUser") || "{}");
    return currentUser.role || null;
  } catch (error) {
    console.error("Error getting current user role:", error);
    return null;
  }
};

/**
 * Initialize security check on app load
 * This should be called when the app starts to ensure no sensitive data
 * is accessible to users without proper permissions
 */
export const initializeSecurityCheck = () => {
  const userRole = getCurrentUserRole();
  if (userRole) {
    clearSensitiveDataForNonPrivilegedUsers(userRole);
  } else {
    // No user logged in, clear all sensitive data
    clearAllSensitiveData();
  }
};
