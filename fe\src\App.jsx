// App.jsx
import { RouterProvider } from "react-router-dom";
import { useEffect } from "react";
import router from "./routes/AppRoutes";
import { UserDataProvider } from "./contexts/UserDataContext";
import { initializeSecurityCheck } from "./utils/securityUtils";

function App() {
  useEffect(() => {
    // Initialize security check when app starts
    initializeSecurityCheck();
  }, []);

  return (
    <UserDataProvider>
      <RouterProvider router={router} />
    </UserDataProvider>
  );
}

export default App;
