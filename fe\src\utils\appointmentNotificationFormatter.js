
const PROCESS_DESCRIPTIONS = {
  1: "<PERSON><PERSON><PERSON> ký",
  2: "<PERSON><PERSON><PERSON><PERSON> sức khỏe cơ bản", 
  3: "<PERSON><PERSON><PERSON> máu",
  4: "<PERSON><PERSON><PERSON> nghiệm máu",
  5: "<PERSON><PERSON><PERSON><PERSON> kho"
};

// Status descriptions
const STATUS_DESCRIPTIONS = {
  true: "Chấp nhận",
  false: "<PERSON>hông chấp nhận",
  "true": "Chấp nhận",
  "false": "Không chấp nhận"
};


export const formatAppointmentNotificationMessage = (message) => {
  if (!message) return message;

  // Handle process update messages
  const processUpdateRegex = /Quy trình được cập nhật thành (\d+)/i;
  const processMatch = message.match(processUpdateRegex);
  if (processMatch) {
    const processNumber = parseInt(processMatch[1]);
    const processDescription = PROCESS_DESCRIPTIONS[processNumber] || "Không xác định";
    return `Quy trình được cập nhật thành: ${processDescription}`;
  }

  // Handle status update messages
  const statusUpdateRegex = /Trạng thái được cập nhật thành (true|false)/i;
  const statusMatch = message.match(statusUpdateRegex);
  if (statusMatch) {
    const statusValue = statusMatch[1].toLowerCase();
    const statusDescription = STATUS_DESCRIPTIONS[statusValue] || "Không xác định";
    return `Trạng thái được cập nhật thành: ${statusDescription}`;
  }

  // Return original message if no pattern matches
  return message;
};


export const shouldFilterNotification = (notification) => {
  if (!notification || !notification.message) return false;

  const message = notification.message || notification.Message || "";
  const title = notification.title || notification.Title || "";

  // Filter out "Ghi chú được cập nhật" notifications
  if (message.includes("Ghi chú được cập nhật") ||
      message.includes("Notes updated") ||
      message.includes("note updated")) {
    return true;
  }

  // Filter out profile update notifications (weight/height updates from blood donation form)
  if (message.includes("Hồ sơ đã cập nhật") ||
      message.includes("Profile updated") ||
      message.includes("Sửa hồ sơ") ||
      title.includes("UserProfile") ||
      title.includes("Profile")) {
    return true;
  }

  return false;
};


export const processNotification = (notification) => {
  if (!notification) return null;

  // Check if notification should be filtered out
  if (shouldFilterNotification(notification)) {
    return null;
  }

  // Format the message
  const originalMessage = notification.message || notification.Message || "";
  const formattedMessage = formatAppointmentNotificationMessage(originalMessage);

  // Return formatted notification
  return {
    ...notification,
    message: formattedMessage,
    Message: formattedMessage // Support both camelCase and PascalCase
  };
};


export const processNotifications = (notifications) => {
  if (!Array.isArray(notifications)) return [];

  return notifications
    .map(processNotification)
    .filter(notification => notification !== null);
};

export default {
  formatAppointmentNotificationMessage,
  shouldFilterNotification,
  processNotification,
  processNotifications
};
