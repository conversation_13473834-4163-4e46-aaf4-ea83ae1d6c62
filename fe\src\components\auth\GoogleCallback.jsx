import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import authService from "../../services/authService";
import { useAuth } from "../../contexts/AuthContext";
import "../../styles/components/GoogleCallback.scss";

export default function GoogleCallback() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { updateAuthState } = useAuth();
  const [status, setStatus] = useState("processing"); // processing, success, error
  const [message, setMessage] = useState("Đang xử lý đăng nhập Google...");

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // Get parameters from URL
        const token = searchParams.get("token");
        const error = searchParams.get("error");
        const errorDescription = searchParams.get("error_description");

        console.log("Google callback parameters:", { token, error, errorDescription });
        console.log("Current URL:", window.location.href);

        // Check if there's an error from backend
        if (error) {
          console.error("Google OAuth error:", error, errorDescription);
          setStatus("error");
          setMessage(errorDescription || "Đăng nhập Google bị hủy hoặc có lỗi xảy ra.");
          setTimeout(() => {
            navigate("/login");
          }, 3000);
          return;
        }

        // Check if we have a token from backend
        if (!token) {
          console.error("No token received from backend");
          setStatus("error");
          setMessage("Không nhận được token xác thực từ backend.");
          setTimeout(() => {
            navigate("/login");
          }, 3000);
          return;
        }

        console.log("Processing token from backend:", token);

        try {
          // Use authService's common token processing method
          const result = await authService.processTokenAndCreateSession(token);

          if (result.success) {
            setStatus("success");
            setMessage("Đăng nhập Google thành công! Đang chuyển hướng...");

            // Update AuthContext with the user data
            const authStateUpdated = updateAuthState(result.user);

            // Debug logging
            console.log("Google login successful, user:", result.user);
            console.log("Auth service state:", {
              isAuthenticated: authService.isAuthenticated,
              currentUser: authService.currentUser,
              userRole: authService.getUserRole()
            });
            console.log("AuthContext updated:", authStateUpdated);

            setTimeout(() => {
              const redirectPath = authService.getRedirectPath();
              console.log("Redirecting to:", redirectPath);
              navigate(redirectPath);
            }, 1000);
          } else {
            console.error("Token processing failed:", result.error);
            setStatus("error");
            setMessage(result.error || "Lỗi xử lý token từ Google.");
            setTimeout(() => {
              navigate("/login");
            }, 3000);
          }
        } catch (tokenError) {
          console.error("Error processing token:", tokenError);
          setStatus("error");
          setMessage("Lỗi xử lý token từ Google.");
          setTimeout(() => {
            navigate("/login");
          }, 3000);
        }
      } catch (error) {
        console.error("Error processing Google callback:", error);
        setStatus("error");
        setMessage("Đã xảy ra lỗi khi xử lý đăng nhập Google.");
        setTimeout(() => {
          navigate("/login");
        }, 3000);
      }
    };

    handleCallback();
  }, [searchParams, navigate]);

  return (
    <div className="google-callback">
      <div className="google-callback__container">
        <div className="google-callback__content">
          {status === "processing" && (
            <>
              <div className="google-callback__spinner"></div>
              <h2>Đang xử lý đăng nhập</h2>
              <p>{message}</p>
            </>
          )}
          
          {status === "success" && (
            <>
              <div className="google-callback__success-icon">✓</div>
              <h2>Đăng nhập thành công!</h2>
              <p>{message}</p>
            </>
          )}
          
          {status === "error" && (
            <>
              <div className="google-callback__error-icon">✗</div>
              <h2>Đăng nhập thất bại</h2>
              <p>{message}</p>
              <p className="google-callback__redirect-info">
                Bạn sẽ được chuyển về trang đăng nhập trong giây lát...
              </p>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
