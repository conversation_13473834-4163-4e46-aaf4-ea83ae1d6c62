import { apiClient } from "./axiosInstance";
import config from "../config/environment";

const API_BASE = config.api.news;

export const fetchAllNews = async () => {
  const res = await apiClient.get(API_BASE);
  return res.data;
};

export const fetchNewsById = async (newsId) => {
  try {
    console.log("Fetching news with ID:", newsId);

    // Use the centralized config for the API endpoint
    const apiUrl = `${API_BASE}/${newsId}`;
    console.log("API URL:", apiUrl);

    const response = await apiClient.get(apiUrl);
    console.log("API Response:", response.data);

    // API có thể trả về array, tìm news có newsId khớp
    const responseData = response.data;
    if (Array.isArray(responseData)) {
      console.log("Response is array, searching for matching news...");
      const news = responseData.find(
        (item) =>
          item.contentID === parseInt(newsId) ||
          item.contentID === newsId ||
          item.postId === parseInt(newsId) ||
          item.postId === newsId ||
          item.id === parseInt(newsId) ||
          item.id === newsId
      );

      if (news) {
        console.log("Found matching news:", news);
        return news;
      } else {
        console.error(`News with ID ${newsId} not found in array`);
        throw new Error(`News with ID ${newsId} not found`);
      }
    } else {
      // Nếu API trả về object trực tiếp
      console.log("Response is direct object:", responseData);
      return responseData;
    }
  } catch (error) {
    console.error("Error fetching news detail:", error);
    throw error;
  }
};

export const createNews = async (data) => {
  // Process tags to separate existing tag IDs and new tag names
  const selectedTags = data.tagIds || [];
  const tagIds = [];
  const newTags = [];

  selectedTags.forEach((tag) => {
    // If it's a number or numeric string, treat as tagId
    if (
      typeof tag === "number" ||
      (typeof tag === "string" && !isNaN(tag) && tag.trim() !== "")
    ) {
      tagIds.push(typeof tag === "number" ? tag : parseInt(tag));
    } else if (typeof tag === "string" && tag.trim() !== "") {
      // If it's a non-numeric string, treat as new tag name
      newTags.push(tag);
    }
  });

  const requestData = {
    title: data.title,
    summary: data.summary || data.excerpt || "",
    content: data.content,
    imgUrl: data.imgUrl || "",
    tagIds: tagIds,
    newTags: newTags,
    userId: data.userId,
  };

  try {
    const res = await apiClient.post(API_BASE, requestData);
    return res.data;
  } catch (error) {
    console.error("Error response from News API:", {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
      url: error.config?.url,
      method: error.config?.method,
    });
    throw error;
  }
};

export const updateNews = async (newsId, data) => {
  // Process tags to separate existing tag IDs and new tag names
  const tagsToProcess = data.tags || data.tagIds || [];
  const tagIds = [];
  const newTags = [];

  tagsToProcess.forEach((tag) => {
    // If it's a number or numeric string, treat as tagId
    if (
      typeof tag === "number" ||
      (typeof tag === "string" && !isNaN(tag) && tag.trim() !== "")
    ) {
      tagIds.push(typeof tag === "number" ? tag : parseInt(tag));
    } else if (typeof tag === "string" && tag.trim() !== "") {
      // If it's a non-numeric string, treat as new tag name
      newTags.push(tag);
    }
  });

  // Also handle newTags from data if provided
  if (data.newTags && Array.isArray(data.newTags)) {
    newTags.push(...data.newTags);
  }

  const requestData = {
    title: data.title,
    summary: data.summary || data.excerpt || "",
    content: data.content,
    imgUrl: data.imgUrl || "",
    tagIds: tagIds,
    newTags: newTags,
    userId: data.userId,
  };

  try {
    const res = await apiClient.put(`${API_BASE}/${newsId}`, requestData);
    return res.data;
  } catch (error) {
    console.error("Update news error:", error);
    throw error;
  }
};

export const deleteNews = async (newsId) => {
  // Get current user ID from localStorage
  const currentUser = localStorage.getItem("currentUser");
  let userId = null;
  if (currentUser) {
    try {
      const userData = JSON.parse(currentUser);
      userId = userData.id || userData.userId || userData.userID;
    } catch (error) {
      console.error("Error parsing currentUser:", error);
    }
  }

  // Send userId as query parameter
  const url = userId
    ? `${API_BASE}/${newsId}?userId=${userId}`
    : `${API_BASE}/${newsId}`;

  try {
    const res = await apiClient.delete(url);
    return res.data;
  } catch (error) {
    console.error("Error deleting news:", error);
    throw error;
  }
};
