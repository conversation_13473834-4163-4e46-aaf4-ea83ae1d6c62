import { useState, useEffect } from "react";
import { useUserData } from "../contexts/UserDataContext";
import { getDepartmentFromUser } from "../utils/departmentUtils";
import authService from "../services/authService";

/**
 * Custom hook for getting department info from blood request data
 * Uses UserDataContext for caching and optimized API calls
 */
export const useBloodRequestDepartment = (requestData, isFromMember = false) => {
  const { getUserData } = useUserData();
  const [department, setDepartment] = useState("Đang tải...");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const getDepartmentInfo = async () => {
      // Don't show department for member requests
      if (isFromMember) {
        setDepartment(null);
        setLoading(false);
        return;
      }

      if (!requestData) {
        setDepartment("Không xác định");
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        // First, try to get department info directly from request data
        const directDepartment = getDepartmentFromUser(requestData);
        if (directDepartment && directDepartment !== "Không xác định") {
          setDepartment(directDepartment);
          setLoading(false);
          return;
        }

        // If no direct department info, try to get from user data
        const userId = requestData.userID || requestData.userId || requestData.UserID;
        const currentUser = authService.getCurrentUser();

        // First try current user if it's their request
        if (userId && currentUser?.id == userId) {
          const currentUserDepartment = getDepartmentFromUser(currentUser);
          if (currentUserDepartment && currentUserDepartment !== "Không xác định") {
            setDepartment(currentUserDepartment);
            setLoading(false);
            return;
          }
        }

        // Then try to fetch user data from API
        if (userId && userId !== 0) {
          const { data: userData, loading: userLoading, error: userError, department: userDepartment } = await getUserData(userId);
          
          if (userError) {
            console.error("Error fetching user data for department:", userError);
            setError(userError);
            setDepartment("Không xác định");
          } else if (userDepartment) {
            setDepartment(userDepartment);
          } else {
            setDepartment("Không xác định");
          }
        } else {
          setDepartment("Không xác định");
        }
      } catch (err) {
        console.error("Error getting department info:", err);
        setError(err.message);
        setDepartment("Không xác định");
      } finally {
        setLoading(false);
      }
    };

    getDepartmentInfo();
  }, [requestData, isFromMember, getUserData]);

  return {
    department,
    loading,
    error,
    // Helper to check if department should be displayed
    shouldShowDepartment: !isFromMember && department !== null
  };
};

/**
 * Hook for getting departments for multiple blood requests
 * Optimizes API calls by batching user data requests
 */
export const useMultipleBloodRequestDepartments = (requests = []) => {
  const { getMultipleUsersData } = useUserData();
  const [departments, setDepartments] = useState(new Map());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const getDepartmentsInfo = async () => {
      if (!requests || requests.length === 0) {
        setDepartments(new Map());
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        // Extract unique user IDs from requests
        const userIds = [...new Set(
          requests
            .filter(req => {
              // Only get departments for doctor requests
              const isMemberRequest = [
                "Gia đình", "gia đình", "Bản thân", "bạn bè", 
                "Chính bản thân tôi", "Other"
              ].includes(req.relationship);
              return !isMemberRequest;
            })
            .map(req => req.userID || req.userId || req.UserID)
            .filter(id => id && id !== 0)
        )];

        if (userIds.length === 0) {
          setDepartments(new Map());
          setLoading(false);
          return;
        }

        // Batch fetch user data
        const usersData = await getMultipleUsersData(userIds);
        
        // Create department mapping for each request
        const departmentMap = new Map();
        
        requests.forEach(request => {
          const requestId = request.requestId || request.requestID || request.id;
          const userId = String(request.userID || request.userId || request.UserID || '');
          
          // Check if it's a member request
          const isMemberRequest = [
            "Gia đình", "gia đình", "Bản thân", "bạn bè", 
            "Chính bản thân tôi", "Other"
          ].includes(request.relationship);
          
          if (isMemberRequest) {
            departmentMap.set(requestId, null); // Don't show department for member requests
          } else {
            const userData = usersData.get(userId);
            const department = userData?.department || "Không xác định";
            departmentMap.set(requestId, department);
          }
        });

        setDepartments(departmentMap);
      } catch (err) {
        console.error("Error getting multiple departments info:", err);
        setError(err.message);
        setDepartments(new Map());
      } finally {
        setLoading(false);
      }
    };

    getDepartmentsInfo();
  }, [requests, getMultipleUsersData]);

  return {
    departments,
    loading,
    error,
    // Helper to get department for specific request
    getDepartmentForRequest: (requestId) => departments.get(requestId) || "Không xác định"
  };
};

export default useBloodRequestDepartment;
