import React from "react";
import { Mo<PERSON>, Button, Alert, Typo<PERSON>, Space } from "antd";
import { UserOutlined, ExclamationCircleOutlined } from "@ant-design/icons";

const { Title, Text } = Typography;

const ProfileIncompleteModal = ({
  visible,
  onClose,
  onGoToProfile,
  profileCheckResult,
}) => {
  return (
    <Modal
      title={
        <Space>
          <ExclamationCircleOutlined style={{ color: "#faad14" }} />
          <span>Thông tin hồ sơ chưa đầy đủ</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose}>
          Đ<PERSON> sau
        </Button>,
        <Button
          key="profile"
          type="primary"
          icon={<UserOutlined />}
          onClick={onGoToProfile}
        >
          Hoàn thiện hồ sơ
        </Button>,
      ]}
      width={500}
      centered
    >
      <div style={{ padding: "16px 0" }}>
        <Alert
          message="<PERSON><PERSON> sơ của bạn chưa đầy đủ thông tin"
          description="Đ<PERSON> đăng ký hiến máu, bạn cần hoàn thiện thông tin hồ sơ cá nhân trước."
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Space direction="vertical" size={8} style={{ width: "100%" }}>
          <Text>
            <strong>Thông tin cần thiết:</strong>
          </Text>
          {profileCheckResult?.missingFields?.length > 0 ? (
            profileCheckResult.missingFields.map((field, index) => (
              <Text key={index}>• {field}</Text>
            ))
          ) : (
            <>
              <Text>• Số điện thoại</Text>
              <Text>• Địa chỉ đầy đủ</Text>
              <Text>• Ngày sinh</Text>
              <Text>• Giới tính</Text>
              <Text>• Thông tin sức khỏe cơ bản</Text>
            </>
          )}
        </Space>

        <div
          style={{
            marginTop: 16,
            padding: 16,
            backgroundColor: "#f6ffed",
            borderRadius: 6,
          }}
        >
          <Text type="secondary" style={{ fontSize: 12 }}>
            <strong>Gợi ý:</strong> Việc hoàn thiện hồ sơ sẽ giúp quá trình
            đăng ký hiến máu diễn ra nhanh chóng và thuận lợi hơn.
          </Text>
        </div>
      </div>
    </Modal>
  );
};

export default ProfileIncompleteModal;
