/* Reject Request Modal Styles */
.reject-request-modal {
  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;
  }

  .ant-modal-title {
    color: #d32f2f;
    font-weight: 600;
    font-size: 16px;
  }

  .ant-modal-body {
    padding: 24px;
  }

  .request-info-alert {
    margin-bottom: 20px;

    .ant-alert-description {
      margin-top: 8px;

      strong {
        color: #262626;
        margin-right: 8px;
      }
    }
  }

  .reason-form {
    .ant-form-item-label > label {
      font-weight: 600;
      color: #262626;
    }

    .ant-input {
      border-radius: 6px;

      &:focus,
      &:hover {
        border-color: #d32f2f;
        box-shadow: 0 0 0 2px rgba(211, 47, 47, 0.1);
      }
    }

    .ant-form-item-explain-error {
      color: #d32f2f;
    }
  }

  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;

    .ant-btn {
      border-radius: 6px;
      font-weight: 500;

      &.ant-btn-default {
        border-color: #d9d9d9;

        &:hover {
          border-color: #40a9ff;
          color: #40a9ff;
        }
      }

      &.ant-btn-primary.ant-btn-dangerous {
        background-color: #d32f2f;
        border-color: #d32f2f;

        &:hover:not(:disabled) {
          background-color: #b71c1c;
          border-color: #b71c1c;
        }

        &:disabled {
          background-color: #f5f5f5;
          border-color: #d9d9d9;
          color: #bfbfbf;
        }
      }
    }
  }
}

/* Action buttons in table */
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;

  .btn {
    border-radius: 4px;
    font-size: 12px;
    padding: 4px 8px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;

    &.btn-sm {
      font-size: 11px;
      padding: 3px 6px;
    }

    &.btn-info {
      background-color: #1890ff;
      color: white;

      &:hover {
        background-color: #40a9ff;
      }
    }

    &.btn-success {
      background-color: #52c41a;
      color: white;

      &:hover {
        background-color: #73d13d;
      }
    }

    &.btn-danger {
      background-color: #ff4d4f;
      color: white;

      &:hover {
        background-color: #ff7875;
      }
    }

    &.btn-primary {
      background-color: #1890ff;
      color: white;

      &:hover {
        background-color: #40a9ff;
      }
    }
  }
}

/* Blood type badge */
.blood-type-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;

  &.positive {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
  }

  &.negative {
    background-color: #fff2e8;
    color: #fa8c16;
    border: 1px solid #ffd591;
  }
}

/* Status badge */
.status-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;

  &.status-pending {
    background-color: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
  }

  &.status-approved {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
  }

  &.status-completed {
    background-color: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
  }

  &.status-rejected {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffb3b3;
  }
}
