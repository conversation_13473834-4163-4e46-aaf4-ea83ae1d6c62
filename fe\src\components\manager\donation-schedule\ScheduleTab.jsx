import { Table, Card, Button, Tooltip, Space, Tag } from "antd";
import { EyeOutlined, ClockCircleOutlined } from "@ant-design/icons";
import FilterSection from "./FilterSection";
import {
  formatDate,
  getNotificationStatusInfo,
  BLOOD_TYPE_OPTIONS,
  SCHEDULE_SORT_OPTIONS
} from "../../../utils/donationScheduleHelpers";
import "../../../styles/components/BloodRequestTable.scss";

/**
 * Schedule tab component for registered donations
 */
const ScheduleTab = ({
  donations,
  filters,
  onFilterChange,
  scheduleSort,
  onSortChange,
  loading,
  onViewDetails,
  onViewWorkflow,
}) => {
  const columns = [
    {
      title: "Mã lịch hẹn",
      dataIndex: "id",
      key: "appointmentId",
      width: 120,
      render: (id) => <span className="request-id">#{id}</span>,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "<PERSON><PERSON><PERSON> hẹn",
      dataIndex: "appointmentDate",
      key: "appointmentDate",
      width: 130,
      sorter: true,
      render: (date) => formatDate(date),
    },
    {
      title: "Nhóm máu",
      dataIndex: "bloodType",
      key: "bloodType",
      width: 100,
      align: "center",
      sorter: true,
      render: (bloodType) => (
        <Tag color="#D93E4C" style={{ fontWeight: "bold" }}>
          {bloodType}
        </Tag>
      ),
    },
    {
      title: "Lượng máu",
      dataIndex: "expectedQuantity",
      key: "expectedQuantity",
      width: 150,
      align: "center",
      sorter: true,
      render: (quantity) => (
        <span style={{ fontWeight: 600, color: "#20374E" }}>
          {quantity}
        </span>
      ),
    },
    {
      title: "Tên người hiến",
      dataIndex: "donorName",
      key: "donorName",
      width: 180,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 600 }}>{text}</div>
          <div style={{ fontSize: "0.8rem", color: "#666" }}>
            ID: {record.donorId}
          </div>
        </div>
      ),
    },

    {
      title: "Hành động",
      key: "actions",
      width: 150,
      render: (_, record) => (
        <Space className="action-buttons">
          <Tooltip title="Xem chi tiết">
            <Button
              type="primary"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => onViewDetails(record)}
            />
          </Tooltip>
          <Tooltip title="Quy trình hiến máu">
            <Button
              type="default"
              icon={<ClockCircleOutlined />}
              size="small"
              onClick={() => onViewWorkflow(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const filterOptions = {
    bloodType: {
      label: "Nhóm máu:",
      options: BLOOD_TYPE_OPTIONS,
    },
    sort: {
      label: "Sắp xếp theo:",
      options: SCHEDULE_SORT_OPTIONS,
    },
  };

  return (
    <div className="schedule-tab-content">
      <FilterSection
        filters={filters}
        onFilterChange={onFilterChange}
        sortValue={`${scheduleSort.field}-${scheduleSort.order}`}
        onSortChange={(value) => {
          const [field, order] = value.split("-");
          onSortChange({ field, order });
        }}
        filterOptions={filterOptions}
      />

      <Card className="donations-table-card">
        <Table
          className="donation-schedule-table blood-request-table"
          dataSource={donations}
          columns={columns}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} lịch hẹn`,
          }}
          scroll={{ x: 1320 }}
          onChange={(_, __, sorter) => {
            if (sorter.field) {
              onSortChange({
                field: sorter.field,
                order: sorter.order === "ascend" ? "asc" : "desc",
              });
            }
          }}
        />
      </Card>
    </div>
  );
};

export default ScheduleTab;
