import { <PERSON><PERSON>, But<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Spin } from "antd";
import { useState, useEffect } from "react";
import { DONATION_STATUSES } from "../../shared/ProcessWorkflowModal";
import {
  formatDate,
  formatTimeSlot,
  getNotificationStatusInfo
} from "../../../utils/donationScheduleHelpers";
import bloodDonationService from "../../../services/bloodDonationService";
import { getGenderText } from "../../../utils/genderUtils";

/**
 * Modal component for displaying detailed donation information
 */
const DonationDetailModal = ({
  visible,
  onCancel,
  donation,
  onSendReminder,
}) => {
  const [detailedData, setDetailedData] = useState(null);
  const [loading, setLoading] = useState(false);

  // Fetch detailed appointment data when modal opens
  useEffect(() => {
    if (visible && donation?.id) {
      fetchDetailedData();
    }
  }, [visible, donation?.id]);

  const fetchDetailedData = async () => {
    try {
      setLoading(true);
      const response = await bloodDonationService.getAppointmentDetails(donation.id);
      setDetailedData(response);
    } catch (error) {
      console.error("Error fetching detailed appointment data:", error);
    } finally {
      setLoading(false);
    }
  };

  if (!donation) return null;

  // Use detailed data if available, otherwise fallback to original donation data
  const displayData = detailedData || donation;

  return (
    <Modal
      title={`Chi tiết lịch hẹn: ${displayData.donorName || donation.donorName}`}
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="close" onClick={onCancel}>
          Đóng
        </Button>,
        donation.status === DONATION_STATUSES.REGISTERED && (
          <Button
            key="send-reminder"
            type="primary"
            onClick={() => {
              onSendReminder(donation);
              onCancel();
            }}
          >
            Gửi lại thông báo
          </Button>
        ),
      ]}
      width={700}
    >
      <Spin spinning={loading}>
        <div className="donation-details">
          <Row gutter={[16, 16]}>
            {/* Basic Information */}
            <Col span={12}>
              <div className="detail-item">
                <strong>Họ tên:</strong> {donation.donorName}
              </div>
            </Col>
            <Col span={12}>
              <div className="detail-item">
                <strong>ID người hiến:</strong> {donation.donorId}
              </div>
            </Col>
            <Col span={12}>
              <div className="detail-item">
                <strong>Nhóm máu:</strong>
                <Tag color="#D93E4C" style={{ marginLeft: 8 }}>
                  {donation.bloodType}
                </Tag>
              </div>
            </Col>
            <Col span={12}>
              <div className="detail-item">
                <strong>Số điện thoại:</strong>
                <a
                  href={`tel:${donation.donorPhone}`}
                  style={{ marginLeft: 8 }}
                >
                  {donation.donorPhone}
                </a>
              </div>
            </Col>
            <Col span={24}>
              <div className="detail-item">
                <strong>Email:</strong>
                <a
                  href={`mailto:${donation.donorEmail}`}
                  style={{ marginLeft: 8 }}
                >
                  {donation.donorEmail}
                </a>
              </div>
            </Col>

            {/* Additional donor details from Information API */}
            {donation.donorDetails?.dateOfBirth && (
              <Col span={12}>
                <div className="detail-item">
                  <strong>Ngày sinh:</strong> {formatDate(donation.donorDetails.dateOfBirth)}
                </div>
              </Col>
            )}
            {donation.donorDetails?.gender && (
              <Col span={12}>
                <div className="detail-item">
                  <strong>Giới tính:</strong> {getGenderText(donation.donorDetails.gender)}
                </div>
              </Col>
            )}
            {donation.donorDetails?.identityCard && (
              <Col span={12}>
                <div className="detail-item">
                  <strong>CCCD/CMND:</strong> {donation.donorDetails.identityCard}
                </div>
              </Col>
            )}
            {donation.donorDetails?.occupation && (
              <Col span={12}>
                <div className="detail-item">
                  <strong>Nghề nghiệp:</strong> {donation.donorDetails.occupation}
                </div>
              </Col>
            )}
            {donation.donorDetails?.emergencyContact && (
              <Col span={24}>
                <div className="detail-item">
                  <strong>Liên hệ khẩn cấp:</strong> {donation.donorDetails.emergencyContact}
                </div>
              </Col>
            )}

            {/* Appointment Information */}
            <Col span={12}>
              <div className="detail-item">
                <strong>Ngày đăng ký:</strong> {formatDate(donation.registrationDate)}
              </div>
            </Col>
            <Col span={12}>
              <div className="detail-item">
                <strong>Lượng máu dự kiến:</strong>{" "}
                <span style={{ fontWeight: 600, color: "#20374E" }}>
                  {donation.expectedQuantity || "450ml"}
                </span>
              </div>
            </Col>
            <Col span={12}>
              <div className="detail-item">
                <strong>Ngày hẹn:</strong> {formatDate(donation.appointmentDate)}
              </div>
            </Col>
            <Col span={12}>
              <div className="detail-item">
                <strong>Ca hiến:</strong> {formatTimeSlot(donation.timeSlot)}
              </div>
            </Col>
            <Col span={24}>
              <div className="detail-item">
                <strong>Địa chỉ:</strong> {donation.location?.address || "Chưa có"}
              </div>
            </Col>
            {donation.location?.distance > 0 && (
              <Col span={12}>
                <div className="detail-item">
                  <strong>Khoảng cách:</strong> {donation.location.distance} km
                </div>
              </Col>
            )}

            {/* Health Information */}
            <Col span={24}>
              <Divider orientation="left">Thông tin khám sức khỏe</Divider>
            </Col>

            {/* Basic Health Info */}
            <Col span={12}>
              <div className="detail-item">
                <strong>💓 Nhịp tim:</strong>{" "}
                {displayData.heartRate ? `${displayData.heartRate} bpm` : "Chưa có"}
              </div>
            </Col>
            <Col span={12}>
              <div className="detail-item">
                <strong>🩸 Huyết áp:</strong>{" "}
                {displayData.bloodPressure || "Chưa có"}
              </div>
            </Col>
            <Col span={12}>
              <div className="detail-item">
                <strong>🔬 Huyết sắc tố:</strong>{" "}
                {displayData.hemoglobin ? `${displayData.hemoglobin} g/dL` : "Chưa có"}
              </div>
            </Col>
            <Col span={12}>
              <div className="detail-item">
                <strong>🌡️ Nhiệt độ:</strong>{" "}
                {displayData.temperature ? `${displayData.temperature}°C` : "Chưa có"}
              </div>
            </Col>

            {/* Weight and Height */}
            <Col span={12}>
              <div className="detail-item">
                <strong>⚖️ Cân nặng:</strong>{" "}
                {displayData.weight || displayData.healthSurvey?.weight
                  ? `${displayData.weight || displayData.healthSurvey.weight} kg`
                  : "Chưa có"}
              </div>
            </Col>
            <Col span={12}>
              <div className="detail-item">
                <strong>📏 Chiều cao:</strong>{" "}
                {displayData.height || displayData.healthSurvey?.height
                  ? `${displayData.height || displayData.healthSurvey.height} cm`
                  : "Chưa có"}
              </div>
            </Col>

            {/* Medical History */}
            {donation.donorDetails?.medicalHistory && (
              <Col span={24}>
                <div className="detail-item">
                  <strong>Tiền sử bệnh án:</strong> {donation.donorDetails.medicalHistory}
                </div>
              </Col>
            )}

            {/* Notification Status for Registered donations */}
            {donation.status === DONATION_STATUSES.REGISTERED && (
              <>
                <Col span={12}>
                  <div className="detail-item">
                    <strong>Trạng thái thông báo:</strong>{" "}
                    {(() => {
                      const statusInfo = getNotificationStatusInfo(donation.notificationStatus);
                      return (
                        <Tag color={statusInfo.color}>
                          {statusInfo.text}
                        </Tag>
                      );
                    })()}
                  </div>
                </Col>
                {donation.notificationStatus === "sent" && donation.notificationSentAt && (
                  <Col span={12}>
                    <div className="detail-item">
                      <strong>Ngày gửi nhắc nhở:</strong> {formatDate(donation.notificationSentAt)}
                    </div>
                  </Col>
                )}
              </>
            )}

            {/* Notes */}
            <Col span={24}>
              <div className="detail-item">
                <strong>Ghi chú:</strong> {displayData.notes || donation.notes || "Không có ghi chú"}
              </div>
            </Col>
          </Row>
        </div>
      </Spin>
    </Modal>
  );
};

export default DonationDetailModal;
