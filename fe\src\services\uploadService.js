import { apiClient } from "./axiosInstance";
import config from "../config/environment";

const API_BASE = config.api.baseUrl;

// Convert file to base64
export const convertToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};

export const uploadImage = async (file) => {
  try {
    // Convert to base64 for database storage
    const base64String = await convertToBase64(file);

    return {
      success: true,
      url: base64String, // Return base64 string directly
      message: "Upload thành công",
    };
  } catch (error) {
    console.error("Upload error:", error);
    return {
      success: false,
      message: "Không thể convert ảnh. Vui lòng thử lại.",
    };
  }
};

// Alternative: Upload to server if needed
export const uploadImageToServer = async (file) => {
  try {
    const formData = new FormData();
    formData.append("image", file);

    const response = await apiClient.upload(`/api/upload`, formData);

    return {
      success: true,
      url: response.data.url || response.data.imgUrl || response.data,
      message: "Upload thành công",
    };
  } catch (error) {
    console.error("Upload error:", error);

    // Fallback to base64 if server upload fails
    try {
      const base64String = await convertToBase64(file);
      return {
        success: true,
        url: base64String,
        message: "Upload thành công (base64)",
        isLocal: true,
      };
    } catch (localError) {
      return {
        success: false,
        message:
          error.response?.data?.message ||
          "Không thể upload ảnh. Vui lòng thử lại.",
      };
    }
  }
};

export const uploadMultipleImages = async (files) => {
  try {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append("files", file);
    });

    const response = await apiClient.upload(
      `${API_BASE}/upload/images`,
      formData
    );

    return response.data;
  } catch (error) {
    console.error("Upload error:", error);
    throw new Error("Không thể upload ảnh. Vui lòng thử lại.");
  }
};

// Upload PDF medical report
export const uploadPdfMedicalReport = async (file) => {
  try {
    const formData = new FormData();
    formData.append("file", file);
    // Lấy URL từ biến môi trường
    const uploadUrl =
      import.meta.env.VITE_UPLOAD_PDF_API ||
      "https://localhost:7021/api/UploadFile/upload";

    console.log("📤 Uploading PDF to:", uploadUrl);

    const response = await fetch(uploadUrl, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(
        `Upload PDF thất bại: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    console.log("✅ Upload PDF response:", data);

    return {
      success: true,
      url: data.url || data.fileUrl || data.path || data,
      message: "Upload PDF thành công",
    };
  } catch (error) {
    console.error("❌ Upload PDF error:", error);
    return {
      success: false,
      message: error.message || "Không thể upload PDF. Vui lòng thử lại.",
    };
  }
};

// Upload image for doctor articles using the same API
export const uploadImageForDoctorArticle = async (file) => {
  try {
    const formData = new FormData();
    formData.append("file", file);
    // Sử dụng cùng API endpoint như upload PDF
    const uploadUrl =
      import.meta.env.VITE_UPLOAD_PDF_API ||
      "https://localhost:7021/api/UploadFile/upload";

    console.log("📤 Uploading image for doctor article to:", uploadUrl);

    const response = await fetch(uploadUrl, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(
        `Upload ảnh thất bại: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    console.log("✅ Upload image response:", data);

    return {
      success: true,
      url: data.url || data.fileUrl || data.path || data,
      message: "Upload ảnh thành công",
    };
  } catch (error) {
    console.error("❌ Upload image error:", error);
    // Fallback to base64 if API upload fails
    try {
      console.log("🔄 Fallback to base64 for image...");
      const base64String = await convertToBase64(file);
      return {
        success: true,
        url: base64String,
        message: "Upload ảnh thành công (base64)",
        isLocal: true,
      };
    } catch (localError) {
      return {
        success: false,
        message: error.message || "Không thể upload ảnh. Vui lòng thử lại.",
      };
    }
  }
};
