import React, { useState } from "react";
import {
  Form,
  Input,
  <PERSON><PERSON>,
  <PERSON>,
  Typography
} from "antd";
import {
  LockOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone
} from "@ant-design/icons";
import ManagerLayout from "../../components/manager/ManagerLayout";
import authService from "../../services/authService";
import { toast } from "../../utils/toastUtils";
import "../../styles/pages/ChangePasswordPage.scss";

const { Title } = Typography;

const ManagerChangePasswordPage = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleChangePassword = async (values) => {
    try {
      setLoading(true);

      const changePasswordData = {
        currentPassword: values.currentPassword,
        newPassword: values.newPassword
      };

      console.log("Manager changing password...", { userId: authService.getCurrentUser()?.id });
      const result = await authService.changePassword(changePasswordData);
      console.log("Manager change password result:", result);

      if (result.success) {
        toast.success('Đổi mật khẩu thành công!');
        form.resetFields();

        // Optional: Force logout to ensure user uses new password
        setTimeout(() => {
          toast.info('Vui lòng đăng nhập lại với mật khẩu mới');
          authService.logout();
          window.location.href = '/login';
        }, 2000);
      } else {
        toast.error(result.error || 'Đổi mật khẩu thất bại. Vui lòng kiểm tra lại mật khẩu hiện tại!');
      }
    } catch (error) {
      console.error("Manager change password error:", error);
      console.error("Manager change password error response:", error.response?.data);
      toast.error('Đổi mật khẩu thất bại. Vui lòng kiểm tra lại mật khẩu hiện tại!');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ManagerLayout>
      <div className="change-password-page">
        <Card className="change-password-card">
          <Title level={2} className="page-title">
            Đặt lại mật khẩu
          </Title>
          
          <Form
            form={form}
            layout="vertical"
            onFinish={handleChangePassword}
            autoComplete="off"
            className="change-password-form"
          >
            <Form.Item
              label="Mật khẩu hiện tại"
              name="currentPassword"
              rules={[
                { required: true, message: "Vui lòng nhập mật khẩu hiện tại!" },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Nhập mật khẩu hiện tại"
                size="large"
                iconRender={(visible) => 
                  visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                }
              />
            </Form.Item>

            <Form.Item
              label="Mật khẩu mới"
              name="newPassword"
              rules={[
                { required: true, message: "Vui lòng nhập mật khẩu mới!" },
                { min: 6, message: "Mật khẩu phải có ít nhất 6 ký tự!" },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Nhập mật khẩu mới"
                size="large"
                iconRender={(visible) => 
                  visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                }
              />
            </Form.Item>

            <Form.Item
              label="Xác nhận mật khẩu mới"
              name="confirmPassword"
              dependencies={['newPassword']}
              rules={[
                { required: true, message: "Vui lòng xác nhận mật khẩu mới!" },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('newPassword') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('Mật khẩu xác nhận không khớp!'));
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Xác nhận mật khẩu mới"
                size="large"
                iconRender={(visible) => 
                  visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                }
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
                block
                className="submit-button"
              >
                Đổi mật khẩu
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </ManagerLayout>
  );
};

export default ManagerChangePasswordPage;
