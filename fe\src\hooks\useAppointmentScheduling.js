import { useState, useEffect } from "react";
import dayjs from "dayjs";
import bloodDonationService from "../services/bloodDonationService";
import RemindService from "../services/remindService";
import axiosInstance from "../services/axiosInstance";
import config from "../config/environment";


export const useAppointmentScheduling = (
  currentUser,
  healthSurvey,
  appointmentData,
  setLoading,
  setRegistrationResult
) => {
  // Add a local loading state to track submission status
  const [isSubmitting, setIsSubmitting] = useState(false);
  // State để lưu thông tin SelfReportedLastDonationDate từ database
  const [selfReportedLastDonationDate, setSelfReportedLastDonationDate] = useState(null);

  // Backend will now handle complex donation history validation logic

  // Function để lấy thông tin SelfReportedLastDonationDate từ database
  const fetchSelfReportedLastDonationDate = async (userId) => {
    try {
      const userInfo = await bloodDonationService.getUserInfo(userId);

      const selfReportedDate = userInfo.selfReportedLastDonationDate ||
        userInfo.SelfReportedLastDonationDate ||
        userInfo.self_reported_last_donation_date;

      // Kiểm tra nếu selfReportedDate là null hoặc undefined
      if (!selfReportedDate) {
        return null;
      }

      // Kiểm tra nếu ngày là special values nghĩa là "user chọn Không"
      if (selfReportedDate.startsWith &&
        (selfReportedDate.startsWith("1900-01-01") ||
          selfReportedDate.startsWith("0001-01-01"))) {
        return null;
      }

      return selfReportedDate;
    } catch (error) {
      return null;
    }
  };

  // Function để refresh SelfReportedLastDonationDate
  const refreshSelfReportedDate = async () => {
    if (currentUser?.id || currentUser?.userID) {
      let userId = currentUser?.id || currentUser?.userID;

      // Also try to get userId from JWT token as fallback
      const token = localStorage.getItem("authToken");
      if (token && (!userId || userId === "0" || userId === 0)) {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          const jwtUserId = payload["http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier"];
          if (jwtUserId && jwtUserId !== "0") {
            userId = jwtUserId;
          }
        } catch (e) {
          // Error parsing JWT
        }
      }

      const parsedUserId = parseInt(userId);
      if (!isNaN(parsedUserId) && parsedUserId > 0) {
        const selfReportedDate = await fetchSelfReportedLastDonationDate(parsedUserId);
        setSelfReportedLastDonationDate(selfReportedDate);
      }
    }
  };

  // Load SelfReportedLastDonationDate khi component mount hoặc khi user thay đổi
  useEffect(() => {
    refreshSelfReportedDate();
  }, [currentUser]);

  // Reload khi healthSurvey.hasDonatedBefore thay đổi để lấy data mới nhất
  useEffect(() => {
    if (healthSurvey.hasDonatedBefore !== null) {
      // Delay một chút để đảm bảo API đã được gọi và lưu xong
      setTimeout(() => {
        refreshSelfReportedDate();
      }, 1000);
    }
  }, [healthSurvey.hasDonatedBefore, healthSurvey.lastDonationDate]);

  // Function để tính toán ngày có thể hiến máu dựa trên SelfReportedLastDonationDate
  const calculateEarliestDonationDate = (lastDonationDate) => {
    if (!lastDonationDate) return null;
    return dayjs(lastDonationDate).add(84, "day");
  };

  const getTimeSlotText = (slot) => {
    return slot === "morning" ? "7:00 - 12:00 (Sáng)" : "13:00 - 17:00 (Chiều)";
  };

  const validateAppointmentData = () => {
    if (!appointmentData.preferredDate) {
      alert("Vui lòng chọn ngày đặt lịch!");
      return false;
    }
    if (!appointmentData.timeSlot) {
      alert("Vui lòng chọn khung giờ đặt lịch!");
      return false;
    }
    if (!healthSurvey.weight) {
      alert("Vui lòng nhập cân nặng!");
      return false;
    }

    // Validate 84-day gap - backend will handle the complex logic
  
    if (healthSurvey.hasDonatedBefore === true && selfReportedLastDonationDate) {
      const lastDonationDate = dayjs(selfReportedLastDonationDate);

      if (lastDonationDate.isValid() && !lastDonationDate.isBefore(dayjs("1900-01-01"))) {
        const appointmentDate = dayjs(appointmentData.preferredDate);
        const daysDifference = appointmentDate.diff(lastDonationDate, "day");

        if (daysDifference < 84) {
          const earliestDate = lastDonationDate.add(84, "day");
          alert(
            `Bạn cần chờ ít nhất 84 ngày từ lần hiến máu gần nhất (${lastDonationDate.format(
              "DD/MM/YYYY"
            )}). Ngày sớm nhất có thể hiến máu là: ${earliestDate.format(
              "DD/MM/YYYY"
            )}`
          );
          return false;
        }
      }
    }

    return true;
  };

  const handleAppointmentSubmit = async () => {
    // Prevent multiple submissions
    if (isSubmitting) {
      return;
    }

    setIsSubmitting(true);
    setLoading(true);

    // Validate required fields for appointment
    if (!validateAppointmentData()) {
      setIsSubmitting(false);
      setLoading(false);
      return;
    }

    try {
      // Step 1: Validate user ID first
      let userId = currentUser?.id || currentUser?.userID;

      // Also try to get userId from JWT token as fallback
      const token = localStorage.getItem("authToken");
      if (token && (!userId || userId === "0" || userId === 0)) {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          const jwtUserId = payload["http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier"];
          if (jwtUserId && jwtUserId !== "0") {
            userId = jwtUserId;
          }
        } catch (e) {
          console.error("Error parsing JWT:", e);
        }
      }

      // Convert to integer and validate
      const parsedUserId = parseInt(userId);

      if (!userId || isNaN(parsedUserId) || parsedUserId === 0) {
        setIsSubmitting(false);
        setLoading(false);
        alert("Lỗi: Không tìm thấy thông tin người dùng hợp lệ. Vui lòng đăng xuất và đăng nhập lại!");
        return;
      }

      // Step 2: New donation history logic - let backend handle the complex validation
      if (healthSurvey.hasDonatedBefore === true) {
        if (!healthSurvey.lastDonationDate) {
          // User said "Yes" but didn't provide a date
          // Backend will check:
          // 1. System appointment records (process >= 2, status = true)
          // 2. If no system records found, check selfReportedLastDonationDate
          // 3. If selfReportedLastDonationDate is null, backend will return error asking for manual input
        }
      } else if (healthSurvey.hasDonatedBefore === false) {
        // Will save null to database (which becomes 1900-01-01)
      }

      // Step 3: Ensure self-reported donation data is properly saved
      // Backend requires this data before creating appointment
      if (healthSurvey.hasDonatedBefore === true && healthSurvey.lastDonationDate) {
        const selfReportedDate = dayjs(healthSurvey.lastDonationDate).format("YYYY-MM-DD");
        try {
          await bloodDonationService.updateSelfReportedDonation(parsedUserId, selfReportedDate);
          // Add delay to ensure backend processes the data
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (error) {
          setIsSubmitting(false);
          setLoading(false);
          alert("Không thể lưu thông tin hiến máu. Vui lòng thử lại.");
          return;
        }
      } else if (healthSurvey.hasDonatedBefore === false) {
        try {
          await bloodDonationService.updateSelfReportedDonation(parsedUserId, null);
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (error) {
          setIsSubmitting(false);
          setLoading(false);
          alert("Không thể lưu thông tin hiến máu. Vui lòng thử lại.");
          return;
        }
      } else if (healthSurvey.hasDonatedBefore === true && !healthSurvey.lastDonationDate) {
        try {
          const systemCheckMarker = "1900-01-02";
          await bloodDonationService.updateSelfReportedDonation(parsedUserId, systemCheckMarker);
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (error) {
          setIsSubmitting(false);
          setLoading(false);
          alert("Không thể lưu thông tin hiến máu. Vui lòng thử lại.");
          return;
        }
      }

      // Step 4: Final validation before appointment creation
      // Backend sẽ xử lý logic validation phức tạp khi tạo appointment

      // Step 5: Prepare appointment data

      // Step 6: Frontend validation is now simplified - backend will handle complex logic
      // We only do basic validation if user provided a self-reported date
      let lastDonationDate = null;
      if (healthSurvey.hasDonatedBefore === true && healthSurvey.lastDonationDate) {
        lastDonationDate = dayjs(healthSurvey.lastDonationDate);

        // Check if donation date is in the future
        const today = dayjs();
        if (lastDonationDate.isAfter(today)) {
          setIsSubmitting(false);
          setLoading(false);
          alert(
            `Ngày hiến máu gần nhất (${lastDonationDate.format("DD/MM/YYYY")}) không thể là ngày trong tương lai. ` +
            `Vui lòng kiểm tra lại ngày hiến máu.`
          );
          return;
        }

        // Basic frontend validation for user experience
        const appointmentDate = dayjs(appointmentData.preferredDate);
        const daysDifference = appointmentDate.diff(lastDonationDate, "day");

        if (daysDifference < 84) {
          const earliestDate = lastDonationDate.add(84, "day");
          const confirmProceed = confirm(
            `Ngày bạn chọn chưa đủ điều kiện hiến máu sau 84 ngày so với lần hiến máu gần nhất của bạn: (${lastDonationDate.format("DD/MM/YYYY")}). ` +
            `Ngày sớm nhất có thể hiến máu là: ${earliestDate.format("DD/MM/YYYY")}.`
          );

          if (!confirmProceed) {
            setIsSubmitting(false);
            setLoading(false);
            return;
          }
        }
      }

      // Step 7: Final check before creating appointment

      // Based on Swagger API documentation, backend expects this exact structure:
      let appointmentDate = dayjs(appointmentData.preferredDate)
        .hour(appointmentData.timeSlot === "morning" ? 9 : 15)
        .minute(0)
        .second(0)
        .millisecond(0);

      const basePayload = {
        userId: parsedUserId, // Use the validated parsedUserId
        appointmentDate: appointmentDate.format("YYYY-MM-DDTHH:mm:ss.SSS[Z]"), // ISO format with timezone
        timeSlot:
          appointmentData.timeSlot === "morning"
            ? "Sáng (7:00-12:00)"
            : "Chiều (13:00-17:00)",
        process: 1, // 1: đã đăng ký, chờ khám sàng lọc
        status: null, // null: đã đăng ký, chờ bác sĩ duyệt
      };



      // Try alternative payload formats to handle validation
      const alternativePayloads = [
        basePayload,
        // Try with different date format
        {
          ...basePayload,
          appointmentDate: appointmentDate.toISOString(),
        },
        // Try with simplified timeSlot
        {
          ...basePayload,
          timeSlot: appointmentData.timeSlot === "morning" ? "morning" : "afternoon",
        },
        // Try with both date formats and simplified timeSlot
        {
          ...basePayload,
          appointmentDate: appointmentDate.toISOString(),
          timeSlot: appointmentData.timeSlot === "morning" ? "morning" : "afternoon",
        },
      ];

      // Log the core payload that matches API schema
      // Try different payload formats to handle validation

      let successfulPayload = null;
      let lastError = null;

      // Try each alternative payload
      for (let i = 0; i < alternativePayloads.length; i++) {
        const payload = alternativePayloads[i];

        try {
          const response = await bloodDonationService.createAppointment(payload);
          successfulPayload = payload;

          // Tạo thông báo đăng ký thành công với nội dung chính xác theo yêu cầu
          try {
            const notificationData = {
              userId: parsedUserId,
              title: "Đăng ký hiến máu thành công",
              message: "Bạn đã tạo hẹn hiến máu thành công",
              type: "Reminder", // Sử dụng type Reminder để hiển thị đúng icon
              relatedId: response.data?.appointmentId || Date.now(),
              isRead: false,
              createdBy: "system",
              sentAt: new Date().toISOString(),
            };

            await axiosInstance.post(config.api.notification, notificationData);
          } catch (notificationError) {
            console.warn("Lỗi khi tạo thông báo:", notificationError);
            // Không làm fail việc tạo appointment nếu thông báo lỗi
          }

          // Set success result to show ResultDisplay
          setRegistrationResult({
            status: "scheduled",
            message: "ĐẶT LỊCH THÀNH CÔNG",
            description: "Lịch hẹn hiến máu của bạn đã được đặt thành công. Vui lòng đến đúng giờ theo lịch hẹn.",
            appointmentId: response.data?.appointmentId || response.appointmentId
          });

          setIsSubmitting(false);
          setLoading(false);
          return;

        } catch (error) {
          lastError = error;
        }
      }

      // If all payloads failed, throw the last error
      if (!successfulPayload) {
        throw lastError;
      }
    } catch (error) {

      // Handle specific donation interval error from backend
      const donationIntervalError = error.response?.data;
      if (typeof donationIntervalError === "string") {
        // Handle 84-day interval error
        if (donationIntervalError.includes("Chưa đủ thời gian nghỉ")) {
          const dateMatch = donationIntervalError.match(/(\d{2}\/\d{2}\/\d{4})/);
          if (dateMatch) {
            const lastDonationDateStr = dateMatch[1];
            const [day, month, year] = lastDonationDateStr.split('/');
            const lastDonationDate = dayjs(`${year}-${month}-${day}`);
            const minNextDate = lastDonationDate.add(84, "days");

            setIsSubmitting(false);
            setLoading(false);
            alert(
              `Bạn đã hiến máu vào ${lastDonationDateStr}. ` +
              `Cần chờ ít nhất 84 ngày. ` +
              `Ngày sớm nhất có thể hiến lại: ${minNextDate.format("DD/MM/YYYY")}`
            );
            return;
          }
        }

        // Handle missing self-reported date error
        if (donationIntervalError.includes("cần nhập ngày hiến máu") ||
          donationIntervalError.includes("self-reported") ||
          donationIntervalError.includes("manual input")) {
          setIsSubmitting(false);
          setLoading(false);
          alert(
            "Bạn đã chọn 'Có' cho câu hỏi hiến máu nhưng hệ thống không tìm thấy lịch sử hiến máu trong hệ thống. " +
            "Vui lòng quay lại bước khảo sát sức khỏe và nhập ngày hiến máu gần nhất để đảm bảo khoảng cách 84 ngày an toàn."
          );
          return;
        }
      }

      // Handle different types of API errors
      let errorMessage = "Có lỗi xảy ra khi đặt lịch. Vui lòng thử lại.";

      if (error.response) {
        // Server responded with error status
        const status = error.response.status;
        const data = error.response.data;

        if (status === 400) {
          // Try to extract detailed error message from server response
          if (typeof data === "string") {
            // Check for specific UserId error
            if (data.includes("UserId 0 does not exist") || data.includes("UserId") && data.includes("does not exist")) {
              errorMessage = "Lỗi xác thực người dùng. Vui lòng đăng xuất và đăng nhập lại.";
            } else if (data.includes("future") || data.includes("tương lai")) {
              errorMessage = "Ngày hiến máu không thể là ngày trong tương lai. Vui lòng kiểm tra lại thông tin.";
            } else if (data.includes("84") || data.includes("day")) {
              // Extract date from the error message and format it nicely
              const dateMatch = data.match(/\((\d{2}\/\d{2}\/\d{4})\)/);
              if (dateMatch) {
                const lastDonationDate = dateMatch[1];
                errorMessage = `Bạn cần chờ ít nhất 84 ngày từ lần hiến máu gần nhất (${lastDonationDate}). Hãy quay lại sau khi đủ thời gian quy định để đảm bảo sức khỏe của bạn.`;
              } else {
                errorMessage = data; // Keep the original message if we can't parse it
              }
            } else {
              errorMessage = data;
            }
          } else if (data?.message) {
            errorMessage = data.message;
          } else if (data?.errors) {
            // Handle validation errors
            const validationErrors = Object.values(data.errors).flat();
            errorMessage = validationErrors.join(", ");
          } else if (data?.title) {
            errorMessage = data.title;
          } else {
            errorMessage =
              "Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.";
          }
        } else if (status === 401) {
          errorMessage = "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.";
        } else if (status === 500) {
          // Handle server errors including NullReferenceException
          if (typeof data === "string" && data.includes("NullReferenceException")) {
            errorMessage = "Có lỗi xảy ra trong hệ thống khi xử lý lịch sử hiến máu. Vui lòng thử lại hoặc liên hệ hỗ trợ.";
          } else {
            errorMessage = "Lỗi hệ thống. Vui lòng thử lại sau ít phút.";
          }
        } else if (status === 409) {
          errorMessage =
            data?.message || "Lịch hẹn bị trùng. Vui lòng chọn thời gian khác.";
        } else if (status >= 500) {
          // Check for specific SQL null value errors
          if (typeof data === "string" && data.includes("SqlNullValueException")) {
            errorMessage = "Lỗi dữ liệu hệ thống. Vui lòng liên hệ quản trị viên.";
          } else {
            errorMessage = "Lỗi hệ thống. Vui lòng thử lại sau.";
          }
        } else {
          errorMessage = data?.message || errorMessage;
        }
      } else if (error.request) {
        // Network error
        errorMessage =
          "Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng.";
      }

      setRegistrationResult({
        status: "error",
        message: "Không thể đặt lịch hẹn",
        description: errorMessage,
      });
    } finally {
      setIsSubmitting(false);
      setLoading(false);
    }
  };

  return {
    getTimeSlotText,
    validateAppointmentData,
    handleAppointmentSubmit,
    calculateEarliestDonationDate,
    selfReportedLastDonationDate,
    refreshSelfReportedDate,
  };
};
