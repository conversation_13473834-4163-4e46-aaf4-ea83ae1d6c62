import React, { useState, useEffect } from "react";
import ManagerLayout from "../../components/manager/ManagerLayout";
import ProfileUpdateForm from "../../components/shared/ProfileUpdateForm";
import { getUserInfo } from "../../services/informationService";
import { toast } from "../../utils/toastUtils";
import authService from "../../services/authService";
import "../../styles/pages/ProfilePage.scss";

const ManagerProfilePage = () => {
  const [userInfo, setUserInfo] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUserInfo();
  }, []);

  const fetchUserInfo = async () => {
    try {
      setLoading(true);
      const currentUserData = await getUserInfo(); // This now returns only current user data

      console.log("Current user data from API:", currentUserData);

      if (currentUserData) {
        setUserInfo(currentUserData);
      } else {
        toast.error("Không tìm thấy thông tin người dùng hiện tại!");
      }
    } catch (error) {
      console.error("Error fetching user info:", error);
      toast.error("Không thể tải thông tin người dùng!");
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateSuccess = (updatedInfo) => {
    setUserInfo(prev => ({ ...prev, ...updatedInfo }));
  };

  return (
    <ManagerLayout>
      <div className="profile-page">
        <ProfileUpdateForm
          userInfo={userInfo}
          onUpdateSuccess={handleUpdateSuccess}
          title="Cập nhật hồ sơ quản lý"
        />
      </div>
    </ManagerLayout>
  );
};

export default ManagerProfilePage;
