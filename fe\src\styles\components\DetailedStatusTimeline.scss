@use "../base/variables" as vars;

.detailed-status-timeline {
  .ant-card-head {
    background: linear-gradient(135deg, #f0f8ff, #e6f7ff);
    border-bottom: 1px solid #d9d9d9;
    
    .ant-card-head-title {
      font-weight: 600;
      color: vars.$text-primary;
    }
  }

  .status-timeline {
    margin: 16px 0;
    
    .ant-timeline-item {
      padding-bottom: 16px;
      
      .ant-timeline-item-content {
        margin-left: 24px;
        min-height: 48px;
      }
      
      .timeline-item {
        .timeline-content {
          padding: 8px 12px;
          background: #fafafa;
          border-radius: 6px;
          border-left: 3px solid #1890ff;
          
          .ant-typography {
            margin-bottom: 4px;
            
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
      
      // Styling cho các loại status khác nhau
      &:has(.timeline-content:contains("Tạo yêu cầu")) {
        .timeline-content {
          border-left-color: #1890ff;
          background: #f0f8ff;
        }
      }
      
      &:has(.timeline-content:contains("<PERSON><PERSON> chấp nhận")) {
        .timeline-content {
          border-left-color: #52c41a;
          background: #f6ffed;
        }
      }
      
      &:has(.timeline-content:contains("Hoàn thành")) {
        .timeline-content {
          border-left-color: #1890ff;
          background: #e6f7ff;
        }
      }
      
      &:has(.timeline-content:contains("Từ chối")) {
        .timeline-content {
          border-left-color: #ff4d4f;
          background: #fff2f0;
        }
      }
    }
    
    // Timeline line styling
    .ant-timeline-item-tail {
      border-left: 2px solid #e8e8e8;
    }
    
    // Timeline dots
    .ant-timeline-item-head {
      background: #fff;
      border: 2px solid #e8e8e8;
      width: 12px;
      height: 12px;
      
      &.ant-timeline-item-head-custom {
        border: none;
        background: transparent;
        width: 20px;
        height: 20px;
        
        .anticon {
          font-size: 16px;
        }
      }
    }
  }
  
  .current-status-summary {
    margin-top: 16px;
    padding: 12px;
    background: #f9f9f9;
    border-radius: 6px;
    text-align: center;
    
    .ant-tag {
      margin-left: 8px;
      font-weight: 500;
      padding: 4px 12px;
      border-radius: 12px;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .detailed-status-timeline {
    .status-timeline {
      .ant-timeline-item-content {
        margin-left: 16px;
      }
      
      .timeline-item {
        .timeline-content {
          padding: 6px 8px;
          font-size: 14px;
        }
      }
    }
  }
}
