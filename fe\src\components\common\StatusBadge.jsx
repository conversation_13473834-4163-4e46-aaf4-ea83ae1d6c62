import { Tag } from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  StopOutlined,
  FileTextOutlined,
  ThunderboltOutlined,
  AlertOutlined,
  UserOutlined,
  HeartOutlined,
  ExperimentOutlined,
} from '@ant-design/icons';
import '../../styles/components/StatusBadge.scss';

/**
 * StatusBadge Component - Unified status display system
 * Provides consistent, modern, and professional status badges across the app
 */

// Status configuration with modern design
const STATUS_CONFIG = {
  // Donation statuses
  donation: {
    0: { // PENDING
      color: 'processing',
      icon: <ClockCircleOutlined />,
      text: 'Chờ duyệt',
      className: 'status-pending'
    },
    1: { // REJECTED
      color: 'error',
      icon: <CloseCircleOutlined />,
      text: 'Từ chối',
      className: 'status-rejected'
    },
    2: { // APPROVED
      color: 'success',
      icon: <CheckCircleOutlined />,
      text: 'Đã duyệt',
      className: 'status-approved'
    },
    3: { // CANCELLED
      color: 'default',
      icon: <StopOutlined />,
      text: 'Đã hủy',
      className: 'status-cancelled'
    },
    // String statuses for backward compatibility
    'pending': {
      color: 'processing',
      icon: <ClockCircleOutlined />,
      text: 'Chờ duyệt',
      className: 'status-pending'
    },
    'approved': {
      color: 'success',
      icon: <CheckCircleOutlined />,
      text: 'Đã duyệt',
      className: 'status-approved'
    },
    'completed': {
      color: 'success',
      icon: <CheckCircleOutlined />,
      text: 'Hoàn thành',
      className: 'status-completed'
    },
    'rejected': {
      color: 'error',
      icon: <CloseCircleOutlined />,
      text: 'Từ chối',
      className: 'status-rejected'
    },
    // DONATION_STATUS constants mapping
    'registered': {
      color: 'processing',
      icon: <UserOutlined />,
      text: 'Đã đăng ký',
      className: 'status-pending'
    },
    'health_checked': {
      color: 'green',
      icon: <CheckCircleOutlined />,
      text: 'Chấp nhận',
      className: 'status-approved'
    },
    'not_eligible_health': {
      color: 'red',
      icon: <CloseCircleOutlined />,
      text: 'Không chấp nhận',
      className: 'status-rejected'
    },
    'donation_completed': {
      color: 'green',
      icon: <CheckCircleOutlined />,
      text: 'Hoàn thành hiến máu',
      className: 'status-completed'
    }
  },

  // Blood request statuses
  request: {
    0: { // PENDING
      color: 'processing',
      icon: <ClockCircleOutlined />,
      text: 'Chờ duyệt',
      className: 'status-pending'
    },
    1: { // APPROVED
      color: 'cyan',
      icon: <CheckCircleOutlined />,
      text: 'Đã duyệt',
      className: 'status-approved'
    },
    2: { // COMPLETED - Theo bloodRequestUtils.js, status 2 là "Hoàn thành"
      color: 'cyan',
      icon: <CheckCircleOutlined />,
      text: 'Hoàn thành',
      className: 'status-completed'
    },
    3: { // REJECTED
      color: 'error',
      icon: <CloseCircleOutlined />,
      text: 'Từ chối',
      className: 'status-rejected'
    },
    4: { // FULFILLED - Hiển thị là "Hoàn thành"
      color: 'cyan',
      icon: <CheckCircleOutlined />,
      text: 'Hoàn thành',
      className: 'status-completed'
    },
    5: { // COMPLETED
      color: 'cyan',
      icon: <CheckCircleOutlined />,
      text: 'Hoàn thành',
      className: 'status-completed'
    },
    6: { // CANCELLED
      color: 'volcano',
      icon: <StopOutlined />,
      text: 'Đã hủy',
      className: 'status-cancelled'
    },
    // String statuses for backward compatibility
    'pending': {
      color: 'processing',
      icon: <ClockCircleOutlined />,
      text: 'Chờ duyệt',
      className: 'status-pending'
    },
    'approved': {
      color: 'cyan',
      icon: <CheckCircleOutlined />,
      text: 'Đã duyệt',
      className: 'status-approved'
    },
    'processing': {
      color: 'cyan',
      icon: <CheckCircleOutlined />,
      text: 'Hoàn thành',
      className: 'status-completed'
    },
    'rejected': {
      color: 'error',
      icon: <CloseCircleOutlined />,
      text: 'Từ chối',
      className: 'status-rejected'
    },
    'fulfilled': {
      color: 'success',
      icon: <CheckCircleOutlined />,
      text: 'Hoàn thành',
      className: 'status-completed'
    },
    'completed': {
      color: 'success',
      icon: <CheckCircleOutlined />,
      text: 'Hoàn thành',
      className: 'status-completed'
    },
    'cancelled': {
      color: 'volcano',
      icon: <StopOutlined />,
      text: 'Đã hủy',
      className: 'status-cancelled'
    }
  },

  // Urgency levels
  urgency: {
    'NORMAL': {
      color: 'success',
      icon: <FileTextOutlined />,
      text: 'Bình thường',
      className: 'urgency-normal'
    },
    'URGENT': {
      color: 'warning',
      icon: <ThunderboltOutlined />,
      text: 'Khẩn cấp',
      className: 'urgency-urgent'
    },
    'CRITICAL': {
      color: 'error',
      icon: <AlertOutlined />,
      text: 'Cấp cứu',
      className: 'urgency-critical'
    }
  },

  // General statuses
  general: {
    'active': {
      color: 'success',
      icon: <CheckCircleOutlined />,
      text: 'Hoạt động',
      className: 'status-active'
    },
    'inactive': {
      color: 'default',
      icon: <StopOutlined />,
      text: 'Không hoạt động',
      className: 'status-inactive'
    },
    'warning': {
      color: 'warning',
      icon: <ExclamationCircleOutlined />,
      text: 'Cảnh báo',
      className: 'status-warning'
    }
  }
};

const StatusBadge = ({
  status,
  type = 'general',
  size = 'default', // small, default, large
  showIcon = true,
  showText = true,
  customText = null,
  className = '',
  style = {},
  ...props
}) => {
  // Get status configuration
  const getStatusConfig = () => {
    const typeConfig = STATUS_CONFIG[type];
    if (!typeConfig) {
      return {
        color: 'default',
        icon: <ExclamationCircleOutlined />,
        text: 'Không xác định',
        className: 'status-unknown'
      };
    }

    const statusConfig = typeConfig[status];
    if (!statusConfig) {
      return {
        color: 'default',
        icon: <ExclamationCircleOutlined />,
        text: 'Không xác định',
        className: 'status-unknown'
      };
    }

    return statusConfig;
  };

  const config = getStatusConfig();
  const displayText = customText || config.text;

  return (
    <Tag
      className={`status-badge ${config.className} status-badge-${size} ${className}`}
      style={style}
      {...props}
    >
      <span className="status-badge-content">
        {showIcon && (
          <span className="status-badge-icon">
            {config.icon}
          </span>
        )}
        {showText && (
          <span className="status-badge-text">
            {displayText}
          </span>
        )}
      </span>
    </Tag>
  );
};

// Helper functions for easy usage
export const DonationStatusBadge = ({ status, ...props }) => (
  <StatusBadge status={status} type="donation" {...props} />
);

export const RequestStatusBadge = ({ status, ...props }) => (
  <StatusBadge status={status} type="request" {...props} />
);

export const UrgencyBadge = ({ level, ...props }) => (
  <StatusBadge status={level} type="urgency" {...props} />
);

export default StatusBadge;
