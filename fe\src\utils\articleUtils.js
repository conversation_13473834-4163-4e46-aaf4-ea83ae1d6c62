// Utility functions for Article data handling
// Handle inconsistent field names in BloodArticles API responses

// Utility để lấy ID chính xác của bài viết
export const getArticleId = (articleItem) => {
  if (!articleItem) return null;

  // Thử các trường ID khác nhau theo thứ tự ưu tiên
  return articleItem.articleId || articleItem.contentID || articleItem.id || articleItem.articleID;
};

// Utility để lấy userId chính xác của bài viết
export const getArticleUserId = (articleItem) => {
  if (!articleItem) return null;

  // Thử các trường userId khác nhau theo thứ tự ưu tiên
  return (
    articleItem.userId ||
    articleItem.userID ||
    articleItem.authorId ||
    articleItem.createdBy
  );
};

// Utility để tìm bài viết theo ID
export const findArticleById = (articlesArray, targetId) => {
  if (!Array.isArray(articlesArray) || !targetId) return null;

  return articlesArray.find((article) => {
    const articleId = getArticleId(article);
    return articleId === targetId || String(articleId) === String(targetId);
  });
};
