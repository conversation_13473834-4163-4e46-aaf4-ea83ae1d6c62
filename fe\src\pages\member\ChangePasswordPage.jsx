import React from "react";
import { useNavigate } from "react-router-dom";
import ChangePasswordForm from "../../components/auth/ChangePasswordForm";
import MemberNavbar from "../../components/member/MemberNavbar";
import Footer from "../../components/common/Footer";
import ScrollToTop from "../../components/common/ScrollToTop";
import "../../styles/pages/ChangePasswordPage.scss";

export default function ChangePasswordPage() {
  const navigate = useNavigate();

  const handleSuccess = () => {
    // Redirect back to profile or dashboard after successful password change
    navigate("/member/profile");
  };

  const handleCancel = () => {
    // Go back to previous page
    navigate(-1);
  };

  return (
    <>
      <MemberNavbar />
      <div className="change-password-page__container">
        <div className="change-password-page__content">
          <ChangePasswordForm
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </div>
      </div>
      <Footer />
      <ScrollToTop />
    </>
  );
}
