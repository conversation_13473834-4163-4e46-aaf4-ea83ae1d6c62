import React from "react";
import { useNavigate } from "react-router-dom";
import {
  FaCheckCircle,
  FaExclamationTriangle,
  FaHome,
  FaRedo,
  FaFileAlt,
} from "react-icons/fa";

/**
 * Component hiển thị kết quả đăng ký yêu cầu máu
 * Đồng bộ UI với ResultDisplay của BloodDonation
 */
const BloodRequestResultDisplay = ({
  submissionResult,
  onRetry,
}) => {
  const navigate = useNavigate();

  if (!submissionResult) return null;

  return (
    <div className="blood-request-form-page">
      <div className="registration-content">
        <div className="result-section">
          <div className={`result-card ${submissionResult.status}`}>
            <div className="result-icon">
              {submissionResult.status === "error" ? (
                <FaExclamationTriangle />
              ) : (
                <FaCheckCircle />
              )}
            </div>
            <div className="result-content">
              <h2>{submissionResult.message}</h2>
              <p>{submissionResult.description}</p>

              {submissionResult.status === "success" && submissionResult.data && (
                <div className="request-summary">
                  <h3 className="summary-title">
                    <FaFileAlt className="me-2" />
                    Thông tin yêu cầu
                  </h3>
                  <div className="request-details">
                    <div className="detail-item">
                      <strong>Mã yêu cầu:</strong>
                      <span className="value request-id">
                        #{submissionResult.requestId}
                      </span>
                    </div>
                    <div className="detail-item">
                      <strong>Nhóm máu:</strong>
                      <span className="value blood-type">
                        {submissionResult.data.bloodType}
                      </span>
                    </div>
                    <div className="detail-item">
                      <strong>Số lượng:</strong>
                      <span className="value">
                        {submissionResult.data.quantity} {submissionResult.data.unit}
                      </span>
                    </div>
                    <div className="detail-item">
                      <strong>Bệnh nhân:</strong>
                      <span className="value">
                        {submissionResult.data.patientName}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              <div className="result-actions">
                <button
                  className="btn btn-primary"
                  onClick={() => navigate("/member")}
                >
                  <FaHome className="me-2" />
                  Về trang chủ
                </button>

                {submissionResult.status === "error" && (
                  <button
                    className="btn btn-outline-primary retry-button"
                    onClick={onRetry}
                  >
                    <FaRedo className="me-2" />
                    Thử lại
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BloodRequestResultDisplay;
