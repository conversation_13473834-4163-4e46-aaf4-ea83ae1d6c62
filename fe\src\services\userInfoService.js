import { apiClient } from "./axiosInstance";
import vietnamAddressService from "./vietnamAddressService";
import config from "../config/environment";


class UserInfoService {
  constructor() {
   
    this.endpoints = {
      information: config.api.information || "/Information",
      users: config.api.information || "/Information", // Use same endpoint for consistency
    };
  }

  // Get all users (restricted to admin/manager roles only)
  async getAllUsers() {
    try {
      // Check if current user has permission to access all users
      const currentUser = JSON.parse(localStorage.getItem("currentUser") || "{}");
      const userRole = currentUser.role;
      
      // Only allow admin (4) and manager (3) roles to access all users
      if (userRole !== "4" && userRole !== "3" && userRole !== 4 && userRole !== 3) {
        throw new Error("Insufficient permissions to access all users data");
      }
      
      const response = await apiClient.get(this.endpoints.users);
      return response.data;
    } catch (error) {
      console.error("Error fetching all users:", error);
      throw error;
    }
  }

  
  async getAllUsersForce() {
    try {
      const url = `${this.endpoints.users}?_=${Date.now()}`;
      const response = await apiClient.get(url);
      return response.data;
    } catch (error) {
      console.error("Error force fetching all users:", error);
      throw error;
    }
  }

  async getUserInfo(userId) {
    try {
      const response = await apiClient.get(
        `${this.endpoints.information}/${userId}`
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching user info:", error);
      throw error;
    }
  }

  // Get user name only (for displaying author names without exposing full user data)
  async getUserName(userId) {
    try {
      const response = await apiClient.get(
        `${this.endpoints.information}/${userId}/name`
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching user name:", error);
      // Fallback to basic user info if name endpoint doesn't exist
      try {
        const userInfo = await this.getUserInfo(userId);
        return userInfo.name || userInfo.fullName || `User ${userId}`;
      } catch (fallbackError) {
        return `User ${userId}`;
      }
    }
  }

  // Get multiple user names in batch (more efficient than multiple single calls)
  async getUserNames(userIds) {
    try {
      const response = await apiClient.post(
        `${this.endpoints.information}/names`,
        { userIds }
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching user names:", error);
      // Fallback to individual calls
      const userNames = {};
      for (const userId of userIds) {
        try {
          userNames[userId] = await this.getUserName(userId);
        } catch (err) {
          userNames[userId] = `User ${userId}`;
        }
      }
      return userNames;
    }
  }

  
  async createUser(userData) {
    try {
      const response = await apiClient.post(this.endpoints.users, userData);
      return response.data;
    } catch (error) {
      console.error("Error creating user:", error);
      throw error;
    }
  }

  async updateUserInfo(userId, userData) {
    try {
      console.log("Sending data to API:", userData);
      console.log("API endpoint:", `${this.endpoints.information}/${userId}`);

      const response = await apiClient.put(
        `${this.endpoints.information}/${userId}`,
        userData
      );
      return response.data;
    } catch (error) {
      console.error("Error updating user info:", error);
      if (error.response) {
        console.error("Response status:", error.response.status);
        console.error("Response data:", error.response.data);
      }
      throw error;
    }
  }

  
  async deleteUser(userId) {
    try {
      const response = await apiClient.delete(
        `${this.endpoints.users}/${userId}`
      );
      return response.data;
    } catch (error) {
      console.error("Error deleting user:", error);
      throw error;
    }
  }

  
  async updateUser(userData) {
    try {
      if (!userData.userID) {
        throw new Error("userID is required for user update");
      }

      const response = await apiClient.put(
        `${this.endpoints.information}/${userData.userID}`,
        userData
      );
      return response.data;
    } catch (error) {
      console.error("Error updating user:", error);
      throw error;
    }
  }

  
  async analyzeUserStatuses() {
    try {
      const users = await this.getAllUsers();
      const analysis = {
        total: users.length,
        active: users.filter((u) => u.status === 1).length,
        suspended: users.filter((u) => u.status === 0).length,
        other: users.filter((u) => u.status !== 0 && u.status !== 1).length,
        statusDistribution: {},
      };

      // Count status distribution
      users.forEach((user) => {
        const status = user.status;
        analysis.statusDistribution[status] =
          (analysis.statusDistribution[status] || 0) + 1;
      });

      return analysis;
    } catch (error) {
      console.error("Error analyzing user statuses:", error);
      throw error;
    }
  }

  
  async updateUserDistance(userId, distance) {
    try {
      const response = await apiClient.patch(
        `${this.endpoints.information}/${userId}/distance`,
        { distance }
      );
      console.log(
        `Distance updated successfully for user ${userId}:`,
        distance
      );
      return response.data;
    } catch (error) {
      console.error("Error updating user distance:", error);
      throw error;
    }
  }

  
  async getProvinces() {
    return vietnamAddressService.getProvinces();
  }

  
  async getWardsByProvince(provinceName) {
    return vietnamAddressService.getWardsByProvince(provinceName);
  }

  
  async preloadAddressData() {
    return vietnamAddressService.preloadAllData();
  }

  
  clearAddressCache() {
    vietnamAddressService.clearCache();
  }

  formatDate(dateString) {
    return dateString ? dateString.split("T")[0] : "";
  }

  
  extractHouseNumberAndStreet(fullAddress, wardName, districtName, provinceName) {
    if (!fullAddress) return "";

    // Split full address by comma
    const parts = fullAddress.split(",").map((part) => part.trim());

    // Remove ward, district and province from address
    const filteredParts = parts.filter((part) => {
      return part !== wardName && part !== districtName && part !== provinceName;
    });

    // Join house number and street
    return filteredParts.join(", ");
  }

  
  calculateAge(dateOfBirth) {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  }

  async saveDistanceForCurrentUser(distance) {
    try {
      // Get current user from localStorage
      const currentUser = JSON.parse(
        localStorage.getItem("currentUser") || "{}"
      );

      if (!currentUser.id) {
        throw new Error("Không tìm thấy thông tin người dùng hiện tại");
      }

      // Update distance in database
      const result = await this.updateUserDistance(currentUser.id, distance);

      // Update current user in localStorage with new distance
      const updatedUser = {
        ...currentUser,
        distance: distance,
      };
      localStorage.setItem("currentUser", JSON.stringify(updatedUser));

      return {
        success: true,
        data: result,
        message: "Đã lưu khoảng cách thành công",
      };
    } catch (error) {
      console.error("Error saving distance for current user:", error);
      return {
        success: false,
        error: error.message || "Có lỗi xảy ra khi lưu khoảng cách",
        details: error,
      };
    }
  }

  
  buildFullAddress(houseNumber, wardName, districtName, provinceName) {
    // Kiểm tra xem houseNumber đã chứa thông tin địa chỉ đầy đủ chưa
    const isFullAddressInBase = houseNumber && (
      (wardName && houseNumber.includes(wardName)) ||
      (districtName && houseNumber.includes(districtName)) ||
      (provinceName && houseNumber.includes(provinceName))
    );

    if (isFullAddressInBase) {
      // Nếu houseNumber đã chứa thông tin đầy đủ, chỉ sử dụng nó
      return houseNumber;
    } else {
      // Nếu chưa, kết hợp các thành phần
      return [houseNumber, wardName, districtName, provinceName]
        .filter(Boolean)
        .join(", ");
    }
  }

  
  prepareUserDataForSubmission(formData, currentUser) {
    // Create full address from components
    const fullAddress = this.buildFullAddress(
      formData.address,
      formData.wardName,
      "", // No district name in form
      formData.provinceName
    );

    return {
      userID: parseInt(currentUser.id),
      email: formData.email || "",
      password: currentUser.password || "",
      phone: formData.phone || "",
      idCardType: formData.documentType || "",
      idCard: formData.documentNumber || "",
      name: formData.fullName || "",
      dateOfBirth: formData.dob ? new Date(formData.dob).toISOString() : null,
      age: formData.dob ? this.calculateAge(formData.dob) : null,
      gender: formData.gender || "",
      city: formData.provinceName || "",
      district: formData.provinceName || "", // Use province as district (database requirement)
      ward: formData.wardName || "",
      address: fullAddress || "",
      distance: null,
      bloodGroup: formData.bloodType || "",
      rhType: formData.rhType || "",
      weight: null,
      height: null,
      status: 1,
      roleID: currentUser.roleID || 1,
      department: currentUser.department || "",
      createdAt: new Date().toISOString(),
    };
  }

  validateUserData(userData) {
    const errors = {};

    // Required fields validation
    if (!userData.name?.trim()) {
      errors.name = "Họ và tên là bắt buộc";
    }

    if (!userData.email?.trim() && !userData.phone?.trim()) {
      errors.contact = "Cần có ít nhất email hoặc số điện thoại";
    }

    // Email validation
    if (userData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userData.email)) {
      errors.email = "Email không hợp lệ";
    }

    // Phone validation
    if (userData.phone && !/^0\d{9}$/.test(userData.phone)) {
      errors.phone = "Số điện thoại không hợp lệ (10 số, bắt đầu bằng 0)";
    }

    // ID card validation
    if (userData.idCard) {
      if (userData.idCardType === "cccd" && !/^\d{12}$/.test(userData.idCard)) {
        errors.idCard = "CCCD phải gồm đúng 12 số";
      } else if (
        userData.idCardType === "passport" &&
        !/^[A-Z]\d{7}$/.test(userData.idCard)
      ) {
        errors.idCard = "Hộ chiếu phải gồm 1 chữ cái in hoa và 7 số";
      }
    }

    // Date of birth validation
    if (userData.dateOfBirth) {
      const birthDate = new Date(userData.dateOfBirth);
      const today = new Date();
      if (birthDate > today) {
        errors.dateOfBirth = "Ngày sinh không thể trong tương lai";
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  }

 
  async searchUsers(criteria = {}) {
    try {
      const allUsers = await this.getAllUsers();

      if (!criteria || Object.keys(criteria).length === 0) {
        return allUsers;
      }

      return allUsers.filter((user) => {
        return Object.entries(criteria).every(([key, value]) => {
          if (!value) return true; // Skip empty criteria

          const userValue = user[key];
          if (typeof userValue === "string") {
            return userValue.toLowerCase().includes(value.toLowerCase());
          }
          return userValue === value;
        });
      });
    } catch (error) {
      console.error("Error searching users:", error);
      throw error;
    }
  }

  async getUserStatistics() {
    try {
      const users = await this.getAllUsers();

      const stats = {
        total: users.length,
        active: users.filter((u) => u.status === 1).length,
        inactive: users.filter((u) => u.status === 0).length,
        byGender: {
          male: users.filter((u) => u.gender === "male").length,
          female: users.filter((u) => u.gender === "female").length,
          other: users.filter((u) => u.gender === "other").length,
        },
        byBloodType: {},
        byProvince: {},
      };

      // Count by blood type
      users.forEach((user) => {
        if (user.bloodGroup) {
          stats.byBloodType[user.bloodGroup] =
            (stats.byBloodType[user.bloodGroup] || 0) + 1;
        }
      });

      // Count by province
      users.forEach((user) => {
        if (user.city) {
          stats.byProvince[user.city] = (stats.byProvince[user.city] || 0) + 1;
        }
      });

      return stats;
    } catch (error) {
      console.error("Error getting user statistics:", error);
      throw error;
    }
  }

  checkProfileCompleteness(userInfo) {
    if (!userInfo) {
      return {
        isComplete: false,
        missingFields: ["Không tìm thấy thông tin người dùng"],
        message: "Không thể kiểm tra thông tin hồ sơ",
      };
    }

    const missingFields = [];
    const requiredFields = [
      { field: "phone", label: "Số điện thoại" },
      { field: "dateOfBirth", label: "Ngày sinh" },
      { field: "gender", label: "Giới tính" },
      { field: "address", label: "Địa chỉ" },
      { field: "city", label: "Tỉnh/Thành phố" },
      
    ];

    // Check required fields
    requiredFields.forEach(({ field, label }) => {
      if (!userInfo[field] || userInfo[field].toString().trim() === "") {
        missingFields.push(label);
      }
    });

    // Check specific validations
    if (userInfo.phone && !/^0\d{9}$/.test(userInfo.phone)) {
      missingFields.push("Số điện thoại không hợp lệ");
    }

    if (userInfo.dateOfBirth) {
      const age = this.calculateAge(userInfo.dateOfBirth);
      if (age < 18 || age > 65) {
        missingFields.push("Tuổi phải từ 18-65 để hiến máu");
      }
    }

    const isComplete = missingFields.length === 0;

    return {
      isComplete,
      missingFields,
      message: isComplete
        ? "Hồ sơ đã đầy đủ thông tin"
        : `Còn thiếu ${missingFields.length} thông tin bắt buộc`,
    };
  }
}

// Export singleton instance
export default new UserInfoService();
