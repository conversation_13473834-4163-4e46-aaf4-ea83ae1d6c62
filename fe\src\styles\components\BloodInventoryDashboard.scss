.blood-inventory-dashboard {
  .dashboard-loading {
    text-align: center;
    padding: 60px 20px;
    
    p {
      margin-top: 16px;
      color: #666;
      font-size: 16px;
    }
  }

  .retry-button {
    background: #1890ff;
    color: white;
    border: none;
    padding: 6px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    
    &:hover {
      background: #40a9ff;
    }
  }

  .overview-section {
    margin-bottom: 24px;

    .overview-card {
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }

      .ant-card-body {
        padding: 20px;
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          font-weight: 600;
          margin-bottom: 8px;
          color: #666;
        }

        .ant-statistic-content {
          .ant-statistic-content-prefix {
            font-size: 20px;
            margin-right: 8px;
          }

          .ant-statistic-content-value {
            font-size: 24px;
            font-weight: 700;
          }

          .ant-statistic-content-suffix {
            font-size: 14px;
            color: #666;
            margin-left: 4px;
          }
        }
      }

      &.total-bags {
        border-left: 4px solid #1890ff;
        
        .ant-statistic-content-prefix {
          color: #1890ff;
        }
      }

      &.total-volume {
        border-left: 4px solid #52c41a;
        
        .ant-statistic-content-prefix {
          color: #52c41a;
        }
      }

      &.rare-blood {
        border-left: 4px solid #faad14;
        
        .ant-statistic-content-prefix {
          color: #faad14;
        }
      }
    }
  }

  .chart-section {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      flex-wrap: wrap;
      gap: 16px;

      h3 {
        margin: 0;
        color: #262626;
        font-size: 18px;
        font-weight: 600;
      }

      .chart-filters {
        display: flex;
        align-items: center;
        gap: 12px;

        .filter-label {
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }

        .ant-radio-group {
          .ant-radio-button-wrapper {
            border-radius: 6px;
            font-size: 13px;
            
            &:first-child {
              border-radius: 6px 0 0 6px;
            }
            
            &:last-child {
              border-radius: 0 6px 6px 0;
            }

            &.ant-radio-button-wrapper-checked {
              background: #1890ff;
              border-color: #1890ff;
              color: white;
              
              &:hover {
                background: #40a9ff;
                border-color: #40a9ff;
              }
            }
          }
        }
      }
    }

    .blood-inventory-chart {
      .ant-card {
        box-shadow: none;
        border: none;
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .overview-section {
      .overview-card {
        .ant-card-body {
          padding: 16px;
        }

        .ant-statistic {
          .ant-statistic-title {
            font-size: 13px;
          }

          .ant-statistic-content {
            .ant-statistic-content-prefix {
              font-size: 18px;
            }

            .ant-statistic-content-value {
              font-size: 20px;
            }

            .ant-statistic-content-suffix {
              font-size: 13px;
            }
          }
        }
      }
    }

    .chart-section {
      .chart-header {
        flex-direction: column;
        align-items: flex-start;

        h3 {
          font-size: 16px;
        }

        .chart-filters {
          width: 100%;
          justify-content: space-between;

          .ant-radio-group {
            .ant-radio-button-wrapper {
              font-size: 12px;
              padding: 4px 8px;
            }
          }
        }
      }
    }
  }

  @media (max-width: 576px) {
    .overview-section {
      .overview-card {
        .ant-card-body {
          padding: 12px;
        }

        .ant-statistic {
          .ant-statistic-title {
            font-size: 12px;
          }

          .ant-statistic-content {
            .ant-statistic-content-prefix {
              font-size: 16px;
            }

            .ant-statistic-content-value {
              font-size: 18px;
            }

            .ant-statistic-content-suffix {
              font-size: 12px;
            }
          }
        }
      }
    }

    .chart-section {
      .chart-header {
        .chart-filters {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }
      }
    }
  }
}
