import React, { useState, useEffect } from "react";
import { Card, Row, Col, Spin, Alert } from "antd";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from "recharts";
import { fetchBloodInventory } from "../../services/bloodInventoryService";
import { getInventoryStatus } from "../../constants/bloodInventoryConstants";
import "../../styles/components/BloodInventoryAlertChart.scss";

// Component mapping
const BLOOD_COMPONENT_MAP = {
  1: "<PERSON>ồng cầu",
  2: "Ti<PERSON>u cầu",
  3: "<PERSON><PERSON><PERSON><PERSON> tương",
  4: "Máu toàn phần",
};

/**
 * Component biểu đồ cảnh báo khẩn cấp cho kho máu
 * Hiển thị gộp cả nhóm máu và thành phần máu trong 1 chart
 */
const BloodInventoryAlertChart = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchAlertData();
  }, []);

  const fetchAlertData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetchBloodInventory();

      // Filter only critical and low status items
      const alertItems = response.filter(item => {
        const status = getInventoryStatus(item.quantity);
        return status === "critical" || status === "low";
      });

      // Transform data to combined chart format
      const transformedData = transformToCombinedChart(alertItems);
      setData(transformedData);
    } catch (err) {
      console.error("Error fetching alert data:", err);
      setError("Không thể tải dữ liệu cảnh báo");
    } finally {
      setLoading(false);
    }
  };

  // Transform data to combined format (both blood type and component)
  const transformToCombinedChart = (alertItems) => {
    const combinedData = {};

    alertItems.forEach(item => {
      const status = getInventoryStatus(item.quantity);
      const bloodType = `${item.bloodGroup}${item.rhType}`;
      const componentName = BLOOD_COMPONENT_MAP[item.componentId] || `Component ${item.componentId}`;

      // Create combined key: BloodType - Component
      const key = `${bloodType} - ${componentName}`;

      if (!combinedData[key]) {
        combinedData[key] = {
          name: key,
          bloodType: bloodType,
          component: componentName,
          critical: 0,
          low: 0,
          total: 0
        };
      }

      if (status === "critical") {
        combinedData[key].critical += item.quantity;
      } else if (status === "low") {
        combinedData[key].low += item.quantity;
      }
      combinedData[key].total += item.quantity;
    });

    return Object.values(combinedData)
      .filter(item => item.total > 0)
      .sort((a, b) => (b.critical + b.low) - (a.critical + a.low));
  };

  // Colors for different alert levels
  const ALERT_COLORS = {
    critical: "#D91022",
    low: "#fa8c16"
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="alert-tooltip">
          <p className="tooltip-label">{label}</p>
          {payload.map((entry, index) => (
            <p key={index} className="tooltip-value" style={{ color: entry.color }}>
              {entry.dataKey === "critical" ? "Cảnh báo khẩn cấp" : "Thiếu máu"}: {entry.value} túi
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <Card className="blood-inventory-alert-chart">
        <div className="loading-container">
          <Spin size="large" />
          <p>Đang tải dữ liệu cảnh báo...</p>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="blood-inventory-alert-chart">
        <Alert
          message="Lỗi tải dữ liệu"
          description={error}
          type="error"
          showIcon
          action={
            <button onClick={fetchAlertData} className="retry-button">
              Thử lại
            </button>
          }
        />
      </Card>
    );
  }

  return (
    <Card className="blood-inventory-alert-chart">
      <div className="chart-header">
        <h3>Cảnh báo tình trạng kho máu</h3>
        
      </div>

      <div className="chart-content">
        <ResponsiveContainer width="100%" height={450}>
          <BarChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 80 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="name"
              angle={-45}
              textAnchor="end"
              height={80}
              fontSize={11}
            />
            <YAxis
              label={{ value: 'Số lượng túi', angle: -90, position: 'insideLeft' }}
              fontSize={12}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend/>
            <Bar dataKey="critical" name="Cảnh báo khẩn cấp" fill={ALERT_COLORS.critical} />
            <Bar dataKey="low" name="Thiếu máu" fill={ALERT_COLORS.low} />
          </BarChart>
        </ResponsiveContainer>
      </div>

      
    </Card>
  );
};

export default BloodInventoryAlertChart;
