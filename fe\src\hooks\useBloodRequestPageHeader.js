import { useMemo } from "react";

/**
 * Custom hook for Blood Request Page Header logic
 * Manages dynamic content and actions based on role and tab state
 */
export const useBloodRequestPageHeader = ({
  role = "doctor",
  activeTab = "internal",
  internalRequests = [],
  externalRequests = [],
  isBloodDepartment = false,
  currentUser = null,
  // Loading states
  loading = false,
  createLoading = false,
  exportLoading = false,
}) => {
  // Get current requests based on tab
  const currentRequests = useMemo(() => {
    return activeTab === "external" ? externalRequests : internalRequests;
  }, [activeTab, internalRequests, externalRequests]);

  // Get dynamic page title
  const getPageTitle = useMemo(() => {
    if (role === "manager") {
      if (isBloodDepartment) {
        return activeTab === "external"
          ? "Yêu cầu máu từ bên ngoài"
          : "Yêu cầu máu nội bộ";
      }
      return "Quản lý Yêu cầu <PERSON>";
    }

    if (role === "doctor") {
      if (isBloodDepartment) {
        return activeTab === "external"
          ? "Yêu cầu máu từ bên ngoài"
          : "Yêu cầu máu nội bộ";
      }
      return "Yêu cầu Máu";
    }

    return "Quản lý Yêu cầu Máu";
  }, [role, activeTab, isBloodDepartment]);

  // Get dynamic page description
  const getPageDescription = useMemo(() => {
    if (role === "manager") {
      if (isBloodDepartment) {
        return activeTab === "external"
          ? "Xử lý các yêu cầu máu từ thành viên bên ngoài"
          : "Xử lý các yêu cầu máu từ bác sĩ khoa khác trong bệnh viện";
      }
      return "Xử lý và theo dõi tất cả yêu cầu máu từ bác sĩ và bệnh nhân";
    }

    if (role === "doctor") {
      if (isBloodDepartment) {
        return activeTab === "external"
          ? "Xử lý các yêu cầu máu từ thành viên bên ngoài"
          : "Xử lý các yêu cầu máu từ bác sĩ khoa khác trong bệnh viện";
      }
      return "Quản lý và theo dõi các yêu cầu máu";
    }

    return "Xử lý và theo dõi tất cả yêu cầu máu";
  }, [role, activeTab, isBloodDepartment]);

  // Get tab configuration for blood department
  const getTabItems = useMemo(() => {
    if (!isBloodDepartment) return [];

    const internalCount = internalRequests.length;
    const externalCount = externalRequests.length;

    return [
      {
        key: "internal",
        label: `Yêu cầu máu nội bộ (${internalCount})`,
      },
      {
        key: "external",
        label: `Yêu cầu máu từ bên ngoài (${externalCount})`,
      },
    ];
  }, [isBloodDepartment, internalRequests.length, externalRequests.length]);

  // Get statistics for current tab
  const getTabStatistics = useMemo(() => {
    const requests = currentRequests;

    const pending = requests.filter(
      (req) => req.status === "PENDING" || req.status === "pending"
    ).length;

    const approved = requests.filter(
      (req) => req.status === "APPROVED" || req.status === "approved"
    ).length;

    const completed = requests.filter(
      (req) => req.status === "COMPLETED" || req.status === "completed"
    ).length;

    const rejected = requests.filter(
      (req) => req.status === "REJECTED" || req.status === "rejected"
    ).length;

    return {
      total: requests.length,
      pending,
      approved,
      completed,
      rejected,
    };
  }, [currentRequests]);

  return {
    // Computed values
    currentRequests,
    getPageTitle,
    getPageDescription,
    getTabItems,
    getTabStatistics,

    // Helper functions
    isCurrentTabEmpty: currentRequests.length === 0,
    shouldShowTabs: isBloodDepartment,

    // Role checks
    canCreateRequest: role === "doctor" && !isBloodDepartment,
    canExport: role === "manager",
    canManageAll: role === "manager",
  };
};
