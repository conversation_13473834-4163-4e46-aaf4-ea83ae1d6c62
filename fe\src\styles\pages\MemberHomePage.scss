@use '../base/variables' as vars;
@use '../base/mixin' as mix;

// Custom styles for member hero section
.member-hero {
  position: relative;

  .hero-content {
    .member-info {
      margin-top: 2rem;
      display: flex;
      gap: 2rem;
      flex-wrap: wrap;

      .info-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.1);
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);

        .label {
          font-weight: 600;
          color: rgba(255, 255, 255, 0.9);
          font-size: 0.9rem;
        }

        .blood-type-badge {
          background: vars.$secondary-color;
          color: white;
          padding: 0.25rem 0.75rem;
          border-radius: 15px;
          font-weight: 700;
          font-size: 0.9rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        span:not(.label):not(.blood-type-badge) {
          color: white;
          font-weight: 500;
        }
      }
    }
  }
}

// Responsive design for member info
@media (max-width: 768px) {
  .member-hero {
    .hero-content {
      .member-info {
        margin-top: 1.5rem;
        gap: 1rem;

        .info-item {
          padding: 0.5rem 1rem;
          font-size: 0.85rem;

          .label {
            font-size: 0.8rem;
          }

          .blood-type-badge {
            font-size: 0.8rem;
            padding: 0.2rem 0.6rem;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .member-hero {
    .hero-content {
      .member-info {
        flex-direction: column;
        gap: 0.75rem;

        .info-item {
          justify-content: space-between;
          width: 100%;
        }
      }
    }
  }
}

// Style cho avatar và dropdown menu cá nhân
.member-avatar-wrapper {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: vars.$secondary-color;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.1rem;
  cursor: pointer;
  transition: box-shadow 0.2s;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.member-dropdown-menu {
  position: absolute;
  top: 60px;
  right: 40px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
  z-index: 2000;
  min-width: 180px;
  padding: 10px 0;
  display: flex;
  flex-direction: column;
  a {
    padding: 12px 24px;
    color: vars.$text-color;
    text-decoration: none;
    font-weight: 500;
    transition: background 0.2s;
    &:hover {
      background: vars.$secondary-color;
      color: #fff;
    }
  }
}
