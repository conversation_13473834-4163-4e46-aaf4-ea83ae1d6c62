import axios from "axios";
import config from "../config/environment";

// Get base URL from config (which uses .env.local)
const baseURL = config.api.baseUrl;

const axiosInstance = axios.create({
  baseURL: baseURL,
  timeout: 30000, // 30 seconds timeout
  headers: {
    Accept: "application/json",
  },
});

axiosInstance.interceptors.request.use(
  (config) => {
    const token =
      localStorage.getItem("authToken") || sessionStorage.getItem("authToken");

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Don't set Content-Type for FormData, let axios handle it
    if (!(config.data instanceof FormData)) {
      config.headers["Content-Type"] = "application/json";
    }

    // Only log in development or for debugging
    if (import.meta.env.DEV && import.meta.env.VITE_DEBUG_API === "true") {
      console.log(
        `[API Request] ${config.method?.toUpperCase()} ${config.url}`,
        {
          headers: config.headers,
          data: config.data,
        }
      );

    }

    return config;
  },
  (error) => {
    console.error("[API Request Error]", error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
axiosInstance.interceptors.response.use(
  (response) => {
    // Only log in development or for debugging
    if (import.meta.env.DEV && import.meta.env.VITE_DEBUG_API === "true") {
      console.log(
        `[API Response] ${response.status} ${response.config.url}`,
        response.data
      );
    }
    return response;
  },
  (error) => {
    console.error("[API Response Error]", {
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      data: error.response?.data,
    });

    // Handle specific error cases
    if (error.response?.status === 401) {
      // Unauthorized - clear auth data and redirect to login
      localStorage.removeItem("authToken");
      localStorage.removeItem("currentUser");
      sessionStorage.removeItem("authToken");

      // Only redirect if not already on login page
      if (window.location.pathname !== "/login") {
        window.location.href = "/login";
      }
    } else if (error.response?.status === 403) {
      // Forbidden - redirect to 403 page
      window.location.href = "/403";
    } else if (error.response?.status === 404) {
      // Not found - could redirect to 404 page or handle gracefully
      console.warn("Resource not found:", error.config?.url);
    } else if (error.response?.status >= 500) {
      // Server error
      console.error(
        "Server error:",
        error.response?.data?.message || "Internal server error"
      );
    }

    return Promise.reject(error);
  }
);

// Helper methods for common HTTP operations
export const apiClient = {
  // GET request
  get: (url, config = {}) => axiosInstance.get(url, config),

  // POST request
  post: (url, data = {}, config = {}) => axiosInstance.post(url, data, config),

  // PUT request
  put: (url, data = {}, config = {}) => axiosInstance.put(url, data, config),

  // PATCH request
  patch: (url, data = {}, config = {}) =>
    axiosInstance.patch(url, data, config),

  // DELETE request
  delete: (url, config = {}) => axiosInstance.delete(url, config),

  // Upload file
  upload: (url, formData, config = {}) => {
    return axiosInstance.post(url, formData, {
      ...config,
      headers: {
        ...config.headers,
        "Content-Type": "multipart/form-data",
      },
    });
  },

  // Download file
  download: (url, config = {}) => {
    return axiosInstance.get(url, {
      ...config,
      responseType: "blob",
    });
  },
};

// Export both the instance and the helper
export default axiosInstance;
