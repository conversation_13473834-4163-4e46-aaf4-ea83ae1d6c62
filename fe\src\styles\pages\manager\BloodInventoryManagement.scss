@use "sass:color";
@use "../../base/variables" as vars;

.blood-inventory-management {
  display: flex;
  min-height: 100vh;
  background: vars.$manager-bg;
  font-family: vars.$font-manager;

  .blood-inventory-content {
    flex: 1;
    margin-left: 280px;
    padding: vars.$spacing-lg;
    transition: margin-left 0.3s ease;

    // PageHeader styles are handled by PageHeader.scss
    // Stats, Filters, and Table styles are handled by Ant Design components
  }

  // Responsive design
  @media (max-width: 768px) {
    .blood-inventory-content {
      margin-left: 0;
      padding: vars.$spacing-md;
    }
  }

  @media (max-width: 576px) {
    .blood-inventory-content {
      padding: vars.$spacing-sm;
    }
  }
}

// Modal styles (reuse from previous components)
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .modal-content {
    background: white;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);

    .modal-header {
      padding: 1.5rem;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        margin: 0;
        color: vars.$primary-color;
      }

      .close-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #666;

        &:hover {
          color: #333;
        }
      }
    }

    .modal-body {
      padding: 1.5rem;

      .form-group {
        margin-bottom: 1rem;

        label {
          display: block;
          margin-bottom: 0.5rem;
          font-weight: 600;
          color: #333;

          &.checkbox-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;

            input[type="checkbox"] {
              width: auto;
            }
          }
        }

        select,
        input {
          width: 100%;
          padding: 0.5rem;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 0.9rem;

          &:focus {
            outline: none;
            border-color: vars.$primary-color;
            box-shadow: 0 0 0 3px rgba(vars.$primary-color, 0.1);
          }

          &:disabled {
            background: #f8f9fa;
            color: #666;
          }
        }
      }

      .modal-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;

        .btn {
          padding: 0.5rem 1rem;
          border: none;
          border-radius: 6px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;

          &.btn-secondary {
            background: #6c757d;
            color: white;

            &:hover {
              background: #5a6268;
            }
          }

          &.btn-primary {
            background: vars.$primary-color;
            color: white;

            &:hover {
              background: color.adjust(vars.$primary-color, $lightness: -10%);
            }

            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
            }
          }
        }
      }
    }
  }
}

// Responsive
@media (max-width: 768px) {
  .blood-inventory-management {
    .blood-inventory-content {
      margin-left: 0;
      padding: 1rem;

      .page-header {
        flex-direction: column;
        align-items: stretch;
      }

      .quick-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;

        .stat-card {
          padding: 1rem;

          .stat-icon {
            font-size: 2rem;
            width: 50px;
            height: 50px;
          }

          .stat-info .stat-number {
            font-size: 1.5rem;
          }
        }
      }

      .filters-section {
        flex-direction: column;
        gap: 1rem;

        .filter-group select {
          min-width: 100%;
        }
      }

      .inventory-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .inventory-table-container {
        overflow-x: auto;

        .inventory-table {
          min-width: 700px;
        }
      }
    }
  }
}

// Blood type badge styles
.blood-type-badge {
  display: inline-block;
  padding: 6px 25px;
  border-radius: 20px;
  font-weight: 700;
  font-size: 20px;
  text-align: center;
  min-height: 40px;
  min-width: 50px;

  &.positive {
    background-color: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
  }

  &.negative {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
  }
}

// Rare badge styles
.rare-badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  background-color: #ffebee;
  color: #d93e4c;
  border: 1px solid #ffcdd2;
  white-space: nowrap;
}

// Status badge styles
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  border: 1px solid;
  white-space: nowrap;
}
