import React from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Layout, Menu, Button, Avatar, Typography, Badge, Dropdown } from "antd";
import {
  DashboardOutlined,
  FileTextOutlined,
  UserOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  TeamOutlined,
  ProfileOutlined,
  LockOutlined,
} from "@ant-design/icons";
import authService from "../../services/authService";
import { getUserName } from "../../utils/userUtils";
import { DOCTOR_TYPES } from "../../constants/systemConstants";
import { useBloodRequestCounts } from "../../hooks/useBloodRequestCounts";
import { getDepartmentNameById } from "../../utils/departmentUtils";
import { getDepartmentIcon } from "../../constants/iconConstants";
import "../../styles/base/manager-design-system.scss";
import "../../styles/components/SidebarBadge.scss";
import logo from "../../assets/images/logo.png";

const { Sider } = Layout;
const { Text } = Typography;

const DoctorSidebar = ({ collapsed, onCollapse }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const currentUser = authService.getCurrentUser();
  const isBloodDepartment = currentUser?.departmentID === 1;

  // Get blood request counts for sidebar badge
  const { total: bloodRequestCount, loading: countsLoading } =
    useBloodRequestCounts();

  // Get department name using utility function
  const departmentName =
    getDepartmentNameById(currentUser?.departmentID) ||
    currentUser?.department ||
    currentUser?.profile?.department ||
    (isBloodDepartment ? "Khoa Huyết học" : "Khoa khác");

  const navItems = [
    {
      key: "/doctor",
      label: "Dashboard",
      icon: <DashboardOutlined />,
      path: "/doctor",
      exact: true,
    },
    {
      key: "/doctor/blood-requests",
      label: "Yêu cầu máu",
      icon: <FileTextOutlined />,
      path: "/doctor/blood-requests",
      badge: bloodRequestCount > 0 ? bloodRequestCount : null,
    },
    {
      key: "/doctor/blood-inventory",
      label: "Xem kho máu",
      icon: <TeamOutlined />,
      path: "/doctor/blood-inventory",
    },

  ];

  if (isBloodDepartment) {
    navItems.splice(2, 0, {
      key: "/doctor/donor-management",
      label: "Quản lý người hiến máu",
      icon: <UserOutlined />,
      path: "/doctor/donor-management",
    });
    navItems.push({
      key: "/doctor/blog",
      label: "Quản lý Blog",
      icon: <FileTextOutlined />,
      path: "/doctor/blog",
    });
  }

  const getSelectedKey = () => {
    const currentPath = location.pathname;
    const exactMatch = navItems.find(
      (item) => item.exact && item.path === currentPath
    );
    if (exactMatch) return [exactMatch.key];
    const pathMatch = navItems.find(
      (item) => !item.exact && currentPath.startsWith(item.path)
    );
    return pathMatch ? [pathMatch.key] : [];
  };

  const menuItems = navItems.map((item) => ({
    key: item.key,
    icon: item.icon,
    label: (
      <Link to={item.path} style={{ color: "inherit", textDecoration: "none" }}>
        <div className="sidebar-menu-item">
          <span className="menu-label">{item.label}</span>
          {item.badge && (
            <Badge
              count={item.badge}
              size="small"
              className="sidebar-badge doctor-badge menu-badge"
            />
          )}
        </div>
      </Link>
    ),
  }));

  const doctorName = getUserName();

  const handleLogout = async () => {
    try {
      await authService.logout();
      navigate("/login");
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  // Dropdown menu items for user avatar
  const userMenuItems = [
    {
      key: 'profile',
      icon: <ProfileOutlined />,
      label: 'Hồ sơ cá nhân',
      onClick: () => navigate('/doctor/profile'),
    },
    {
      key: 'change-password',
      icon: <LockOutlined />,
      label: 'Đặt lại mật khẩu',
      onClick: () => navigate('/doctor/change-password'),
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Đăng xuất',
      onClick: handleLogout,
    },
  ];

  return (
    <Sider
      className="admin-sidebar"
      collapsible
      collapsed={collapsed}
      onCollapse={onCollapse}
      width={280}
      collapsedWidth={80}
      trigger={null}
      style={{
        position: "fixed",
        left: 0,
        top: 0,
        bottom: 0,
        zIndex: 1000,
        background: "#20374E",
        overflow: "auto",
        height: "100vh",
      }}
    >
      <div style={{ height: "100%", display: "flex", flexDirection: "column" }}>
        {/* Header */}
        <div
          style={{
            padding: collapsed ? "16px 8px" : "24px 16px",
            borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            textAlign: collapsed ? "center" : "left",
          }}
        >
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => onCollapse(!collapsed)}
            style={{
              fontSize: "16px",
              width: collapsed ? 40 : 64,
              height: 40,
              color: "white",
              marginBottom: collapsed ? 0 : 16,
            }}
          />

          {collapsed && (
            <div style={{ textAlign: "center", marginBottom: "16px" }}>
              <img src={logo} alt="Ánh Dương Logo" style={{ height: '32px', width: 'auto' }} />
            </div>
          )}

          {!collapsed && (
            <>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "12px",
                  color: "white",
                  fontSize: "20px",
                  fontWeight: "bold",
                  fontFamily: "Inter, sans-serif",
                  marginBottom: "4px",
                }}
              >
                <img src={logo} alt="Ánh Dương Logo" style={{ height: '32px', width: 'auto' }} />
                <span>Ánh Dương</span>
              </div>
              <Text
                style={{
                  color: "rgba(255, 255, 255, 0.7)",
                  fontSize: "14px",
                  fontFamily: "Inter, sans-serif",
                }}
              >
                Bác sĩ
              </Text>
              <div style={{ color: "#fff", fontSize: 13, marginTop: 8, display: 'flex', alignItems: 'center', gap: '6px' }}>
                {getDepartmentIcon(isBloodDepartment)}
                {departmentName}
              </div>
            </>
          )}
        </div>

        {/* Navigation Menu */}
        <Menu
          mode="inline"
          selectedKeys={getSelectedKey()}
          items={menuItems}
          style={{
            flex: 1,
            background: "transparent",
            border: "none",
            color: "white",
            overflow: "auto",
          }}
          theme="dark"
        />

        {/* Footer */}
        <div
          style={{
            padding: collapsed ? "16px 8px" : "16px",
            borderTop: "1px solid rgba(255, 255, 255, 0.1)",
            background: "#20374E",
          }}
        >
          {!collapsed && (
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="topRight"
              trigger={['click']}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  marginBottom: "12px",
                  color: "white",
                  cursor: "pointer",
                  padding: "8px",
                  borderRadius: "6px",
                  transition: "background-color 0.3s",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = "rgba(255, 255, 255, 0.1)";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "transparent";
                }}
              >
                <Avatar
                  size={32}
                  style={{ backgroundColor: "#D93E4C", marginRight: "8px" }}
                  icon={<UserOutlined />}
                />
                <Text
                  style={{
                    color: "white",
                    fontSize: "14px",
                    fontFamily: "Inter, sans-serif",
                  }}
                >
                  {doctorName}
                </Text>
              </div>
            </Dropdown>
          )}

          {collapsed && (
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="topRight"
              trigger={['click']}
            >
              <Avatar
                size={32}
                style={{
                  backgroundColor: "#D93E4C",
                  cursor: "pointer",
                  margin: "0 auto",
                  display: "block"
                }}
                icon={<UserOutlined />}
              />
            </Dropdown>
          )}
        </div>
      </div>
    </Sider>
  );
};

export default DoctorSidebar;
