import React, { useState, useEffect } from "react";
import DoctorLayout from "../../components/doctor/DoctorLayout";
import ProfileUpdateForm from "../../components/shared/ProfileUpdateForm";
import { getUserInfo } from "../../services/informationService";
import { toast } from "../../utils/toastUtils";
import authService from "../../services/authService";
import "../../styles/pages/ProfilePage.scss";

const DoctorProfilePage = () => {
  const [userInfo, setUserInfo] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUserInfo();
  }, []);

  const fetchUserInfo = async () => {
    try {
      setLoading(true);
      const currentUserData = await getUserInfo(); // This now returns only current user data

      console.log("Current user data from API:", currentUserData);

      if (currentUserData) {
        setUserInfo(currentUserData);
      } else {
        toast.error("Không tìm thấy thông tin người dùng hiện tại!");
      }
    } catch (error) {
      console.error("Error fetching user info:", error);
      toast.error("Không thể tải thông tin người dùng!");
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateSuccess = (updatedInfo) => {
    setUserInfo(prev => ({ ...prev, ...updatedInfo }));
  };

  return (
    <DoctorLayout>
      <div className="profile-page">
        <ProfileUpdateForm
          userInfo={userInfo}
          onUpdateSuccess={handleUpdateSuccess}
          title="Cập nhật hồ sơ bác sĩ"
        />
      </div>
    </DoctorLayout>
  );
};

export default DoctorProfilePage;
