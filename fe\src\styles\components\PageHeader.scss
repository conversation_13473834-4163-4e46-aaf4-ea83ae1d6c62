// Manager PageHeader Component Styles
// Medical color scheme: #D93E4C, #20374E, #DECCAA, #D91022

@use "../base/variables" as *;
@use "../base/mixin" as *;
@use "../base/admin-design-system.scss" as admin;
@use "sass:color";

.manager-page-header {
  background: $manager-bg;
  padding: $spacing-lg;
  border-radius: 12px;
  margin-bottom: $spacing-lg;
  box-shadow: 0 2px 8px $manager-shadow;
  border: 1px solid $manager-border;
  @include flex-align(space-between, flex-start);
  gap: $spacing-md;
  transition: all 0.3s ease;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: $spacing-md;
    padding: $spacing-md;
  }

  .header-info {
    flex: 1;
    min-width: 0; // Prevent flex item from overflowing

    .header-title-section {
      @include flex-align(flex-start, flex-start);
      gap: $spacing-md;

      @media (max-width: 576px) {
        flex-direction: column;
        gap: $spacing-sm;
      }

      .header-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        background: linear-gradient(
          135deg,
          $primary-color 0%,
          color.adjust($primary-color, $lightness: -10%) 100%
        );
        border-radius: 12px;
        color: white;
        font-size: 24px;
        flex-shrink: 0;
        box-shadow: 0 4px 12px rgba($primary-color, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba($primary-color, 0.4);
        }

        @media (max-width: 576px) {
          width: 40px;
          height: 40px;
          font-size: 20px;
        }
      }

      .header-text {
        flex: 1;
        min-width: 0;

        .header-title {
          margin: 0 0 4px 0 !important;
          font-size: 1.8rem;
          font-weight: 600;
          color: color.adjust($manager-text, $lightness: 10%);
          font-family: $font-manager;
          line-height: 1.2;
          word-wrap: break-word;

          @media (max-width: 768px) {
            font-size: 1.5rem;
          }

          @media (max-width: 576px) {
            font-size: 1.3rem;
          }
        }

        .header-description {
          margin: 0;
          color: color.adjust($manager-text-light, $lightness: 15%);
          font-size: 1rem;
          font-family: $font-manager;
          line-height: 1.4;
          word-wrap: break-word;

          @media (max-width: 768px) {
            font-size: 0.9rem;
          }

          @media (max-width: 576px) {
            font-size: 0.85rem;
          }
        }
      }
    }
  }

  .header-actions {
    flex-shrink: 0;

    @media (max-width: 768px) {
      width: 100%;

      .ant-space {
        width: 100%;
        justify-content: center;

        @media (max-width: 576px) {
          flex-direction: column;

          .ant-space-item {
            width: 100%;

            .ant-btn {
              width: 100%;
            }
          }
        }
      }
    }

    .ant-btn {
      font-family: $font-manager;
      border-radius: 8px;
      font-weight: 500;
      height: 40px;
      padding: 0 20px;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      &.ant-btn-primary {
        background: color.adjust($primary-color, $lightness: -8%);
        border-color: color.adjust($primary-color, $lightness: -8%);

        &:hover:not(:disabled) {
          background: color.adjust(
            color.adjust($primary-color, $lightness: -8%),
            $lightness: -8%
          );
          border-color: color.adjust(
            color.adjust($primary-color, $lightness: -8%),
            $lightness: -8%
          );
        }
      }

      &.ant-btn-default {
        background: white;
        border-color: $manager-border;
        color: $manager-text;

        &:hover:not(:disabled) {
          border-color: color.adjust($primary-color, $lightness: -8%);
          color: color.adjust($primary-color, $lightness: -8%);
        }
      }

      @media (max-width: 576px) {
        height: 36px;
        font-size: 14px;
        padding: 0 16px;
      }
    }
  }

  // Hover effect for entire header
  &:hover {
    box-shadow: 0 4px 16px rgba($manager-shadow, 0.15);
    border-color: color.adjust($primary-color, $lightness: -8%);
  }

  // Custom variants
  &.compact {
    padding: $spacing-md;
    margin-bottom: $spacing-md;

    .header-info .header-title-section {
      .header-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
      }

      .header-text .header-title {
        font-size: 1.5rem;
      }
    }
  }

  &.minimal {
    background: transparent;
    border: none;
    box-shadow: none;
    padding: $spacing-md 0;

    &:hover {
      box-shadow: none;
      border-color: transparent;
    }
  }
}

// Dark theme support
.dark-theme {
  .manager-page-header {
    background: color.adjust($manager-bg, $lightness: -5%);
    border-color: color.adjust($manager-border, $lightness: -10%);

    .header-info .header-title-section .header-text {
      .header-title {
        color: color.adjust($manager-text, $lightness: 10%);
      }

      .header-description {
        color: color.adjust($manager-text-light, $lightness: 15%);
      }
    }
  }
}

.page-header {
  background-color: admin.$background-white;
  border-radius: admin.$border-radius-lg;
  box-shadow: admin.$box-shadow-card;
  margin-bottom: admin.$spacing-lg;
  padding: admin.$spacing-lg;

  .header-content {
    @include flex-between;
    flex-wrap: wrap;
    gap: admin.$spacing-md;

    .header-title-section {
      display: flex;
      align-items: center;
      gap: admin.$spacing-md;
      flex: 1;
      min-width: 200px;

      .header-icon {
        font-size: 24px;
        color: $primary-color;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background-color: color.adjust($primary-color, $lightness: -10%);
        border-radius: admin.$border-radius-base;
      }

      .header-title {
        margin: 0;
        color: $text-primary;
        font-weight: 500;
      }

      .header-description {
        margin-top: admin.$spacing-xs;
        color: $text-secondary;
      }
    }

    .header-breadcrumb {
      margin: admin.$spacing-sm 0;
      color: $text-secondary;
      font-size: $font-size-sm;
    }

    .header-actions {
      display: flex;
      gap: admin.$spacing-sm;
      flex-wrap: wrap;

      .ant-btn {
        display: inline-flex;
        align-items: center;
        gap: admin.$spacing-xs;
        height: 32px;
        padding: 0 admin.$spacing-md;
        border-radius: admin.$border-radius-base;
        font-size: $font-size-base;
        transition: all admin.$transition-duration admin.$transition-timing;

        &:hover {
          transform: translateY(-1px);
        }

        &.ant-btn-primary {
          background-color: $primary-color;
          border-color: $primary-color;

          &:hover {
            background-color: color.adjust($primary-color, $lightness: -10%);
            border-color: color.adjust($primary-color, $lightness: -10%);
          }
        }

        .anticon {
          font-size: 16px;
        }
      }
    }
  }
}

// Responsive styles
@media screen and (max-width: 768px) {
  .page-header {
    padding: $spacing-md;

    .header-content {
      flex-direction: column;
      align-items: flex-start;

      .header-title-section {
        width: 100%;
      }

      .header-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }
  }
}

// Admin Page Header - Đồng bộ với Manager và Doctor
.admin-page-header {
  background: $manager-bg;
  padding: $spacing-lg;
  border-radius: 12px;
  margin-bottom: $spacing-lg;
  box-shadow: 0 2px 8px $manager-shadow;
  border: 1px solid rgba($primary-color, 0.2);
  @include flex-align(space-between, flex-start);
  gap: $spacing-md;
  transition: all 0.3s ease;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: $spacing-md;
    padding: $spacing-md;
  }

  .header-info {
    flex: 1;
    min-width: 0;

    .header-title-section {
      @include flex-align(flex-start, center);
      gap: $spacing-md;

      .header-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: linear-gradient(135deg, $primary-color 0%, $primary-hover 100%);
        @include flex-center;
        color: white;
        font-size: 24px;
        box-shadow: 0 4px 12px rgba($primary-color, 0.3);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 6px 16px rgba($primary-color, 0.4);
          transform: translateY(-2px);
        }
      }

      .header-text {
        flex: 1;
        min-width: 0;

        .header-title {
          margin: 0 !important;
          color: $text-primary !important;
          font-weight: 600;
          font-size: 24px;
          line-height: 1.2;
          font-family: $font-manager;

          @media (max-width: 768px) {
            font-size: 20px;
          }
        }

        .header-description {
          margin-top: 4px;
          color: $text-secondary !important;
          font-size: 14px;
          line-height: 1.4;
          font-family: $font-manager;
        }
      }
    }
  }

  .header-actions {
    flex-shrink: 0;
    @include flex-align(flex-end, flex-start);

    @media (max-width: 768px) {
      width: 100%;
      justify-content: flex-start;
    }

    .ant-space {
      @media (max-width: 576px) {
        width: 100%;

        .ant-space-item {
          flex: 1;

          .ant-btn {
            width: 100%;
          }
        }
      }
    }

    .ant-btn {
      height: 40px;
      border-radius: 8px;
      font-weight: 500;
      font-family: $font-manager;
      transition: all 0.3s ease;

      &.ant-btn-primary {
        background: $primary-color;
        border-color: $primary-color;

        &:hover:not(:disabled) {
          background: $primary-hover;
          border-color: $primary-hover;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba($primary-color, 0.3);
        }
      }

      &.ant-btn-default {
        background: white;
        border-color: $manager-border;
        color: $manager-text;

        &:hover:not(:disabled) {
          border-color: $primary-color;
          color: $primary-color;
        }
      }

      @media (max-width: 576px) {
        height: 36px;
        font-size: 14px;
        padding: 0 16px;
      }
    }
  }

  &:hover {
    box-shadow: 0 4px 16px rgba($primary-color, 0.15);
    border-color: rgba($primary-color, 0.3);
  }
}
