import { DONATION_STATUSES } from "../components/shared/ProcessWorkflowModal";


// Filter donations for schedule tab (all appointments with custom date sorting)
export const getScheduleDonations = (allDonations, filters, scheduleSort) => {
  // Get today's date at start of day for comparison
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Get tomorrow's date
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  let filtered = allDonations.filter(
    (d) => {
      // Show all appointments (not just registered ones)
      if (!d.appointmentDate) return false; // Skip if no appointment date
      return true;
    }
  );

  if (filters.bloodType !== "all") {
    filtered = filtered.filter((d) => d.bloodType === filters.bloodType);
  }

  // Sort donations with custom priority: today -> tomorrow -> future -> past
  return filtered.sort((a, b) => {
    const { field, order } = scheduleSort;

    // If sorting by appointment date with priority or standard date sorting
    if (field === "appointmentDate") {
      const aDate = new Date(a.appointmentDate);
      const bDate = new Date(b.appointmentDate);
      aDate.setHours(0, 0, 0, 0);
      bDate.setHours(0, 0, 0, 0);

      // Use priority sorting for "priority" order or default behavior
      if (order === "priority" || order === "desc") {
        // Helper function to get date priority
        const getDatePriority = (date) => {
          if (date.getTime() === today.getTime()) return 1; // Today
          if (date.getTime() === tomorrow.getTime()) return 2; // Tomorrow
          if (date > tomorrow) return 3; // Future
          return 4; // Past
        };

        const aPriority = getDatePriority(aDate);
        const bPriority = getDatePriority(bDate);

        // If different priorities, sort by priority
        if (aPriority !== bPriority) {
          return aPriority - bPriority;
        }

        // If same priority, sort by actual date
        if (aPriority === 3) { // Future dates - ascending (nearest first)
          return aDate - bDate;
        } else if (aPriority === 4) { // Past dates - descending (most recent first)
          return bDate - aDate;
        } else { // Today and tomorrow - by time if available
          return aDate - bDate;
        }
      } else {
        // Standard date sorting for "asc" order
        return order === "asc" ? aDate - bDate : bDate - aDate;
      }
    }

    // For other fields, use standard sorting
    let aValue, bValue;
    switch (field) {
      case "registrationDate":
        aValue = new Date(a.registrationDate);
        bValue = new Date(b.registrationDate);
        break;
      case "bloodType":
        aValue = a.bloodType;
        bValue = b.bloodType;
        break;
      case "expectedQuantity":
        aValue = parseInt(a.expectedQuantity);
        bValue = parseInt(b.expectedQuantity);
        break;
      default:
        aValue = a[field];
        bValue = b[field];
    }

    if (order === "asc") {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });
};

// Filter donations for process tab (process > 1 or approved/rejected)
export const getProcessDonations = (allDonations, filters, processSort) => {
  let filtered = allDonations.filter(
    (d) => (d.process && d.process > 1) ||
      d.status === true ||
      d.status === false ||
      (d.status !== DONATION_STATUSES.REGISTERED && typeof d.status === 'string')
  );

  if (filters.bloodType !== "all") {
    filtered = filtered.filter((d) => d.bloodType === filters.bloodType);
  }

  if (filters.status !== "all") {
    filtered = filtered.filter((d) => {
      // Support both process numbers and old string statuses
      if (typeof filters.status === 'number') {
        return d.process === filters.status;
      } else if (typeof filters.status === 'boolean') {
        return d.status === filters.status;
      } else {
        return d.status === filters.status;
      }
    });
  }

  // Sort donations
  return filtered.sort((a, b) => {
    const { field, order } = processSort;
    let aValue, bValue;

    switch (field) {
      case "status": {
        // Sort by process order (correct order: 1->2->3->4->5)
        if (a.process && b.process) {
          // Standard process order: 1 -> 2 -> 3 -> 4 -> 5
          aValue = a.process;
          bValue = b.process;
        } else {
          // Fallback to old status mapping with correct priority
          const statusOrder = {
            [DONATION_STATUSES.HEALTH_CHECKED]: 2,
            [DONATION_STATUSES.BLOOD_TAKEN]: 3,   // Lấy máu
            [DONATION_STATUSES.BLOOD_TESTED]: 4,  // Xét nghiệm máu
            [DONATION_STATUSES.STORED]: 5,
          };
          aValue = statusOrder[a.status] || (a.status === true ? 2 : 1);
          bValue = statusOrder[b.status] || (b.status === true ? 2 : 1);
        }
        break;
      }
      case "registrationDate":
        aValue = new Date(a.registrationDate);
        bValue = new Date(b.registrationDate);
        break;
      case "appointmentDate":
        aValue = new Date(a.appointmentDate);
        bValue = new Date(b.appointmentDate);
        break;
      case "bloodType":
        aValue = a.bloodType;
        bValue = b.bloodType;
        break;
      default:
        aValue = a[field];
        bValue = b[field];
    }

    if (order === "asc") {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });
};

// Get status info for display based on process number and status
export const getStatusInfo = (item) => {
  // Handle both old format (string status) and new format (process + status)
  if (typeof item === 'string') {
    // Old format - string status
    const statusMap = {
      [DONATION_STATUSES.REGISTERED]: {
        text: "Đã đăng ký",
        color: "#1890ff",
        icon: "UserOutlined",
      },
      [DONATION_STATUSES.HEALTH_CHECKED]: {
        text: "Đã khám sức khỏe cơ bản",
        color: "#52c41a",
        icon: "CheckCircleOutlined",
      },
      [DONATION_STATUSES.BLOOD_TAKEN]: {
        text: "Đã lấy máu",
        color: "#722ed1",
        icon: "HeartOutlined",
      },
      [DONATION_STATUSES.BLOOD_TESTED]: {
        text: "Đã xét nghiệm máu",
        color: "#fa8c16",
        icon: "ClockCircleOutlined",
      },
      [DONATION_STATUSES.STORED]: {
        text: "Đã nhập kho",
        color: "#13c2c2",
        icon: "CheckCircleOutlined",
      },
    };
    return statusMap[item] || statusMap[DONATION_STATUSES.REGISTERED];
  }

  // New format - process number + status boolean
  const process = item.process || 1;
  const status = item.status;

  // If rejected (status = false), show rejection
  if (status === false) {
    return {
      text: "Không chấp nhận",
      color: "#ff4d4f",
      icon: "CloseCircleOutlined",
    };
  }

  // Map process steps to display info
  const processMap = {
    1: {
      text: "Đăng ký",
      color: "#1890ff",
      icon: "UserOutlined",
    },
    2: {
      text: "Khám sức khỏe cơ bản",
      color: "#fa8c16",
      icon: "CheckCircleOutlined",
    },
    3: {
      text: "Lấy máu",
      color: "#722ed1",
      icon: "HeartOutlined",
    },
    4: {
      text: "Xét nghiệm máu",
      color: "#13c2c2",
      icon: "ClockCircleOutlined",
    },
    5: {
      text: "Nhập kho",
      color: "#52c41a",
      icon: "CheckCircleOutlined",
    },
  };

  return processMap[process] || processMap[1];
};

// Format date for display
export const formatDate = (date) => {
  if (!date) return "Chưa có";
  return new Date(date).toLocaleDateString("vi-VN");
};

// Format time slot for display
export const formatTimeSlot = (timeSlot) => {
  const timeSlotMap = {
    morning: "Sáng (7:00-11:00)",
    afternoon: "Chiều (13:00-17:00)",
  };
  return timeSlotMap[timeSlot] || timeSlot;
};

// Get notification status info
export const getNotificationStatusInfo = (status) => {
  const statusMap = {
    sent: { text: "Đã gửi nhắc nhở", color: "#52c41a" },
    pending: { text: "Chưa gửi", color: "#faad14" },
    failed: { text: "Gửi thất bại", color: "#ff4d4f" },
  };
  return statusMap[status] || statusMap.pending;
};

// Blood type options for filters
export const BLOOD_TYPE_OPTIONS = [
  { value: "all", label: "Tất cả" },
  { value: "A+", label: "A+" },
  { value: "A-", label: "A-" },
  { value: "B+", label: "B+" },
  { value: "B-", label: "B-" },
  { value: "AB+", label: "AB+" },
  { value: "AB-", label: "AB-" },
  { value: "O+", label: "O+" },
  { value: "O-", label: "O-" },
];

// Status options for process filter (updated order: xét nghiệm máu before lấy máu)
export const STATUS_OPTIONS = [
  { value: "all", label: "Tất cả" },
  { value: 2, label: "Khám sức khỏe cơ bản" },
  { value: 4, label: "Xét nghiệm máu" },
  { value: 3, label: "Lấy máu" },
  { value: 5, label: "Đã nhập kho" },
  { value: false, label: "Không chấp nhận" },
];

// Sort options for schedule
export const SCHEDULE_SORT_OPTIONS = [
  { value: "appointmentDate-priority", label: "Ngày hẹn" },
  { value: "appointmentDate-desc", label: "Ngày hẹn (mới nhất)" },
  { value: "appointmentDate-asc", label: "Ngày hẹn (cũ nhất)" },
  { value: "id-asc", label: "Mã lịch hẹn (tăng dần)" },
  { value: "id-desc", label: "Mã lịch hẹn (giảm dần)" },
  { value: "bloodType-asc", label: "Loại máu (A-Z)" },
  { value: "bloodType-desc", label: "Loại máu (Z-A)" },
  { value: "expectedQuantity-asc", label: "Lượng máu (tăng dần)" },
  { value: "expectedQuantity-desc", label: "Lượng máu (giảm dần)" },
];

// Sort options for process
export const PROCESS_SORT_OPTIONS = [
  { value: "status-asc", label: "Trạng thái (theo quy trình)" },
  { value: "appointmentDate-desc", label: "Ngày hẹn (mới nhất)" },
  { value: "appointmentDate-asc", label: "Ngày hẹn (cũ nhất)" },
  { value: "id-asc", label: "Mã lịch hẹn (tăng dần)" },
  { value: "id-desc", label: "Mã lịch hẹn (giảm dần)" },
  { value: "bloodType-asc", label: "Loại máu (A-Z)" },
  { value: "bloodType-desc", label: "Loại máu (Z-A)" },
];
