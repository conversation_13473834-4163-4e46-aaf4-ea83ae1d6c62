import vietnamProvinces from '../data/vietnam-provinces.json';


class VietnamAddressService {
  constructor() {
    this.data = vietnamProvinces;
    this.cache = {
      provinces: null,
      wards: new Map(), // Cache wards by province name
    };
  }

  async getProvinces() {
    // Return cached data if available
    if (this.cache.provinces) {
      return this.cache.provinces;
    }

    try {
      if (this.data && this.data.success && Array.isArray(this.data.data)) {
        const provinces = this.data.data.map(item => item.province);
        this.cache.provinces = provinces; // Cache the result
        return provinces;
      } else {
        throw new Error('Invalid province data format');
      }
    } catch (error) {
      console.error('Failed to load province data:', error);
      throw new Error('Không thể tải dữ liệu tỉnh thành');
    }
  }

  async getWardsByProvince(provinceName) {
    if (!provinceName) {
      return [];
    }

    // Return cached data if available
    if (this.cache.wards.has(provinceName)) {
      return this.cache.wards.get(provinceName);
    }

    try {
      if (this.data && this.data.success && Array.isArray(this.data.data)) {
        const provinceData = this.data.data.find(item => item.province === provinceName);
        if (provinceData && Array.isArray(provinceData.wards)) {
          const wards = provinceData.wards.map(ward => ward.name);
          this.cache.wards.set(provinceName, wards); // Cache the result
          return wards;
        } else {
          throw new Error('Province not found or invalid ward data');
        }
      } else {
        throw new Error('Invalid data format');
      }
    } catch (error) {
      console.error('Failed to load ward data:', error);
      throw new Error('Không thể tải dữ liệu phường/xã');
    }
  }

  /**
   * Clear cache (useful for refreshing data)
   */
  clearCache() {
    this.cache.provinces = null;
    this.cache.wards.clear();
  }

  
  getCachedProvinces() {
    return this.cache.provinces;
  }

 
  getCachedWards(provinceName) {
    return this.cache.wards.get(provinceName) || null;
  }

 
  async preloadAllData() {
    try {
      if (this.data && this.data.success && Array.isArray(this.data.data)) {
        // Cache provinces
        const provinces = this.data.data.map(item => item.province);
        this.cache.provinces = provinces;

        // Cache all wards
        this.data.data.forEach(provinceData => {
          if (provinceData.wards && Array.isArray(provinceData.wards)) {
            const wards = provinceData.wards.map(ward => ward.name);
            this.cache.wards.set(provinceData.province, wards);
          }
        });

        console.log('Vietnam address data preloaded successfully');
      } else {
        throw new Error('Invalid data format');
      }
    } catch (error) {
      console.error('Failed to preload Vietnam address data:', error);
      throw error;
    }
  }
}

// Export singleton instance
export default new VietnamAddressService();
