
// Status mapping từ API
export const BLOOD_REQUEST_STATUS = {
  0: "Đang chờ xử lý",
  1: "Đ<PERSON> duyệt",
  2: "<PERSON><PERSON><PERSON> thành",
  3: "<PERSON>ừ chối",
  4: "<PERSON><PERSON> hủy",
};

// Reverse mapping để tìm status code từ text
export const BLOOD_REQUEST_STATUS_REVERSE = Object.entries(
  BLOOD_REQUEST_STATUS
).reduce((acc, [code, text]) => {
  acc[text.toLowerCase()] = parseInt(code);
  return acc;
}, {});


export const isPendingRequest = (request) => {
  // Handle numeric status (API returns 0 for pending)
  if (typeof request.status === "number") {
    return request.status === 0; // 0 = "Đang chờ xử lý"
  }

  // Handle string status for backward compatibility
  const status = request.status?.toLowerCase();
  return (
    status === "pending" ||
    status === "chờ xử lý" ||
    status === "đang chờ xử lý" ||
    request.status === "PENDING"
  );
};


export const getStatusDisplayText = (status) => {
  if (typeof status === "number") {
    return BLOOD_REQUEST_STATUS[status] || `Không xác định (${status})`;
  }
  return status; // Return as-is if already string
};


export const getStatusCode = (displayText) => {
  const code = BLOOD_REQUEST_STATUS_REVERSE[displayText.toLowerCase()];
  return code !== undefined ? code : null;
};

export const isCompletedStatus = (status) => {
  if (typeof status === "number") {
    return status === 2; // 2 = "Hoàn thành"
  }
  const statusLower = status?.toLowerCase();
  return statusLower === "completed" || statusLower === "hoàn thành";
};

export const isApprovedStatus = (status) => {
  if (typeof status === "number") {
    return status === 1; // 1 = "Đã duyệt"
  }
  const statusLower = status?.toLowerCase();
  return statusLower === "approved" || statusLower === "đã duyệt";
};


export const isRejectedStatus = (status) => {
  if (typeof status === "number") {
    return status === 3; // 3 = "Từ chối"
  }
  const statusLower = status?.toLowerCase();
  return statusLower === "rejected" || statusLower === "từ chối";
};


export const getStatusColor = (status) => {
  if (isPendingRequest({ status })) return "#faad14"; // Orange
  if (isApprovedStatus(status)) return "#52c41a"; // Green
  if (isCompletedStatus(status)) return "#1677ff"; // Blue
  if (isRejectedStatus(status)) return "#ff4d4f"; // Red
  return "#d9d9d9"; // Gray for unknown
};

export default {
  BLOOD_REQUEST_STATUS,
  BLOOD_REQUEST_STATUS_REVERSE,
  isPendingRequest,
  getStatusDisplayText,
  getStatusCode,
  isCompletedStatus,
  isApprovedStatus,
  isRejectedStatus,
  getStatusColor,
};
