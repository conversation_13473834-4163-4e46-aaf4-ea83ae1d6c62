@use "../base/variables" as vars;

.logout-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &__icon {
    font-size: 1rem;
  }

  &__spinner {
    width: 14px;
    height: 14px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  // Default variant
  &--default {
    background: #dc3545;
    color: white;

    &:hover:not(:disabled) {
      background: #c82333;
      transform: translateY(-1px);
    }
  }

  // Sidebar variant
  &--sidebar {
    background: transparent;
    color: #666;
    padding: 0.75rem 1rem;
    width: 100%;
    justify-content: flex-start;
    border-radius: 8px;

    &:hover:not(:disabled) {
      background: #f8f9fa;
      color: #dc3545;
    }
  }

  // Header variant
  &--header {
    background: transparent;
    color: #666;
    padding: 0.5rem;
    border: 1px solid #ddd;

    &:hover:not(:disabled) {
      background: #dc3545;
      color: white;
      border-color: #dc3545;
    }
  }

  // Icon only variant
  &--icon-only {
    padding: 0.5rem;
    min-width: auto;

    .logout-button__icon {
      margin: 0;
    }

    span:not(.logout-button__icon):not(.logout-button__spinner) {
      display: none;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Responsive
@media (max-width: 768px) {
  .logout-button {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;

    &--sidebar {
      padding: 0.6rem 0.8rem;
    }
  }
}
