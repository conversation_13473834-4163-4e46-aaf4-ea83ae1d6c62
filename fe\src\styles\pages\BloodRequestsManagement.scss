// Blood Requests Management Page Styles
// Medical color scheme: #D93E4C, #20374E, #DECCAA, #D91022

@use "../base/variables" as vars;
@use "../base/mixin" as *;
@use "sass:color";

.blood-requests-management {
  display: flex;
  min-height: 100vh;

  .blood-requests-content {
    flex: 1;
    padding: 24px;
    margin-left: 280px; // Sidebar width
    transition: margin-left 0.3s ease;

    @media (max-width: 768px) {
      margin-left: 0;
      padding: 16px;
    }

    // Page Header
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 24px;
      padding: 16px 0;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 16px;
      }

      .header-info {
        h2 {
          margin-bottom: 8px !important;
          color: #20374e;
          font-weight: 600;
        }
      }

      .header-actions {
        .ant-btn {
          margin-left: 8px;

          &:first-child {
            margin-left: 0;
          }

          @media (max-width: 768px) {
            margin: 0 4px;
            font-size: 12px;
            padding: 4px 8px;
          }
        }
      }
    }

    // Statistics Section
    .stats-section {
      margin-bottom: 24px;

      .stat-card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .stat-content {
          text-align: center;
          padding: 8px 0;

          .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 4px;
            line-height: 1;
          }

          .stat-label {
            color: #666;
            font-size: 0.9rem;
            font-weight: 500;
          }
        }
      }
    }

    // Filters Card
    .filters-card {
      margin-bottom: 16px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .filter-group {
        .ant-input,
        .ant-select {
          border-radius: 6px;
          border-color: #deccaa;

          &:hover,
          &:focus {
            border-color: #d93e4c;
            box-shadow: 0 0 0 2px rgba(217, 62, 76, 0.1);
          }
        }
      }
    }

    .tabs-navigation {
      display: flex;
      gap: 1rem;
      margin-bottom: 2rem;

      .tab-btn {
        padding: 1rem 2rem;
        border: none;
        border-radius: 16px;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.7);
        color: #6c757d;
        font-size: 1rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &:hover {
          background: rgba(255, 255, 255, 0.9);
          transform: translateY(-2px);
        }

        &.active {
          background: linear-gradient(135deg, #667eea, #764ba2);
          color: white;
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
          }
        }
      }
    }

    .filters-section {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      padding: 1.5rem;
      border-radius: 12px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
      display: flex;
      gap: 2rem;
      flex-wrap: wrap;

      .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        label {
          font-weight: 600;
          color: #333;
          font-size: 0.9rem;
        }

        select {
          padding: 0.5rem;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 0.9rem;
          min-width: 150px;

          &:focus {
            outline: none;
            border-color: vars.$primary-color;
            box-shadow: 0 0 0 3px rgba(vars.$primary-color, 0.1);
          }
        }
      }
    }

    // Table Card - Synchronized with DonationSchedulePage
    .requests-table-card {
      background: #ffffff;
      border-radius: 12px;
      border: 1px solid vars.$manager-border;
      box-shadow: 0 2px 8px vars.$manager-shadow;
      overflow-x: auto;
      max-width: 100%;

      .ant-table {
        min-width: 1200px;
        font-family: vars.$font-manager;

        .ant-table-thead > tr > th {
          background: #ffffff;
          color: vars.$manager-text;
          font-weight: 600;
          border-bottom: 1px solid vars.$manager-border;
          padding: 16px;
          text-align: center;
          font-size: 14px;
          font-family: vars.$font-manager;

          &:first-child {
            border-top-left-radius: 8px;
          }

          &:last-child {
            border-top-right-radius: 8px;
          }
        }

        .ant-table-tbody > tr {
          transition: all 0.3s ease;

          &:hover > td {
            background: rgba(vars.$primary-color, 0.02);
          }

          &.urgent-row {
            background-color: rgba(217, 16, 34, 0.05);
            border-left: 4px solid #d91022;
          }

          &.priority-row {
            background-color: rgba(217, 62, 76, 0.05);
            border-left: 4px solid #d93e4c;
          }

          > td {
            background: #ffffff;
            border-bottom: 1px solid vars.$manager-border;
            padding: 16px;
            text-align: center;
            font-size: 14px;
            font-family: vars.$font-manager;
          }
        }

        .ant-tag {
          border-radius: 6px;
          font-weight: 600;
          padding: 4px 12px;
          font-family: vars.$font-manager;
        }

        .ant-btn {
          border-radius: 6px;
          font-weight: 500;
          font-family: vars.$font-manager;

          &:hover {
            transform: translateY(-1px);
          }
        }
      }

      .ant-pagination {
        margin: 16px 0;
        text-align: center;

        .ant-pagination-item {
          border-radius: 6px;
          border: 1px solid #e8e8e8;
          background-color: #ffffff;
          color: #666666;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            border-color: #20374e;
            color: #20374e;
            background-color: rgba(32, 55, 78, 0.05);
          }

          a {
            color: inherit;
            font-weight: inherit;
          }
        }

        .ant-pagination-item-active {
          background-color: #20374e;
          border-color: #20374e;
          color: #ffffff;
          font-weight: 600;

          &:hover {
            background-color: #20374e;
            border-color: #20374e;
            color: #ffffff;
          }

          a {
            color: #ffffff;
            font-weight: 600;
          }
        }

        .ant-pagination-prev,
        .ant-pagination-next {
          border-radius: 6px;
          border: 1px solid #e8e8e8;
          color: #666666;
          transition: all 0.3s ease;

          &:hover {
            border-color: #20374e;
            color: #20374e;
            background-color: rgba(32, 55, 78, 0.05);
          }

          .ant-pagination-item-link {
            color: inherit;
            border: none;
            background: transparent;
          }
        }

        .ant-pagination-disabled {
          opacity: 0.4;
          cursor: not-allowed;

          &:hover {
            border-color: #e8e8e8;
            color: #666666;
            background-color: #ffffff;
          }
        }

        .ant-pagination-jump-prev,
        .ant-pagination-jump-next {
          color: #666666;

          &:hover {
            color: #20374e;
          }
        }
      }
    }

    // Card View
    .request-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
      }

      .card-content {
        .info-row {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .anticon {
            font-size: 14px;
          }
        }
      }

      .ant-card-actions {
        background-color: #f8f9fa;
        border-top: 1px solid #deccaa;

        li {
          margin: 8px 0;

          .anticon {
            font-size: 16px;
            transition: all 0.3s ease;

            &:hover {
              transform: scale(1.2);
            }
          }
        }
      }
    }

    .requests-table-container {
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      margin-bottom: 2rem;

      .requests-table {
        width: 100%;
        border-collapse: collapse;

        th,
        td {
          padding: 1rem;
          text-align: left;
          border-bottom: 1px solid #eee;
        }

        th {
          background: #f8f9fa;
          font-weight: 600;
          color: #333;
          font-size: 0.9rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        td {
          font-size: 0.9rem;
        }

        tr:hover {
          background: #f8f9fa;
        }

        .blood-type-badge {
          display: inline-block;
          padding: 6px 25px;
          border-radius: 20px;
          font-weight: 700;
          font-size: 20px;
          text-align: center;
          min-height: 40px;
          min-width: 50px;

          &.positive {
            background-color: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
          }

          &.negative {
            background-color: #ffebee;
            color: #c62828;
            border: 1px solid #ffcdd2;
          }
        }

        .status-badge {
          display: inline-flex;
          align-items: center;
          padding: 6px 12px;
          border-radius: 20px;
          font-weight: 600;
          font-size: 14px;
          border: 1px solid;
          white-space: nowrap;
        }

        .status-badge,
        .urgency-badge {
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          font-weight: 600;
          font-size: 0.8rem;
          text-transform: uppercase;

          &.status-warning,
          &.urgency-warning {
            background: #fff3cd;
            color: #856404;
          }

          &.status-info,
          &.urgency-info {
            background: #d1ecf1;
            color: #0c5460;
          }

          &.status-success,
          &.urgency-success {
            background: #d4edda;
            color: #155724;
          }

          &.status-danger,
          &.urgency-danger {
            background: #f8d7da;
            color: #721c24;
          }

          &.status-secondary,
          &.urgency-secondary {
            background: #e2e3e5;
            color: #383d41;
          }
        }

        .action-buttons {
          display: flex;
          gap: 0.5rem;
          flex-wrap: wrap;

          .btn {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;

            &.btn-sm {
              padding: 0.25rem 0.5rem;
            }

            &.btn-info {
              background: #17a2b8;
              color: white;

              &:hover {
                background: #138496;
              }
            }

            &.btn-success {
              background: #28a745;
              color: white;

              &:hover {
                background: #218838;
              }
            }

            &.btn-danger {
              background: #dc3545;
              color: white;

              &:hover {
                background: #c82333;
              }
            }

            &.btn-primary {
              background: color.adjust(vars.$primary-color, $lightness: -10%);
              color: white;

              &:hover {
                background: color.adjust(vars.$primary-color, $lightness: -20%);
              }
            }
          }
        }
      }
    }

    .statistics-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;

      .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        text-align: center;

        h3 {
          margin: 0 0 1rem 0;
          color: #666;
          font-size: 0.9rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .stat-number {
          font-size: 2rem;
          font-weight: 700;
          margin: 0;
          color: vars.$primary-color;

          &.warning {
            color: #ffc107;
          }

          &.success {
            color: #28a745;
          }

          &.danger {
            color: #dc3545;
          }
        }
      }
    }
  }
}

// Modal styles
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .modal-content {
    background: white;
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);

    .modal-header {
      padding: 1.5rem;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        margin: 0;
        color: vars.$primary-color;
      }

      .close-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #666;

        &:hover {
          color: #333;
        }
      }
    }

    .modal-body {
      padding: 1.5rem;

      .detail-row {
        margin-bottom: 1rem;
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;

        strong {
          min-width: 120px;
          color: #333;
        }

        .notes-list {
          width: 100%;
          margin-top: 0.5rem;

          .note-item {
            background: #f8f9fa;
            padding: 0.75rem;
            border-radius: 6px;
            margin-bottom: 0.5rem;

            small {
              color: #666;
              font-size: 0.8rem;
              margin-left: 0.5rem;
            }
          }
        }
      }
    }
  }

  // Emergency Requests Section
  .emergency-requests-section {
    .emergency-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;

      .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        padding: 2rem;
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        text-align: center;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 20px 45px rgba(0, 0, 0, 0.15);
        }

        .stat-number {
          font-size: 2.5rem;
          font-weight: 800;
          margin: 0 0 0.5rem 0;
          background: linear-gradient(135deg, #667eea, #764ba2);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .stat-label {
          color: #6c757d;
          font-size: 1rem;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        &.urgent {
          border-left: 4px solid #ffc107;
        }

        &.critical {
          border-left: 4px solid #dc3545;
        }

        &.rare {
          border-left: 4px solid #6f42c1;
        }
      }
    }

    .emergency-requests-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
      margin-bottom: 2rem;

      .emergency-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
        }

        &.urgent::before {
          background: linear-gradient(135deg, #ffc107, #fd7e14);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        &.critical::before {
          background: linear-gradient(135deg, #dc3545, #c82333);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          animation: pulse 2s infinite;
        }

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 20px 45px rgba(0, 0, 0, 0.15);
        }

        .emergency-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1.5rem;

          .blood-type-large {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #dc3545, #fd7e14);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }

          .urgency-indicator {
            font-size: 0.9rem;
            font-weight: 700;
            padding: 0.5rem 1rem;
            border-radius: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;

            &:contains("CỰC KHẨN CẤP") {
              background: linear-gradient(135deg, #dc3545, #c82333);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              color: white;
              animation: pulse 2s infinite;
            }

            &:contains("KHẨN CẤP") {
              background: linear-gradient(135deg, #ffc107, #fd7e14);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              color: white;
            }
          }
        }

        .emergency-details {
          margin-bottom: 1.5rem;

          .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.75rem;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);

            .label {
              font-weight: 600;
              color: #6c757d;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              font-size: 0.9rem;
            }

            .value {
              font-weight: 700;
              color: #495057;

              &.countdown {
                color: #dc3545;
                font-weight: 800;
              }
            }
          }
        }

        .emergency-actions {
          display: flex;
          gap: 1rem;

          .btn {
            flex: 1;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 12px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;

            &.btn-success {
              background: linear-gradient(135deg, #28a745, #20c997);
              color: white;
              box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);

              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
              }
            }
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 4rem;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 20px;
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);

      .empty-icon {
        font-size: 5rem;
        display: block;
        margin-bottom: 1.5rem;
      }

      h3 {
        margin: 0 0 1rem 0;
        color: #495057;
        font-size: 1.5rem;
        font-weight: 700;
      }

      p {
        margin: 0;
        color: #6c757d;
        font-size: 1.1rem;
        line-height: 1.5;
      }
    }
  }
}

// Animations
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

// Enhanced Responsive Design
@media (max-width: 1200px) {
  .blood-requests-management {
    .blood-requests-content {
      .requests-table-card {
        .ant-table {
          font-size: 14px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .blood-requests-management {
    .blood-requests-content {
      margin-left: 0;
      padding: 16px;

      .stats-section {
        .ant-col {
          margin-bottom: 16px;
        }
      }

      .filters-card {
        .ant-row {
          .ant-col {
            margin-bottom: 16px;
          }
        }
      }

      .request-card {
        margin-bottom: 16px;
      }

      .filters-section {
        flex-direction: column;
        gap: 1rem;

        .filter-group {
          select {
            min-width: 100%;
          }
        }
      }

      .requests-table-container {
        overflow-x: auto;

        .requests-table {
          min-width: 800px;
        }
      }

      .statistics-section {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;

        .stat-card {
          padding: 1rem;

          .stat-number {
            font-size: 1.5rem;
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .blood-requests-management {
    .blood-requests-content {
      .page-header {
        .header-actions {
          .ant-space {
            flex-wrap: wrap;
            justify-content: center;
          }
        }
      }

      .requests-table-card {
        .ant-table {
          .ant-table-thead > tr > th,
          .ant-table-tbody > tr > td {
            padding: 8px 4px;
            font-size: 12px;
          }
        }
      }
    }
  }
}

// Modal Styles - Push-up Effect
.blood-requests-management {
  .ant-modal {
    .ant-modal-content {
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);

      .ant-modal-header {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-bottom: 1px solid #e8e8e8;
        padding: 20px 24px;

        .ant-modal-title {
          font-family: vars.$font-manager;
          font-weight: 600;
          font-size: 18px;
        }
      }

      .ant-modal-body {
        font-family: vars.$font-manager;

        .manage-content {
          .ant-card {
            border-radius: 8px;
            border: 1px solid #e8e8e8;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            }
          }

          .ant-btn {
            font-family: vars.$font-manager;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;

            &:hover:not(:disabled) {
              transform: translateY(-1px);
            }

            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
            }
          }

          .ant-input {
            border-radius: 6px;
            border: 1px solid #e8e8e8;
            font-family: vars.$font-manager;

            &:hover,
            &:focus {
              border-color: vars.$primary-color;
              box-shadow: 0 0 0 2px rgba(vars.$primary-color, 0.1);
            }
          }
        }
      }
    }

    // Push-up animation
    &.ant-modal-centered {
      .ant-modal-content {
        animation: modalPushUp 0.3s ease-out;
      }
    }
  }
}

// Modal Push-up Animation
@keyframes modalPushUp {
  0% {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
