import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  Card,
  Progress,
  Typography,
  Space,
  Divider,
  Alert,
} from "antd";
import { toast } from "../../../utils/toastUtils";
import {
  PlayCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import * as bloodArticleService from "../../../services/bloodArticleService";
import * as newsService from "../../../services/newsService";

const { Title, Text, Paragraph } = Typography;

const ContentFormatter = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState(0);
  const [logs, setLogs] = useState([]);
  const [stats, setStats] = useState({
    total: 0,
    processed: 0,
    updated: 0,
    errors: 0,
  });

  const addLog = (message, type = "info") => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs((prev) => [...prev, { message, type, timestamp }]);
  };

  // Function to clean and format HTML content
  const formatContent = (content) => {
    if (!content) return content;

    let formatted = content;

    // Remove extra whitespace and line breaks
    formatted = formatted.replace(/\s+/g, " ").trim();

    // Ensure proper paragraph spacing
    formatted = formatted.replace(/<\/p>\s*<p>/g, "</p>\n<p>");

    // Clean up blockquotes
    formatted = formatted.replace(/<blockquote>\s*/g, "<blockquote>\n  ");
    formatted = formatted.replace(/\s*<\/blockquote>/g, "\n</blockquote>");

    // Format lists properly
    formatted = formatted.replace(/<\/li>\s*<li>/g, "</li>\n<li>");
    formatted = formatted.replace(/<ul>\s*/g, "<ul>\n  ");
    formatted = formatted.replace(/\s*<\/ul>/g, "\n</ul>");
    formatted = formatted.replace(/<ol>\s*/g, "<ol>\n  ");
    formatted = formatted.replace(/\s*<\/ol>/g, "\n</ol>");

    // Format headers
    for (let i = 1; i <= 6; i++) {
      const regex = new RegExp(`<\/h${i}>\\s*<h${i}>`, "g");
      formatted = formatted.replace(regex, `</h${i}>\n<h${i}>`);
    }

    // Add proper spacing around block elements
    const blockElements = [
      "div",
      "p",
      "blockquote",
      "h1",
      "h2",
      "h3",
      "h4",
      "h5",
      "h6",
      "ul",
      "ol",
      "pre",
    ];
    blockElements.forEach((tag) => {
      const openRegex = new RegExp(`<${tag}([^>]*)>`, "g");
      const closeRegex = new RegExp(`<\/${tag}>`, "g");

      formatted = formatted.replace(openRegex, `\n<${tag}$1>`);
      formatted = formatted.replace(closeRegex, `</${tag}>\n`);
    });

    // Clean up multiple newlines
    formatted = formatted.replace(/\n\s*\n\s*\n/g, "\n\n");
    formatted = formatted.trim();

    return formatted;
  };

  const formatAllArticles = async () => {
    setIsRunning(true);
    setProgress(0);
    setLogs([]);
    setStats({ total: 0, processed: 0, updated: 0, errors: 0 });

    try {
      addLog("Bắt đầu format content cho tất cả bài viết...", "info");

      // Fetch all blood articles
      addLog("Đang tải danh sách Blood Articles...", "info");
      const bloodArticles = await bloodArticleService.getBloodArticles();
      const bloodArticlesList = Array.isArray(bloodArticles)
        ? bloodArticles
        : bloodArticles?.data || [];

      // Fetch all news articles
      addLog("Đang tải danh sách News Articles...", "info");
      const newsArticles = await newsService.getAllNews();
      const newsArticlesList = Array.isArray(newsArticles)
        ? newsArticles
        : newsArticles?.data || [];

      const allArticles = [
        ...bloodArticlesList.map((article) => ({ ...article, type: "blood" })),
        ...newsArticlesList.map((article) => ({ ...article, type: "news" })),
      ];

      const total = allArticles.length;
      setStats((prev) => ({ ...prev, total }));
      addLog(`Tổng cộng ${total} bài viết cần xử lý`, "info");

      if (total === 0) {
        addLog("Không có bài viết nào để xử lý", "warning");
        setIsRunning(false);
        return;
      }

      let processed = 0;
      let updated = 0;
      let errors = 0;

      for (const article of allArticles) {
        try {
          const originalContent = article.content;

          if (!originalContent) {
            addLog(
              `Bỏ qua bài viết ${
                article.articleId || article.id
              }: Không có content`,
              "warning"
            );
            processed++;
            continue;
          }

          const formattedContent = formatContent(originalContent);

          // Check if content actually changed
          if (formattedContent === originalContent) {
            addLog(
              `Bài viết ${
                article.articleId || article.id
              }: Content đã chuẩn`,
              "success"
            );
            processed++;
            setProgress((processed / total) * 100);
            setStats((prev) => ({ ...prev, processed }));
            continue;
          }

          // Update the article
          const updateData = {
            title: article.title,
            content: formattedContent,
            tagIds:
              article.tags?.map((tag) =>
                typeof tag === "object" ? tag.tagId : tag
              ) || [],
          };

          let response;
          if (article.type === "blood") {
            response = await bloodArticleService.updateArticle(
              article.articleId || article.id,
              updateData
            );
          } else {
            response = await newsService.updateNews(
              article.articleId || article.id,
              updateData
            );
          }

          if (response?.success !== false) {
            addLog(
              `Đã format bài viết ${article.articleId || article.id}: "${
                article.title
              }"`,
              "success"
            );
            updated++;
          } else {
            throw new Error(response?.message || "Update failed");
          }
        } catch (error) {
          addLog(
            `Lỗi khi xử lý bài viết ${article.articleId || article.id}: ${
              error.message
            }`,
            "error"
          );
          errors++;
        }

        processed++;
        setProgress((processed / total) * 100);
        setStats((prev) => ({ ...prev, processed, updated, errors }));

        // Small delay to prevent overwhelming the server
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      addLog(
        `Hoàn thành! Đã xử lý ${processed}/${total} bài viết`,
        "success"
      );
      addLog(
        `Kết quả: ${updated} bài viết được cập nhật, ${errors} lỗi`,
        "info"
      );

      if (updated > 0) {
        toast.success(`Đã format thành công ${updated} bài viết!`);
      } else {
        toast.info("Tất cả bài viết đã có format chuẩn!");
      }
    } catch (error) {
      addLog(`Lỗi nghiêm trọng: ${error.message}`, "error");
      toast.error(" Có lỗi xảy ra trong quá trình format!");
    } finally {
      setIsRunning(false);
    }
  };

  const getLogIcon = (type) => {
    switch (type) {
      case "success":
        return "✓";
      case "error":
        return "✗";
      case "warning":
        return "!";
      default:
        return "i";
    }
  };

  const getLogColor = (type) => {
    switch (type) {
      case "success":
        return "#52c41a";
      case "error":
        return "#ff4d4f";
      case "warning":
        return "#faad14";
      default:
        return "#1890ff";
    }
  };

  return (
    <div style={{ padding: "24px" }}>
      <Card>
        <Title level={3}>
          <ExclamationCircleOutlined
            style={{ color: "#faad14", marginRight: "8px" }}
          />
          Content Formatter Tool
        </Title>

        <Alert
          message="Công cụ tự động format content"
          description="Tool này sẽ tự động format lại HTML content của tất cả bài viết để có cấu trúc đẹp và chuẩn. Quá trình này có thể mất vài phút."
          type="info"
          showIcon
          style={{ marginBottom: "16px" }}
        />

        <Space direction="vertical" size="large" style={{ width: "100%" }}>
          <div>
            <Button
              type="primary"
              size="large"
              icon={<PlayCircleOutlined />}
              onClick={formatAllArticles}
              loading={isRunning}
              disabled={isRunning}
            >
              {isRunning ? "Đang xử lý..." : "Bắt đầu Format All Content"}
            </Button>
          </div>

          {isRunning && (
            <div>
              <Text strong>Tiến độ: {progress.toFixed(1)}%</Text>
              <Progress
                percent={progress}
                status={progress === 100 ? "success" : "active"}
                strokeColor={{
                  "0%": "#108ee9",
                  "100%": "#87d068",
                }}
              />
            </div>
          )}

          {(stats.total > 0 || logs.length > 0) && (
            <Card size="small" title="Thống kê">
              <Space split={<Divider type="vertical" />}>
                <Text>
                  Tổng: <strong>{stats.total}</strong>
                </Text>
                <Text>
                  Đã xử lý: <strong>{stats.processed}</strong>
                </Text>
                <Text style={{ color: "#52c41a" }}>
                  Cập nhật: <strong>{stats.updated}</strong>
                </Text>
                <Text style={{ color: "#ff4d4f" }}>
                  Lỗi: <strong>{stats.errors}</strong>
                </Text>
              </Space>
            </Card>
          )}

          {logs.length > 0 && (
            <Card
              size="small"
              title="Logs"
              style={{ maxHeight: "400px", overflow: "auto" }}
            >
              {logs.map((log, index) => (
                <div
                  key={index}
                  style={{ marginBottom: "4px", fontSize: "12px" }}
                >
                  <Text style={{ color: "#666" }}>[{log.timestamp}]</Text>
                  <Text
                    style={{ color: getLogColor(log.type), marginLeft: "8px" }}
                  >
                    {getLogIcon(log.type)} {log.message}
                  </Text>
                </div>
              ))}
            </Card>
          )}
        </Space>
      </Card>
    </div>
  );
};

export default ContentFormatter;
