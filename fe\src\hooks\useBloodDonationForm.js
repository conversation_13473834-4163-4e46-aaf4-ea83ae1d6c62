import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import authService from "../services/authService";
import GeolibService from "../services/geolibService";
import { getUserName } from "../utils/userUtils";
import userInfoService from "../services/userInfoService";
import { useUserData } from "../contexts/UserDataContext";
import bloodDonationService from "../services/bloodDonationService";
import dayjs from "dayjs";

/**
 * Custom hook để quản lý state tổng thể của form hiến máu
 */
export const useBloodDonationForm = () => {
  const navigate = useNavigate();
  const currentUser = authService.getCurrentUser();
  const { getUserData, clearCache } = useUserData();

  // State chính của form
  const [step, setStep] = useState(1); // 1: Personal Info, 2: Health Survey, 3: Schedule Appointment
  const [loading, setLoading] = useState(false);
  const [registrationResult, setRegistrationResult] = useState(null);
  const [distanceInfo, setDistanceInfo] = useState(null);
  const [hasSystemDonationHistory, setHasSystemDonationHistory] = useState(false);
  const [addressRefreshTrigger, setAddressRefreshTrigger] = useState(0);

  // State thông tin cá nhân
  const [personalInfo, setPersonalInfo] = useState({
    fullName: "",
    email: "",
    phone: "",
    dateOfBirth: "",
    gender: "",
    address: {
      houseNumber: "",
      street: "",
      province: "",
      district: "",
      ward: "",
      provinceName: "",
      districtName: "",
      wardName: "",
      fullAddress: "",
      coordinates: { lat: null, lng: null },
      distance: null,
      travelTime: null,
      formattedAddress: "",
    },
  });

  // State khảo sát sức khỏe
  const [healthSurvey, setHealthSurvey] = useState({
    // Basic Info
    weight: "",
    height: "",
    bloodPressure: "",
    heartRate: "",
    bloodType: "",

    // Question 1: Previous Donation
    hasDonatedBefore: null, // true/false/null
    lastDonationDate: "", // New field for last donation date

    // Question 2: Current Medical Conditions
    hasCurrentMedicalConditions: null, // true/false/null
    currentMedicalConditionsDetail: "", // New field for current medical conditions detail

    // Question 3: Previous Serious Conditions
    hasPreviousSeriousConditions: null, // true/false/null
    otherPreviousConditions: "", // text input for other conditions

    // Question 4: Last 12 Months
    hadMalariaSyphilisTuberculosis: false,
    hadBloodTransfusion: false,
    hadVaccination: false,
    last12MonthsNone: false,

    // Question 5: Last 6 Months
    hadTyphoidSepsis: false,
    unexplainedWeightLoss: false,
    persistentLymphNodes: false,
    invasiveMedicalProcedures: false,
    tattoosPiercings: false,
    drugUse: false,
    bloodExposure: false,
    livedWithHepatitisB: false,
    sexualContactWithInfected: false,
    sameSexContact: false,
    last6MonthsNone: false,

    // Question 6: Last 1 Month
    hadUrinaryInfection: false,
    visitedEpidemicArea: false,
    last1MonthNone: false,

    // Question 7: Last 14 Days
    hadFluSymptoms: false,
    last14DaysNone: false,
    hasOtherSymptoms: false, // checkbox for "Khác (cụ thể)"
    otherSymptoms: "", // text input for other symptoms

    // Question 8: Last 7 Days
    tookAntibiotics: false,
    last7DaysNone: false,
    hasOtherMedications: false, // checkbox for "Khác (cụ thể)"
    otherMedications: "", // text input for other medications

    // Question 9: Women Only
    isPregnantOrNursing: false,
    hadPregnancyTermination: false,
    womenQuestionsNone: false,
  });

  // State đặt lịch hẹn
  const [appointmentData, setAppointmentData] = useState({
    preferredDate: "",
    timeSlot: "", // morning (7-11) or afternoon (13-17)
    location: null,
  });

  // Load thông tin người dùng khi component mount
  useEffect(() => {
    // Clear cache first to ensure fresh data
    if (currentUser?.id) {
      clearCache(currentUser.id);
    }
    loadUserProfile();
  }, [currentUser]);

  // Reload user profile when returning to the page (to get fresh data after profile updates)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page became visible, clear cache and reload user profile to get fresh data
        if (currentUser?.id) {
          clearCache(currentUser.id);
        }
        loadUserProfile();
        // Trigger address refresh to recalculate distance
        setAddressRefreshTrigger(prev => prev + 1);
      }
    };

    const handleFocus = () => {
      // Window gained focus, clear cache and reload user profile to get fresh data
      if (currentUser?.id) {
        clearCache(currentUser.id);
      }
      loadUserProfile();
      // Trigger address refresh to recalculate distance
      setAddressRefreshTrigger(prev => prev + 1);
    };

    const handleStorageChange = (e) => {
      // localStorage changed, reload user profile if memberInfo was updated
      if (e.key === 'memberInfo' || e.key === null) {
        // Clear cache first to ensure fresh data
        if (currentUser?.id) {
          clearCache(currentUser.id);
        }
        loadUserProfile();
        // Trigger address refresh to recalculate distance
        setAddressRefreshTrigger(prev => prev + 1);
      }
    };

    const handleCustomProfileUpdate = () => {
      // Custom event for profile updates
      // Clear cache first to ensure fresh data
      if (currentUser?.id) {
        clearCache(currentUser.id);
      }
      loadUserProfile();
      // Trigger address refresh to recalculate distance
      setAddressRefreshTrigger(prev => prev + 1);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('profileUpdated', handleCustomProfileUpdate);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('profileUpdated', handleCustomProfileUpdate);
    };
  }, []);

  // Tính khoảng cách khi location thay đổi
  useEffect(() => {
    if (personalInfo.location) {
      const distance = GeolibService.getDistanceToHospital(
        personalInfo.location
      );
      setDistanceInfo({
        distance,
        formattedDistance: GeolibService.formatDistance(distance),
        travelTime: "",
      });
    }
  }, [personalInfo.location]);

  // Auto-fill blood type từ personal info
  useEffect(() => {
    if (personalInfo.bloodType && !healthSurvey.bloodType) {
      setHealthSurvey((prev) => ({
        ...prev,
        bloodType: personalInfo.bloodType,
      }));
    }
  }, [personalInfo.bloodType, healthSurvey.bloodType]);

  // Auto-fill previous donation history
  useEffect(() => {
    const fetchPreviousDonations = async () => {
      if (!currentUser?.id || healthSurvey.hasDonatedBefore !== null) {
        return; // Skip if no user ID or already set
      }

      try {
        // Get user's previous appointments
        const appointments = await bloodDonationService.getAppointmentsByUser(currentUser.id);

        if (appointments && appointments.length > 0) {
          // Find the most recent completed donation (process >= 2 and status = true)
          const completedDonations = appointments.filter(appointment =>
            appointment.process >= 2 &&
            appointment.status === true &&
            appointment.donationDate
          );

          if (completedDonations.length > 0) {
            // Sort by donation date to get the most recent
            completedDonations.sort((a, b) =>
              new Date(b.donationDate) - new Date(a.donationDate)
            );

            const mostRecentDonation = completedDonations[0];
            const donationDate = dayjs(mostRecentDonation.donationDate).format("YYYY-MM-DD");

            // Auto-fill the health survey
            setHealthSurvey((prev) => ({
              ...prev,
              hasDonatedBefore: true,
              lastDonationDate: donationDate,
            }));

            // Set flag to indicate user has system donation history
            setHasSystemDonationHistory(true);

            console.log("✅ Auto-filled previous donation:", {
              date: donationDate,
              appointmentId: mostRecentDonation.appointmentId
            });
          }
        }
      } catch (error) {
        console.error("❌ Error fetching previous donations:", error);
        // Don't set any values on error - let user fill manually
      }
    };

    fetchPreviousDonations();
  }, [currentUser?.id, healthSurvey.hasDonatedBefore]);

  const loadUserProfile = async () => {
    try {
      // Gọi API để lấy thông tin chi tiết từ database với Context (bao gồm weight, height)
      // Ưu tiên dữ liệu từ API để đảm bảo luôn có dữ liệu mới nhất
      let apiUserInfo = {};
      try {
        if (currentUser?.id) {
          const result = await getUserData(currentUser.id);
          apiUserInfo = result.data || {};
        }
      } catch (apiError) {
        // Silent fail - use fallback data
      }

      // Lấy thông tin từ localStorage và currentUser làm fallback
      const storedMemberInfo = JSON.parse(
        localStorage.getItem("memberInfo") || "{}"
      );
      const userProfile = currentUser?.profile || {};

      // Tạo địa chỉ đầy đủ từ các thành phần - ưu tiên API data
      const baseAddress = apiUserInfo.address || storedMemberInfo.address || userProfile.address;
      const wardName = apiUserInfo.ward || storedMemberInfo.wardName || userProfile.wardName;
      const districtName = apiUserInfo.district || storedMemberInfo.districtName || userProfile.districtName;
      const provinceName = apiUserInfo.city || storedMemberInfo.provinceName || userProfile.provinceName;

      // Kiểm tra xem baseAddress đã chứa thông tin địa chỉ đầy đủ chưa
      const isFullAddressInBase = baseAddress && (
        (wardName && baseAddress.includes(wardName)) ||
        (districtName && baseAddress.includes(districtName)) ||
        (provinceName && baseAddress.includes(provinceName))
      );

      let fullAddress;
      if (isFullAddressInBase) {
        // Nếu baseAddress đã chứa thông tin đầy đủ, chỉ sử dụng nó
        fullAddress = baseAddress;
      } else {
        // Nếu chưa, kết hợp các thành phần
        fullAddress = [baseAddress, wardName, districtName, provinceName]
          .filter(Boolean)
          .join(", ");
      }

      // Cập nhật personalInfo với thông tin thực từ hồ sơ - ưu tiên API data
      const bloodGroup =
        apiUserInfo.bloodGroup ||
        storedMemberInfo.bloodGroup ||
        userProfile.bloodGroup ||
        "";
      const rhType =
        apiUserInfo.rhType ||
        storedMemberInfo.rhType ||
        userProfile.rhType ||
        "";
      let bloodType = "";
      if (bloodGroup && rhType) {
        // Chuẩn hóa ký hiệu Rh
        const rhSymbol =
          rhType === "Rh+" || rhType === "+"
            ? "+"
            : rhType === "Rh-" || rhType === "-"
              ? "-"
              : rhType;
        bloodType = `${bloodGroup}${rhSymbol}`;
      }

      console.log("🔄 loadUserProfile - Setting personalInfo with:", {
        apiUserInfo,
        storedMemberInfo,
        userProfile,
        fullAddress,
        apiAddress: apiUserInfo.address
      });

      setPersonalInfo((prev) => ({
        ...prev,
        fullName: getUserName(),
        email:
          apiUserInfo.email ||
          storedMemberInfo.email ||
          userProfile.email ||
          currentUser?.email ||
          "",
        phone:
          apiUserInfo.phone ||
          storedMemberInfo.phone ||
          userProfile.phone ||
          currentUser?.phone ||
          "",
        dateOfBirth: apiUserInfo.dateOfBirth
          ? apiUserInfo.dateOfBirth.split("T")[0] // Chỉ lấy phần ngày
          : storedMemberInfo.dateOfBirth
            ? storedMemberInfo.dateOfBirth.split("T")[0]
            : userProfile.dateOfBirth
              ? userProfile.dateOfBirth.split("T")[0]
              : "",
        gender: apiUserInfo.gender || storedMemberInfo.gender || userProfile.gender || "",
        address: {
          // Include the full address from API (priority field for geocoding)
          address: apiUserInfo.address || storedMemberInfo.address || "",
          houseNumber: apiUserInfo.houseNumber || storedMemberInfo.houseNumber || "",
          street: apiUserInfo.street || storedMemberInfo.street || "",
          province: apiUserInfo.province || storedMemberInfo.province || "",
          district: apiUserInfo.district || storedMemberInfo.district || "",
          ward: apiUserInfo.wardId || storedMemberInfo.ward || "",
          provinceName: provinceName,
          districtName: districtName,
          wardName: wardName,
          fullAddress: fullAddress,
          coordinates: { lat: null, lng: null },
          distance: null,
          travelTime: null,
          formattedAddress: fullAddress,
        },
        bloodType: bloodType,
      }));

      // Set luôn cho healthSurvey với weight và height từ database
      setHealthSurvey((prev) => ({
        ...prev,
        bloodType: bloodType,
        weight: apiUserInfo.weight || prev.weight || "",
        height: apiUserInfo.height || prev.height || "",
      }));

      // Nếu có địa chỉ đầy đủ, thực hiện geocoding
      if (fullAddress && fullAddress.length > 10) {
        try {
          // Import NominatimService để geocoding
          const NominatimService = (
            await import("../services/nominatimService")
          ).default;
          const geocodeResult = await NominatimService.geocodeAddress(
            fullAddress
          );

          if (geocodeResult) {
            setPersonalInfo((prev) => ({
              ...prev,
              address: {
                ...prev.address,
                coordinates: {
                  lat: geocodeResult.lat,
                  lng: geocodeResult.lng,
                },
                formattedAddress: geocodeResult.address || fullAddress,
              },
            }));

            // Tính khoảng cách
            const distance = GeolibService.getDistanceToHospital({
              lat: geocodeResult.lat,
              lng: geocodeResult.lng,
            });

            setDistanceInfo({
              distance,
              formattedDistance: GeolibService.formatDistance(distance),
              travelTime: "",
              priority: GeolibService.getDistancePriority(distance),
            });
          }
        } catch (geocodeError) {
          // Silent fail - geocoding is not critical
        }
      }

      // Trigger address refresh to ensure distance calculation
      setAddressRefreshTrigger(prev => prev + 1);
    } catch (error) {
      // Silent fail - profile loading is not critical
    }
  };

  const handlePersonalInfoChange = (field, value) => {
    setPersonalInfo((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Calculate distance when location changes
    if (field === "location" && value) {
      const distance = GeolibService.getDistanceToHospital(value);
      setDistanceInfo({
        distance,
        formattedDistance: GeolibService.formatDistance(distance),
        travelTime: "",
        priority: GeolibService.getDistancePriority(distance),
      });
    }
  };

  const resetForm = () => {
    setStep(1);
    setRegistrationResult(null);
    setHasSystemDonationHistory(false);
    setHealthSurvey({
      weight: "",
      height: "",
      bloodPressure: "",
      heartRate: "",
      bloodType: personalInfo.bloodType || "",
      hasDonatedBefore: null,
      hasCurrentMedicalConditions: null,
      hasPreviousSeriousConditions: null,
      otherPreviousConditions: "",
      hadMalariaSyphilisTuberculosis: false,
      hadBloodTransfusion: false,
      hadVaccination: false,
      last12MonthsNone: false,
      hadTyphoidSepsis: false,
      unexplainedWeightLoss: false,
      persistentLymphNodes: false,
      invasiveMedicalProcedures: false,
      tattoosPiercings: false,
      drugUse: false,
      bloodExposure: false,
      livedWithHepatitisB: false,
      sexualContactWithInfected: false,
      sameSexContact: false,
      last6MonthsNone: false,
      hadUrinaryInfection: false,
      visitedEpidemicArea: false,
      last1MonthNone: false,
      hadFluSymptoms: false,
      last14DaysNone: false,
      otherSymptoms: "",
      tookAntibiotics: false,
      last7DaysNone: false,
      otherMedications: "",
      isPregnantOrNursing: false,
      hadPregnancyTermination: false,
      womenQuestionsNone: false,
    });
    setAppointmentData({
      preferredDate: "",
      timeSlot: "",
      location: null,
    });
  };

  return {
    // State
    step,
    loading,
    registrationResult,
    distanceInfo,
    personalInfo,
    healthSurvey,
    appointmentData,
    currentUser,
    navigate,
    hasSystemDonationHistory,
    addressRefreshTrigger,

    // Setters
    setStep,
    setLoading,
    setRegistrationResult,
    setPersonalInfo,
    setHealthSurvey,
    setAppointmentData,

    // Methods
    handlePersonalInfoChange,
    resetForm,
  };
};
