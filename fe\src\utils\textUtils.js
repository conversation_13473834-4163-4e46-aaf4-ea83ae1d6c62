export function getShortContent(content, keyword = "", maxLength = 120) {
  if (!content) return "";

  // Helper function to strip HTML tags for length calculation and keyword search
  const stripHtml = (html) => {
    return html.replace(/<[^>]*>/g, "");
  };

  // For HTML content, we need to be careful when truncating
  if (content.includes("<") && content.includes(">")) {
    const textContent = stripHtml(content);

    if (!keyword.trim()) {
      if (textContent.length <= maxLength) {
        return content;
      }
      // For HTML content, return the original HTML but truncated smartly
      // Find a safe place to cut (after a complete tag or at word boundary)
      const words = textContent.split(" ");
      let currentLength = 0;
      let wordCount = 0;

      for (let word of words) {
        if (currentLength + word.length + 1 > maxLength) break;
        currentLength += word.length + 1;
        wordCount++;
      }

      // Try to find the corresponding position in HTML
      const truncatedText = words.slice(0, wordCount).join(" ");
      if (truncatedText.length < textContent.length) {
        return (
          stripHtml(
            content.substring(
              0,
              content.indexOf(truncatedText) + truncatedText.length
            )
          ) + "..."
        );
      }
      return content;
    }

    // Keyword search in HTML content
    const lowerTextContent = textContent.toLowerCase();
    const lowerKeyword = keyword.toLowerCase();
    const idx = lowerTextContent.indexOf(lowerKeyword);

    if (idx === -1) {
      return textContent.length > maxLength
        ? stripHtml(content.slice(0, maxLength)) + "..."
        : content;
    }

    const start = Math.max(0, idx - 40);
    const end = Math.min(textContent.length, idx + lowerKeyword.length + 40);
    let snippet =
      (start > 0 ? "..." : "") +
      textContent.slice(start, end) +
      (end < textContent.length ? "..." : "");
    return snippet;
  }

  // For plain text content, use the original logic
  if (!keyword.trim())
    return content.length > maxLength
      ? content.slice(0, maxLength) + "..."
      : content;

  const lowerContent = content.toLowerCase();
  const lowerKeyword = keyword.toLowerCase();
  const idx = lowerContent.indexOf(lowerKeyword);
  if (idx === -1)
    return content.length > maxLength
      ? content.slice(0, maxLength) + "..."
      : content;
  const start = Math.max(0, idx - 40);
  const end = Math.min(content.length, idx + lowerKeyword.length + 40);
  let snippet =
    (start > 0 ? "..." : "") +
    content.slice(start, end) +
    (end < content.length ? "..." : "");
  return snippet;
}

export function getHighlightedSnippet(content, keyword, maxLength = 120) {
  // Có thể dùng cho highlight UI, trả về đoạn text chứa keyword
  return getShortContent(content, keyword, maxLength);
}
