import React from 'react';
import { Modal, Button, Result } from 'antd';
import { ExclamationCircleOutlined, PhoneOutlined, MailOutlined } from '@ant-design/icons';

const SuspendedAccountModal = ({ visible, onClose, userEmail }) => {
  return (
    <Modal
      title={null}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={500}
      centered
      closable={false}
      maskClosable={false}
    >
      <Result
        icon={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
        title="Tài khoản bị đình chỉ hoạt động"
        subTitle={
          <div style={{ textAlign: 'left', marginTop: 20 }}>
            <div style={{
              background: '#fff2f0',
              border: '1px solid #ffccc7',
              borderRadius: 8,
              padding: 16,
              marginBottom: 20
            }}>
              <p style={{ fontSize: '16px', marginBottom: 8, color: '#cf1322' }}>
                <strong>Tr<PERSON><PERSON> thái tài khoản: ĐÌNH CHỈ HOẠT ĐỘNG</strong>
              </p>
              <p style={{ fontSize: '14px', marginBottom: 0 }}>
                Tà<PERSON> kho<PERSON>n <strong>{userEmail}</strong> hiện có thể đã bị đình chỉ hoạt động và không thể đăng nhập.
              </p>
            </div>

            <p style={{ marginBottom: 12 }}>
              <strong>Nguyên nhân có thể:</strong>
            </p>
            <ul style={{ paddingLeft: 20, marginBottom: 16 }}>
              <li>Vi phạm quy định sử dụng hệ thống</li>
              <li>Hoạt động bất thường được phát hiện</li>
              <li>Quyết định đình chỉ từ quản trị viên</li>
              <li>Tài khoản được đánh dấu không hoạt động</li>
            </ul>



            <p style={{ marginBottom: 12 }}>
              <strong>Liên hệ hỗ trợ:</strong>
            </p>
            <ul style={{ paddingLeft: 20, marginBottom: 16 }}>
              <li>Hotline: <a href="tel:***********">***********</a></li>
              <li>Email: <a href="mailto:<EMAIL>"><EMAIL></a></li>
            </ul>
          </div>
        }
        extra={
          <Button type="primary" size="large" onClick={onClose}>
            Đã hiểu
          </Button>
        }
      />
    </Modal>
  );
};

export default SuspendedAccountModal;
