import React, { createContext, useContext, useState, useCallback } from "react";
import userInfoService from "../services/userInfoService";
import { getDepartmentFromUser } from "../utils/departmentUtils";

/**
 * Context for managing user data with caching
 * Prevents duplicate API calls and provides centralized user data management
 */

const UserDataContext = createContext();

export const useUserData = () => {
  const context = useContext(UserDataContext);
  if (!context) {
    throw new Error("useUserData must be used within a UserDataProvider");
  }
  return context;
};

export const UserDataProvider = ({ children }) => {
  // Cache for user data - key: userId, value: { data, loading, error, timestamp }
  const [userCache, setUserCache] = useState(new Map());
  
  // Cache expiry time (5 minutes)
  const CACHE_EXPIRY = 5 * 60 * 1000;

  /**
   * Check if cached data is still valid
   */
  const isCacheValid = (cacheEntry) => {
    if (!cacheEntry) return false;
    return Date.now() - cacheEntry.timestamp < CACHE_EXPIRY;
  };

  /**
   * Get user data with caching
   * @param {string|number} userId - User ID
   * @returns {Object} { data, loading, error, department }
   */
  const getUserData = useCallback(async (userId) => {
    if (!userId || userId === 0) {
      return { data: null, loading: false, error: "Invalid user ID", department: null };
    }

    const userIdStr = String(userId);
    const cached = userCache.get(userIdStr);

    // Return cached data if valid
    if (cached && isCacheValid(cached)) {
      return {
        data: cached.data,
        loading: false,
        error: cached.error,
        department: cached.department
      };
    }

    // If currently loading, return loading state
    if (cached && cached.loading) {
      return {
        data: cached.data,
        loading: true,
        error: cached.error,
        department: cached.department
      };
    }

    // Set loading state
    setUserCache(prev => new Map(prev).set(userIdStr, {
      data: cached?.data || null,
      loading: true,
      error: null,
      department: cached?.department || null,
      timestamp: Date.now()
    }));

    try {
      const userData = await userInfoService.getUserInfo(userId);
      
      if (userData) {
        const department = getDepartmentFromUser(userData);
        
        const cacheEntry = {
          data: userData,
          loading: false,
          error: null,
          department,
          timestamp: Date.now()
        };

        setUserCache(prev => new Map(prev).set(userIdStr, cacheEntry));

        return {
          data: userData,
          loading: false,
          error: null,
          department
        };
      } else {
        const cacheEntry = {
          data: null,
          loading: false,
          error: "User not found",
          department: null,
          timestamp: Date.now()
        };

        setUserCache(prev => new Map(prev).set(userIdStr, cacheEntry));

        return {
          data: null,
          loading: false,
          error: "User not found",
          department: null
        };
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
      
      const cacheEntry = {
        data: cached?.data || null,
        loading: false,
        error: error.message || "Failed to fetch user data",
        department: cached?.department || null,
        timestamp: Date.now()
      };

      setUserCache(prev => new Map(prev).set(userIdStr, cacheEntry));

      return {
        data: cached?.data || null,
        loading: false,
        error: error.message || "Failed to fetch user data",
        department: cached?.department || null
      };
    }
  }, [userCache]);

  /**
   * Get multiple users data
   * @param {Array} userIds - Array of user IDs
   * @returns {Promise<Map>} Map of userId -> userData
   */
  const getMultipleUsersData = useCallback(async (userIds) => {
    const results = new Map();
    
    // Process all user IDs in parallel
    const promises = userIds.map(async (userId) => {
      const userData = await getUserData(userId);
      return { userId: String(userId), userData };
    });

    const responses = await Promise.all(promises);
    
    responses.forEach(({ userId, userData }) => {
      results.set(userId, userData);
    });

    return results;
  }, [getUserData]);

  /**
   * Clear cache for specific user or all users
   * @param {string|number} userId - Optional user ID to clear, if not provided clears all
   */
  const clearCache = useCallback((userId = null) => {
    if (userId) {
      setUserCache(prev => {
        const newCache = new Map(prev);
        newCache.delete(String(userId));
        return newCache;
      });
    } else {
      setUserCache(new Map());
    }
  }, []);

  /**
   * Preload user data (useful for anticipated usage)
   * @param {string|number} userId - User ID to preload
   */
  const preloadUserData = useCallback(async (userId) => {
    await getUserData(userId);
  }, [getUserData]);

  const value = {
    getUserData,
    getMultipleUsersData,
    clearCache,
    preloadUserData,
    // Expose cache stats for debugging
    getCacheStats: () => ({
      size: userCache.size,
      entries: Array.from(userCache.entries()).map(([id, entry]) => ({
        id,
        hasData: !!entry.data,
        loading: entry.loading,
        error: entry.error,
        department: entry.department,
        age: Date.now() - entry.timestamp
      }))
    })
  };

  return (
    <UserDataContext.Provider value={value}>
      {children}
    </UserDataContext.Provider>
  );
};

export default UserDataContext;
