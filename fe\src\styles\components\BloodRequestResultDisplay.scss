// BloodRequestResultDisplay.scss
// Style đồng bộ với BloodDonationFormPage ResultDisplay

.blood-request-form-page {
  .registration-content {
    padding: 40px 20px;
    min-height: calc(100vh - 200px);
    display: flex;
    align-items: center;
    justify-content: center;

    .result-section {
      width: 100%;
      max-width: 800px;

      .result-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        padding: 48px 32px;
        text-align: center;
        transition: all 0.3s ease;
        border: 2px solid transparent;

        .result-icon {
          font-size: 4rem;
          margin-bottom: 24px;
          display: inline-block;
          padding: 24px;
          border-radius: 50%;
          width: 120px;
          height: 120px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 24px auto;
        }

        .result-content {
          h2 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 16px;
            text-transform: uppercase;
            letter-spacing: 1px;
          }

          p {
            font-size: 1.125rem;
            color: #6b7280;
            margin-bottom: 32px;
            line-height: 1.6;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
          }
        }

        // Success state styling
        &.success {
          border-color: #10b981;
          background: linear-gradient(135deg,
              rgba(16, 185, 129, 0.05) 0%,
              rgba(16, 185, 129, 0.1) 100%);

          .result-icon {
            color: #10b981;
            background: rgba(16, 185, 129, 0.1);
            animation: bounce 0.6s ease-in-out;
          }

          h2 {
            color: #059669;
          }

          .request-summary {
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
            border: 2px solid #bbf7d0;
            border-radius: 16px;
            padding: 32px;
            margin: 32px 0;
            text-align: left;

            .summary-title {
              display: flex;
              align-items: center;
              font-weight: 700;
              color: #059669;
              margin-bottom: 24px;
              font-size: 1.25rem;

              .me-2 {
                margin-right: 8px;
              }
            }

            .request-details {
              .detail-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 0;
                border-bottom: 1px solid #d1fae5;

                &:last-child {
                  border-bottom: none;
                }

                strong {
                  font-weight: 600;
                  color: #374151;
                }

                .value {
                  font-weight: 500;
                  color: #1f2937;

                  &.request-id {
                    color: #059669;
                    font-weight: 700;
                    font-size: 1.1rem;
                  }

                  &.blood-type {
                    background: #dc2626;
                    color: white;
                    padding: 6px 16px;
                    border-radius: 20px;
                    font-weight: 600;
                    font-size: 0.875rem;
                  }
                }
              }
            }
          }
        }

        // Error state styling
        &.error {
          border-color: #ef4444;
          background: linear-gradient(135deg,
              rgba(239, 68, 68, 0.05) 0%,
              rgba(239, 68, 68, 0.1) 100%);

          .result-icon {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
            animation: shake 0.5s ease-in-out;
          }

          h2 {
            color: #dc2626;
          }
        }

        .result-actions {
          display: flex;
          justify-content: center;
          gap: 16px;
          margin-top: 32px;
          padding-top: 32px;
          border-top: 1px solid #e5e7eb;

          .btn {
            min-width: 180px;
            padding: 14px 28px;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;

            .me-2 {
              margin-right: 0 !important;
            }

            &.btn-primary {
              background: linear-gradient(135deg, #10b981 0%, #059669 100%);
              border: none;
              color: white;

              &:hover {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
                transform: translateY(-2px);
                box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
              }
            }

            &.btn-outline-primary {
              border: 2px solid #3b82f6;
              color: #3b82f6;
              background: transparent;

              &:hover {
                background: #3b82f6;
                color: white;
                transform: translateY(-2px);
                box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
              }
            }
          }
        }
      }
    }
  }
}

// Animations
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -15px, 0);
  }
  70% {
    transform: translate3d(0, -7px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

// Responsive design
@media (max-width: 768px) {
  .blood-request-form-page {
    .registration-content {
      padding: 20px 16px;

      .result-section {
        .result-card {
          padding: 32px 24px;

          .result-icon {
            font-size: 3rem;
            width: 100px;
            height: 100px;
            padding: 20px;
          }

          .result-content {
            h2 {
              font-size: 1.5rem;
            }

            p {
              font-size: 1rem;
            }
          }

          .request-summary {
            padding: 24px;
            margin: 24px 0;

            .summary-title {
              font-size: 1.125rem;
            }

            .request-details {
              .detail-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
                text-align: left;

                .value {
                  &.blood-type {
                    align-self: flex-end;
                  }
                }
              }
            }
          }

          .result-actions {
            flex-direction: column;
            gap: 12px;

            .btn {
              width: 100%;
              min-width: auto;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .blood-request-form-page {
    .registration-content {
      .result-section {
        .result-card {
          padding: 24px 16px;

          .result-icon {
            font-size: 2.5rem;
            width: 80px;
            height: 80px;
            padding: 16px;
          }

          .result-content {
            h2 {
              font-size: 1.25rem;
            }
          }
        }
      }
    }
  }
}
