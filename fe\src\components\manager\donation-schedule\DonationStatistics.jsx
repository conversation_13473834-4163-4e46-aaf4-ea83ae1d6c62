import { Row, Col, Card, Statistic } from "antd";

/**
 * Statistics component for donation schedule
 */
const DonationStatistics = ({ donations = [] }) => {
  // Calculate statistics
  const totalDonations = donations.length;
  
  const todayDonations = donations.filter((d) => {
    const today = new Date().toDateString();
    const appointmentDate = new Date(d.appointmentDate).toDateString();
    return appointmentDate === today;
  }).length;

  const upcomingDonations = donations.filter((d) => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const appointmentDate = new Date(d.appointmentDate);
    return appointmentDate >= tomorrow;
  }).length;

  const completedDonations = donations.filter((d) => 
    d.status === "completed" || d.process === 4
  ).length;

  return (
    <Row gutter={16} style={{ marginBottom: 24 }} className="stats-section">
      <Col xs={24} sm={12} md={6}>
        <Card className="total-card">
          <Statistic
            title="Tổng lịch hẹn"
            value={totalDonations}
            valueStyle={{ color: "#1677ff", fontWeight: 600 }}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card className="today">
          <Statistic
            title="Hôm nay"
            value={todayDonations}
            valueStyle={{ color: "#1677ff", fontWeight: 600 }}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card className="upcoming">
          <Statistic
            title="Sắp tới"
            value={upcomingDonations}
            valueStyle={{ color: "#52c41a", fontWeight: 600 }}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card className="completed">
          <Statistic
            title="Đã hoàn thành"
            value={completedDonations}
            valueStyle={{ color: "#52c41a", fontWeight: 600 }}
          />
        </Card>
      </Col>
    </Row>
  );
};

export default DonationStatistics;
