@use "../base/variables" as vars;
@use "../base/common-design-system" as common;
@use "sass:color";

.activity-history-page {
  min-height: 100vh;
  background: vars.$background-main;

  .activity-content {
    padding: 2rem;
    margin-top: 20px;

    .page-header {
      margin-bottom: vars.$spacing-lg;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      padding: 2rem 2.5rem;
      border-radius: vars.$border-radius-lg;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
      border: 1px solid rgba(255, 255, 255, 0.8);
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
        border-color: rgba(102, 126, 234, 0.2);
      }

      // Decorative background elements
      &::before {
        content: "";
        position: absolute;
        top: -50%;
        right: -20%;
        width: 200px;
        height: 200px;
        background: linear-gradient(45deg,
            rgba(25, 118, 210, 0.05),
            rgba(33, 150, 243, 0.08));
        border-radius: 50%;
        z-index: 0;
      }

      &::after {
        content: "";
        position: absolute;
        bottom: -30%;
        left: -10%;
        width: 150px;
        height: 150px;
        background: linear-gradient(45deg,
            rgba(76, 175, 80, 0.05),
            rgba(139, 195, 74, 0.08));
        border-radius: 50%;
        z-index: 0;
      }

      .header-content {
        position: relative;
        z-index: 1;
        flex: 1;

        h1 {
          color: vars.$text-primary;
          margin-bottom: vars.$spacing-sm;
          font-size: 2.5rem;
          font-weight: 800;
          font-family: vars.$font-primary;
          background: linear-gradient(135deg,
              vars.$text-primary 0%,
              vars.$primary-color 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          line-height: 1.2;
          letter-spacing: -0.5px;

          // Fallback for browsers that don't support background-clip
          @supports not (-webkit-background-clip: text) {
            color: vars.$text-primary;
          }
        }

        p {
          color: vars.$text-secondary;
          font-size: 1.1rem;
          margin: 0;
          font-weight: 500;
          line-height: 1.5;
          max-width: 500px;

          // Add icon before text
          &::before {
            content: "📊";
            margin-right: 8px;
            font-size: 1.2rem;
          }
        }

        // Add breadcrumb-style navigation hint
        .breadcrumb-hint {
          margin-top: vars.$spacing-xs;
          font-size: 0.85rem;
          color: vars.$text-muted;
          font-weight: 400;

          .breadcrumb-item {
            display: inline-block;

            &::after {
              content: " › ";
              margin: 0 4px;
              color: vars.$border-light;
            }

            &:last-child::after {
              display: none;
            }

            &.active {
              color: vars.$primary-color;
              font-weight: 600;
            }
          }
        }
      }

      .btn {
        position: relative;
        z-index: 1;
        padding: 12px 24px;
        border: none;
        border-radius: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        font-size: 0.95rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

        &.btn-primary {
          background: linear-gradient(135deg,
              vars.$primary-color 0%,
              vars.$primary-hover 100%);
          color: vars.$white;
          border: 2px solid transparent;

          // Add subtle inner glow
          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.2) 0%,
                transparent 50%);
            border-radius: 10px;
            pointer-events: none;
          }

          &:hover:not(:disabled) {
            background: linear-gradient(135deg,
                vars.$primary-hover 0%,
                color.adjust(vars.$primary-hover, $lightness: -10%) 100%);
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 8px 25px rgba(25, 118, 210, 0.4);
          }

          &:active {
            transform: translateY(-1px) scale(1.01);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
          }
        }

        &.btn-danger {
          background: vars.$error-color;
          color: vars.$white;

          &:hover:not(:disabled) {
            background: color.adjust(vars.$error-color, $lightness: -10%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }

        &.btn-info {
          background: vars.$info-color;
          color: vars.$white;

          &:hover:not(:disabled) {
            background: color.adjust(vars.$info-color, $lightness: -10%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }

        &.btn-success {
          background: vars.$success-color;
          color: vars.$white;

          &:hover:not(:disabled) {
            background: #388e3c;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }
    }

    .stats-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;

      .stat-card {
        background: vars.$white;
        padding: vars.$spacing-md;
        border-radius: vars.$border-radius-lg;
        box-shadow: 0 4px 12px vars.$shadow-color;
        border: 1px solid vars.$border-light;
        display: flex;
        align-items: center;
        gap: vars.$spacing-base;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
          font-size: 2.5rem;
          flex-shrink: 0;
        }

        .stat-content {
          .stat-number {
            font-size: vars.$font-size-2xl;
            font-weight: vars.$font-weight-extrabold;
            margin: 0 0 vars.$spacing-xxs 0;
            color: vars.$text-color;
          }

          .stat-label {
            color: vars.$text-secondary;
            font-size: vars.$font-size-small;
            font-weight: vars.$font-weight-semibold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }

        &.total {
          border-left: 4px solid vars.$primary-color;

          .stat-icon {
            color: vars.$primary-color;
          }
        }

        &.donations {
          border-left: 4px solid vars.$secondary-color;

          .stat-icon {
            color: vars.$secondary-color;
          }
        }

        &.requests {
          border-left: 4px solid vars.$info-color;

          .stat-icon {
            color: vars.$info-color;
          }
        }

        &.completed {
          border-left: 4px solid vars.$success-color;

          .stat-icon {
            color: vars.$success-color;
          }
        }
      }
    }

    .filters-section {
      background: vars.$white;
      padding: vars.$spacing-md;
      border-radius: vars.$border-radius-lg;
      box-shadow: 0 4px 12px vars.$shadow-color;
      border: 1px solid vars.$border-light;
      margin-bottom: vars.$spacing-lg;
      display: flex;
      gap: 2rem;
      align-items: center;
      flex-wrap: wrap;

      .filter-group {
        display: flex;
        align-items: center;
        gap: 0.75rem;

        label {
          font-weight: vars.$font-weight-bold;
          color: vars.$text-color;
          font-size: vars.$font-size-small;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        select {
          padding: vars.$spacing-sm vars.$spacing-base;
          border: 2px solid vars.$border-light;
          border-radius: vars.$border-radius-base;
          font-size: vars.$font-size-base;
          background: vars.$white;
          transition: all 0.3s ease;
          font-weight: vars.$font-weight-medium;
          min-width: 150px;

          &:focus {
            outline: none;
            border-color: vars.$primary-color;
            box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.1);
          }
        }
      }
    }

    // CSS cho filter section card mới
    .filter-section-card {
      margin-bottom: vars.$spacing-lg;
      border-radius: vars.$border-radius-lg;
      box-shadow: 0 4px 12px vars.$shadow-color;
      border: 1px solid vars.$border-light;

      .filter-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, vars.$primary-color, vars.$secondary-color);
        border-radius: 50%;

        .filter-icon {
          color: vars.$white;
          font-size: 14px;
        }
      }

      .filter-label {
        color: vars.$text-color;
        font-size: vars.$font-size-base;
        margin: 0;
      }

      .filter-select {
        .ant-select-selector {
          border-radius: vars.$border-radius-base;
          border: 2px solid vars.$border-light;
          transition: all 0.3s ease;

          &:hover {
            border-color: vars.$primary-color;
          }
        }

        &.ant-select-focused .ant-select-selector {
          border-color: vars.$primary-color;
          box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.1);
        }
      }
    }

    .activities-section {
      .loading-state {
        text-align: center;
        padding: vars.$spacing-xxl;
        color: vars.$text-secondary;

        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 4px solid vars.$background-light;
          border-top: 4px solid vars.$primary-color;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto vars.$spacing-base;
        }

        p {
          font-size: 1.1rem;
          margin: 0;
        }
      }

      .empty-state {
        text-align: center;
        padding: vars.$spacing-xxl;
        color: vars.$text-secondary;

        .empty-icon {
          font-size: vars.$font-size-5xl;
          display: block;
          margin-bottom: vars.$spacing-md;
        }

        h3 {
          margin: 0 0 vars.$spacing-base 0;
          color: vars.$text-color;
          font-size: vars.$font-size-2xl;
        }

        p {
          margin: 0;
          font-size: vars.$font-size-lg;
          line-height: vars.$line-height-normal;
        }
      }

      .activities-list {
        display: flex;
        flex-direction: column;
        gap: vars.$spacing-lg;

        .activity-card {
          background: vars.$white;
          border-radius: vars.$border-radius-lg;
          box-shadow: 0 4px 12px vars.$shadow-color;
          border: 1px solid vars.$border-light;
          margin-bottom: vars.$spacing-md;
          transition: all 0.3s ease;
          overflow: hidden;

          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
          }

          // Styling for cancelled appointments
          &.cancelled {
            opacity: 0.6;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            position: relative;

            &::before {
              content: "❌ ĐÃ HỦY";
              position: absolute;
              top: 10px;
              right: 10px;
              background: rgba(220, 53, 69, 0.1);
              color: #dc3545;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 0.75rem;
              font-weight: bold;
              z-index: 10;
            }

            &:hover {
              transform: none;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .activity-header {
              background: linear-gradient(135deg, #f8f9fa, #e9ecef);
              opacity: 0.8;
            }

            .activity-title {
              text-decoration: line-through;
              color: #6c757d !important;
            }

            .status-badge {
              background-color: #6c757d !important;
            }

            .activity-actions .btn {
              opacity: 0.5;
              pointer-events: none;
            }
          }

          &.donation {
            border-left: 4px solid vars.$secondary-color;
          }

          &.request {
            border-left: 4px solid vars.$info-color;
          }

          .activity-header {
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            flex-wrap: wrap;
            gap: 1rem;

            .activity-info {
              flex: 1;
              min-width: 250px;

              .activity-title {
                font-size: 1.3rem;
                font-weight: 700;
                color: #495057;
                margin-bottom: 0.5rem;
              }

              .activity-date {
                color: #6c757d;
                font-size: 0.9rem;
                font-weight: 500;

                .completed-date {
                  color: #28a745;
                  font-weight: 700;
                  margin-left: 0.5rem;
                }
              }
            }

            .activity-status {
              // Sử dụng StatusBadge component thống nhất, xóa custom style
            }
          }

          .activity-details {
            padding: 1.5rem;

            .detail-section {
              margin-bottom: 1.5rem;

              &:last-child {
                margin-bottom: 0;
              }

              h4 {
                margin: 0 0 0.75rem 0;
                color: vars.$text-color;
                font-size: 1rem;
                font-weight: 700;
              }

              .blood-info,
              .appointment-info,
              .patient-info {
                display: flex;
                flex-direction: column;
                gap: vars.$spacing-sm;

                .info-item {
                  display: flex;
                  align-items: center;
                  gap: vars.$spacing-sm;
                  padding: 8px 12px;
                  background: vars.$background-section;
                  border-radius: 8px;
                  border: 1px solid vars.$border-light;

                  .info-icon {
                    color: vars.$primary-color;
                    font-size: 14px;
                    flex-shrink: 0;
                  }

                  .info-label {
                    font-weight: vars.$font-weight-semibold;
                    color: vars.$text-secondary;
                    min-width: 80px;
                  }

                  .info-value {
                    color: vars.$text-color;
                    font-weight: vars.$font-weight-medium;
                    flex: 1;

                    &.blood-type {
                      font-weight: vars.$font-weight-bold;
                      color: vars.$secondary-color;
                    }
                  }
                }

                // New structured appointment info layout
                .appointment-time-section {
                  margin-bottom: vars.$spacing-md;

                  .appointment-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: vars.$spacing-sm;
                    margin-bottom: vars.$spacing-xs;
                    border-radius: vars.$border-radius-base;
                    background: vars.$background-section;
                    border-left: 3px solid vars.$border-light;
                    transition: all 0.3s ease;

                    &:hover {
                      background: vars.$white;
                      border-left-color: vars.$primary-color;
                      transform: translateX(2px);
                    }

                    &.highlight {
                      background: linear-gradient(135deg,
                          vars.$primary-light,
                          vars.$secondary-light );
                      border-left-color: vars.$primary-color;
                      font-weight: vars.$font-weight-semibold;
                    }

                    .appointment-label {
                      color: vars.$text-color;
                      font-weight: vars.$font-weight-medium;
                      font-size: vars.$font-size-small;
                    }

                    .appointment-value {
                      color: vars.$text-secondary;
                      font-weight: vars.$font-weight-semibold;
                      font-size: vars.$font-size-small;
                    }
                  }
                }

                .health-info-section,
                .donation-history-section {
                  margin-top: vars.$spacing-md;
                  padding: vars.$spacing-md;
                  background: vars.$background-section;
                  border-radius: vars.$border-radius-base;
                  border: 1px solid vars.$border-light;

                  h5 {
                    margin: 0 0 vars.$spacing-sm 0;
                    color: vars.$primary-color;
                    font-size: vars.$font-size-base;
                    font-weight: vars.$font-weight-semibold;
                  }
                }

                .health-info-grid,
                .donation-history-grid {
                  display: grid;
                  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                  gap: vars.$spacing-sm;

                  .health-info-item,
                  .history-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: vars.$spacing-xs vars.$spacing-sm;
                    background: vars.$white;
                    border-radius: vars.$border-radius-base;
                    border: 1px solid vars.$border-light;

                    .health-label,
                    .history-label {
                      color: vars.$text-color;
                      font-size: vars.$font-size-xs;
                      font-weight: vars.$font-weight-medium;
                    }

                    .health-value,
                    .history-value {
                      color: vars.$primary-color;
                      font-size: vars.$font-size-xs;
                      font-weight: vars.$font-weight-semibold;

                      &.yes {
                        color: vars.$success-color;
                      }

                      &.no {
                        color: vars.$text-secondary;
                      }
                    }
                  }
                }

                .blood-type-badge {
                  background: vars.$secondary-color;
                  color: vars.$white;
                  font-weight: vars.$font-weight-bold;
                  box-shadow: 0 2px 8px rgba(211, 47, 47, 0.3);
                }

                .quantity-info {
                  background: vars.$primary-color;
                  color: vars.$white;
                  font-weight: vars.$font-weight-bold;
                  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
                }

                .urgency-badge {
                  color: vars.$white;
                  font-weight: vars.$font-weight-bold;
                  font-size: vars.$font-size-xs;
                }
              }

              .notes {
                background: vars.$background-section;
                padding: 1rem;
                border-radius: 8px;
                color: vars.$text-color;
                font-style: italic;
                border-left: 4px solid vars.$primary-color;
              }

              .doctor-notes {
                background: linear-gradient(135deg,
                    vars.$primary-light 0%,
                    vars.$secondary-light 100%);
                padding: 1rem;
                border-radius: 8px;
                color: vars.$primary-color;
                font-weight: 500;
                border-left: 4px solid vars.$primary-color;
                position: relative;

                &::before {
                  content: "👨‍⚕️";
                  position: absolute;
                  top: 0.5rem;
                  right: 0.75rem;
                  font-size: 1.2rem;
                  opacity: 0.7;
                }
              }
            }
          }

          .activity-actions {
            padding: 1rem 1.5rem;
            background: vars.$background-section;
            border-top: 1px solid vars.$border-light;
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            flex-wrap: wrap;

            .btn {
              padding: 0.5rem 1rem;
              border: none;
              border-radius: 8px;
              font-weight: 600;
              cursor: pointer;
              transition: all 0.3s ease;
              text-decoration: none;
              font-size: 0.9rem;

              &.btn-info {
                background: linear-gradient(45deg,
                    vars.$info-color,
                    vars.$success-color );
                color: vars.$white;
                box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);

                &:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
                  text-decoration: none;
                  color: vars.$white;
                }
              }

              &.btn-success {
                background: linear-gradient(45deg,
                    vars.$success-color,
                    color.adjust(vars.$success-color, $lightness: 10%));
                color: vars.$white;
                box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);

                &:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
                  text-decoration: none;
                  color: vars.$white;
                }
              }
            }

            .cancelled-label {
              display: inline-flex;
              align-items: center;
              padding: 0.5rem 1rem;
              background: rgba(244, 67, 54, 0.1);
              color: vars.$error-color;
              border-radius: 8px;
              font-weight: 600;
              font-size: 0.9rem;
              border: 1px solid rgba(244, 67, 54, 0.2);
            }
          }
        }
      }
    }

    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: vars.$z-index-modal;

      .workflow-modal {
        background: vars.$white;
        border-radius: vars.$border-radius-lg;
        max-width: 800px;
        width: 95%;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 20px 40px vars.$shadow-color;

        .modal-header {
          padding: 1.5rem 2rem;
          background: linear-gradient(135deg,
              vars.$background-section,
              vars.$border-light );
          border-bottom: 1px solid vars.$border-light;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-radius: 20px 20px 0 0;

          h3 {
            margin: 0;
            color: vars.$primary-color;
            font-size: vars.$font-size-xl;
            font-weight: vars.$font-weight-bold;
          }

          .close-btn {
            background: linear-gradient(45deg,
                vars.$error-color,
                color.adjust(vars.$error-color, $lightness: -10%));
            border: none;
            color: vars.$white;
            font-size: 1.5rem;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;

            &:hover {
              transform: rotate(90deg) scale(1.1);
              box-shadow: 0 5px 15px rgba(244, 67, 54, 0.3);
            }
          }
        }

        .modal-body {
          padding: 2rem;

          .activity-summary {
            margin-bottom: 2rem;
            text-align: center;

            h4 {
              margin: 0 0 0.5rem 0;
              color: vars.$text-color;
              font-size: 1.2rem;
            }

            p {
              margin: 0;
              color: vars.$text-secondary;
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: vars.$z-index-modal;

    .detail-modal {
      background: vars.$white;
      border-radius: vars.$border-radius-lg;
      max-width: 800px;
      width: 95%;
      max-height: 90vh;
      overflow-y: auto;
      box-shadow: 0 20px 40px vars.$shadow-color;

      .modal-header {
        padding: 1.5rem 2rem;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 20px 20px 0 0;

        h3 {
          margin: 0;
          color: vars.$primary-color;
          font-size: vars.$font-size-xl;
          font-weight: vars.$font-weight-bold;
        }

        .close-btn {
          background: linear-gradient(45deg, #dc3545, #c82333);
          border: none;
          color: white;
          font-size: 1.5rem;
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          &:hover {
            transform: rotate(90deg) scale(1.1);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
          }
        }
      }

      .modal-body {
        padding: 2rem;

        .activity-summary {
          margin-bottom: 2rem;
          text-align: center;

          h4 {
            margin: 0 0 0.5rem 0;
            color: #495057;
            font-size: 1.2rem;
          }

          p {
            margin: 0;
            color: #6c757d;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// Enhanced Statistics Overview Styles
.activity-history-page {
  .activity-content {
    .statistics-overview-card {
      background: linear-gradient(135deg, #ffffff 0%, #f8fffe 100%);
      border-radius: vars.$border-radius-lg;
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
      border: 1px solid rgba(255, 255, 255, 0.9);
      padding: 2rem;
      margin-bottom: vars.$spacing-lg;

      .overview-header {
        text-align: center;
        margin-bottom: 2rem;

        .overview-title {
          color: vars.$text-primary;
          font-weight: 700;
          margin-bottom: 0.5rem;
          font-size: 1.5rem;
        }

        .overview-subtitle {
          color: vars.$text-secondary;
          font-size: 0.95rem;
        }
      }

      .statistics-grid {
        margin-bottom: 2rem;

        .statistic-item {
          background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
          border-radius: vars.$border-radius-md;
          padding: 1.5rem;
          border: 1px solid #f0f0f0;
          transition: all 0.3s ease;
          height: 140px;
          display: flex;
          align-items: center;
          gap: 1rem;
          position: relative;
          overflow: hidden;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
          }

          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            border-radius: vars.$border-radius-md vars.$border-radius-md 0 0;
          }

          &.total-activities {
            &::before {
              background: linear-gradient(90deg,
                  vars.$warning-color,
                  color.adjust(vars.$warning-color, $lightness: 20%));
            }

            .statistic-icon-wrapper .statistic-icon {
              background: linear-gradient(135deg,
                  vars.$warning-color,
                  color.adjust(vars.$warning-color, $lightness: 20%));
            }
          }

          &.donations {
            &::before {
              background: linear-gradient(90deg,
                  vars.$secondary-color,
                  color.adjust(vars.$secondary-color, $lightness: 15%));
            }

            .statistic-icon-wrapper .statistic-icon {
              background: linear-gradient(135deg,
                  vars.$secondary-color,
                  color.adjust(vars.$secondary-color, $lightness: 15%));
            }
          }

          &.requests {
            &::before {
              background: linear-gradient(90deg,
                  vars.$info-color,
                  color.adjust(vars.$info-color, $lightness: 15%));
            }

            .statistic-icon-wrapper .statistic-icon {
              background: linear-gradient(135deg,
                  vars.$info-color,
                  color.adjust(vars.$info-color, $lightness: 15%));
            }
          }

          &.completed {
            &::before {
              background: linear-gradient(90deg,
                  vars.$success-color,
                  color.adjust(vars.$success-color, $lightness: 15%));
            }

            .statistic-icon-wrapper .statistic-icon {
              background: linear-gradient(135deg,
                  vars.$success-color,
                  color.adjust(vars.$success-color, $lightness: 15%));
            }
          }

          .statistic-icon-wrapper {
            .statistic-icon {
              width: 60px;
              height: 60px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-size: 1.5rem;
              box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
            }
          }

          .statistic-content {
            flex: 1;

            .statistic-number {
              font-size: 2.2rem;
              font-weight: 700;
              color: vars.$text-primary;
              line-height: 1;
              margin-bottom: 0.25rem;
            }

            .statistic-label {
              font-size: 1rem;
              font-weight: 600;
              color: vars.$text-primary;
              margin-bottom: 0.25rem;
            }

            .statistic-description {
              font-size: 0.85rem;
              color: vars.$text-secondary;
              opacity: 0.8;
            }
          }
        }
      }

      .progress-summary {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: vars.$border-radius-md;
        padding: 1.5rem;
        border: 1px solid #dee2e6;

        .progress-item {
          .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;

            .progress-percentage {
              font-size: 1.1rem;
              font-weight: 700;
              color: vars.$success-color;
            }
          }

          .progress-bar {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            position: relative;

            .progress-fill {
              height: 100%;
              background: linear-gradient(90deg, vars.$success-color, #66bb6a);
              border-radius: 4px;
              transition: width 0.8s ease;
              position: relative;

              &::after {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(90deg,
                    transparent,
                    rgba(255, 255, 255, 0.3),
                    transparent);
                animation: shimmer 2s infinite;
              }
            }
          }
        }
      }
    }
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

// Animations
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

@keyframes slideOutRight {
  0% {
    opacity: 1;
    transform: translateX(0);
  }

  100% {
    opacity: 0;
    transform: translateX(100%);
  }
}

// Apply animations to header elements
.activity-history-page .activity-content .page-header {
  animation: fadeInUp 0.6s ease-out;

  .header-content {
    h1 {
      animation: fadeInUp 0.8s ease-out 0.2s both;
    }

    p {
      animation: fadeInUp 0.8s ease-out 0.4s both;
    }

    .breadcrumb-hint {
      animation: fadeInUp 0.8s ease-out 0.6s both;
    }
  }

  .btn {
    animation: slideInRight 0.8s ease-out 0.3s both;

    &:hover {
      animation: pulse 0.6s ease-in-out;
    }
  }
}

// Responsive
@media (max-width: 768px) {
  .activity-history-page {
    .activity-content {
      margin-left: 0;
      margin-top: 10px;
      padding: 1rem;

      .page-header {
        padding: 1.5rem;
        flex-direction: column;
        align-items: stretch;
        gap: 1.5rem;

        &::before,
        &::after {
          display: none; // Hide decorative elements on mobile
        }

        .header-content {
          text-align: center;

          h1 {
            font-size: 2rem !important;
            margin-bottom: 0.75rem;
          }

          p {
            font-size: 1rem;
            max-width: none;

            &::before {
              display: block;
              margin: 0 0 4px 0;
            }
          }

          .breadcrumb-hint {
            justify-content: center;
            margin-top: 0.75rem;

            .breadcrumb-item {
              font-size: 0.8rem;
            }
          }
        }

        .btn {
          align-self: center;
          min-width: 140px;
        }
      }

      .stats-section {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;

        .stat-card {
          padding: 1rem;
          flex-direction: column;
          text-align: center;

          .stat-icon {
            font-size: 2rem;
          }

          .stat-content .stat-number {
            font-size: 1.5rem;
          }
        }
      }

      .filters-section {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;

        .filter-group {
          flex-direction: column;
          align-items: stretch;

          select {
            min-width: auto;
          }
        }
      }

      // Responsive CSS cho filter section card mới
      .filter-section-card {
        .ant-row {
          flex-direction: column;
          gap: 1rem;

          .ant-col {
            width: 100% !important;
            max-width: 100% !important;
          }
        }

        .filter-select {
          width: 100%;
          min-width: auto !important;
        }
      }

      .activities-section .activities-list .activity-card {
        .activity-header {
          flex-direction: column;
          align-items: stretch;
          text-align: left;

          .activity-status {
            text-align: left;
          }
        }

        .activity-details .detail-section {

          .blood-info,
          .appointment-info,
          .patient-info {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
          }
        }

        .activity-actions {
          justify-content: stretch;

          .btn {
            flex: 1;
            text-align: center;
          }
        }
      }
    }

    .modal-overlay .workflow-modal {
      width: 98%;
      max-height: 95vh;

      .modal-body {
        padding: 1.5rem;
      }
    }

    // Responsive pagination styles
    .activities-list .activities-pagination {
      margin-top: 1.5rem;
      padding: 1rem 0;

      .activity-pagination {
        .ant-pagination-total-text {
          display: none; // Hide total text on mobile
        }

        .ant-pagination-options {
          display: none; // Hide page size selector on mobile
        }
      }
    }
  }
}

// Enhanced List Layout Styles
.activity-history-page {
  .activity-content {
    .activities-list {
      display: flex;
      flex-direction: column;
      gap: vars.$spacing-lg;

      // Pagination styles
      .activities-pagination {
        display: flex;
        justify-content: center;
        margin-top: 2rem;
        padding: 1.5rem 0;
        border-top: 1px solid #e9ecef;

        .activity-pagination {
          .ant-pagination-item {
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            background: white;
            transition: all 0.3s ease;
            margin: 0 4px;

            a {
              color: #1e293b;
              font-weight: 500;
              font-family: "Roboto", sans-serif;
            }

            &:hover {
              border-color: #2b6cb0;
              background: #f8fafc;
            }

            &.ant-pagination-item-active {
              border-radius: 8px;
              background: linear-gradient(135deg, #2b6cb0, #4299e1);
              border-color: #4299e1;

              a {
                color: white;
              }

              &:hover {
                border-color: #4299e1;
              }
            }
          }

          .ant-pagination-prev,
          .ant-pagination-next {
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            background: white;
            transition: all 0.3s ease;

            .ant-pagination-item-link {
              color: #1e293b;
              border-radius: 8px;
            }

            &:hover {
              border-color: #2b6cb0;
              background: #f8fafc;
            }
          }

          .ant-pagination-disabled {
            .ant-pagination-item-link {
              color: #94a3b8;
            }
          }

          .ant-pagination-jump-prev,
          .ant-pagination-jump-next {
            .ant-pagination-item-ellipsis {
              color: #8c8c8c;
            }
          }

          .ant-pagination-total-text {
            color: #64748b;
            font-size: 0.9rem;
            margin-right: 1rem;
          }
        }
      }

      .activity-card {
        &.donation {
          border-left: 4px solid vars.$secondary-color;
        }

        &.request {
          border-left: 4px solid vars.$info-color;
        }

        .activity-header {
          .activity-info {
            .activity-title-wrapper {
              display: flex;
              align-items: flex-start;
              gap: vars.$spacing-base;

              .activity-icon {
                width: 48px;
                height: 48px;
                border-radius: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 20px;
                color: vars.$white;
                flex-shrink: 0;
              }

              .activity-title-content {
                flex: 1;

                .activity-title {
                  margin: 0 0 vars.$spacing-xs 0;
                  color: vars.$text-color;
                  font-size: vars.$font-size-xl;
                  font-weight: vars.$font-weight-bold;
                  line-height: 1.3;
                }

                .activity-date {
                  color: vars.$text-secondary;
                  font-size: vars.$font-size-small;
                  font-weight: vars.$font-weight-medium;
                  display: flex;
                  align-items: center;
                  gap: 4px;

                  .date-icon {
                    font-size: 12px;
                  }
                }
              }
            }
          }
        }

        .activity-details {
          .detail-section {
            h4 {
              margin: 0 0 vars.$spacing-sm 0;
              color: vars.$text-color;
              font-size: vars.$font-size-base;
              font-weight: vars.$font-weight-bold;
              display: flex;
              align-items: center;
              gap: 6px;
            }

            .blood-info,
            .appointment-info,
            .patient-info {
              display: flex;
              flex-direction: column;
              gap: vars.$spacing-sm;

              .info-item {
                display: flex;
                align-items: center;
                gap: vars.$spacing-sm;
                padding: 8px 12px;
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #e9ecef;

                .info-icon {
                  color: vars.$primary-color;
                  font-size: 14px;
                  flex-shrink: 0;
                }

                .info-label {
                  font-weight: vars.$font-weight-semibold;
                  color: vars.$text-secondary;
                  min-width: 80px;
                }

                .info-value {
                  color: vars.$text-color;
                  font-weight: vars.$font-weight-medium;
                  flex: 1;

                  &.blood-type {
                    font-weight: vars.$font-weight-bold;
                    color: vars.$secondary-color;
                  }
                }
              }
            }

            .notes,
            .doctor-notes {
              padding: vars.$spacing-sm vars.$spacing-base;
              background: vars.$background-section;
              border-radius: 8px;
              border-left: 4px solid vars.$info-color;
              font-style: italic;
              color: vars.$text-secondary;
            }

            &.cancelled-info {
              .cancelled-details {
                padding: vars.$spacing-sm vars.$spacing-base;
                background: color.adjust(vars.$error-color, $lightness: 45%);
                border-radius: 8px;
                border-left: 4px solid vars.$error-color;

                .cancelled-date {
                  margin-top: vars.$spacing-xs;
                }
              }
            }
          }
        }

        .activity-actions {
          .btn {
            display: flex;
            align-items: center;
            gap: 6px;

            &.btn-info {
              background: vars.$info-color;
              color: vars.$white;

              &:hover:not(:disabled) {
                background: color.adjust(vars.$info-color, $lightness: -10%);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
              }
            }

            &.btn-danger {
              background: vars.$error-color;
              color: vars.$white;

              &:hover:not(:disabled) {
                background: #d32f2f;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
              }
            }

            &.btn-success {
              background: vars.$success-color;
              color: vars.$white;

              &:hover:not(:disabled) {
                background: #388e3c;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
              }
            }
          }

          .cancelled-label {
            flex: 1;
            display: flex;
            align-items: center;
            font-style: italic;
            color: #6c757d;
          }
        }
      }
    }

    // Enhanced loading and empty states
    .loading-state {
      .loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: vars.$spacing-md;

        .loading-spinner {
          width: 50px;
          height: 50px;
          border: 4px solid vars.$background-light;
          border-top: 4px solid vars.$primary-color;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        .loading-text-wrapper {
          text-align: center;

          .loading-title {
            margin: 0 0 vars.$spacing-sm 0;
            color: vars.$text-color;
            font-size: vars.$font-size-xl;
          }

          .loading-description {
            margin: 0;
            font-size: vars.$font-size-base;
            color: vars.$text-secondary;
          }
        }
      }
    }

    .empty-state {
      .empty-content {
        .empty-title {
          margin: 0 0 vars.$spacing-base 0;
          color: vars.$text-color;
          font-size: vars.$font-size-2xl;
        }

        .empty-description {
          margin: 0 0 vars.$spacing-lg 0;
          font-size: vars.$font-size-lg;
          line-height: vars.$line-height-normal;
          max-width: 600px;
          margin-left: auto;
          margin-right: auto;
        }

        .empty-action-button {
          font-size: vars.$font-size-base;
          font-weight: vars.$font-weight-semibold;
          padding: 12px 24px;
          height: auto;
          border-radius: 12px;
        }
      }
    }
  }
}

// Activity type specific icon colors
.activity-card.donation .activity-icon {
  background: linear-gradient(135deg,
      vars.$secondary-color,
      #f44336) !important;
}

.activity-card.request .activity-icon {
  background: linear-gradient(135deg, vars.$info-color, #2196f3) !important;
}

// Mobile responsive improvements for list layout
@media (max-width: 768px) {
  .activity-history-page .activity-content {
    .activities-list {
      gap: vars.$spacing-base;

      .activity-card {
        .activity-header {
          flex-direction: column;
          align-items: stretch;
          gap: vars.$spacing-sm;

          .activity-info .activity-title-wrapper {
            flex-direction: row;
            align-items: flex-start;

            .activity-icon {
              width: 40px;
              height: 40px;
              font-size: 16px;
            }

            .activity-title-content .activity-title {
              font-size: vars.$font-size-lg;
            }
          }
        }

        .activity-details .detail-section {

          .blood-info,
          .appointment-info,
          .patient-info {
            .info-item {
              flex-direction: column;
              align-items: flex-start;
              gap: 4px;

              .info-label {
                min-width: auto;
                font-size: vars.$font-size-small;
              }
            }
          }
        }

        .activity-actions {
          flex-direction: column;
          gap: vars.$spacing-sm;

          .btn {
            width: 100%;
            justify-content: center;
          }

          .cancelled-note {
            text-align: center;
            margin-top: 0.75rem;
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .activity-history-page .activity-content {
    .activities-list {
      .activity-card {
        .activity-actions {
          .ant-space {
            flex-direction: column;
            align-items: stretch;
          }

          .btn {
            min-width: auto;
            width: 100%;
            margin-bottom: 0.5rem;
          }

          .cancelled-note {
            text-align: center;
            margin-top: 0.75rem;
          }
        }
      }
    }
  }
}

// Activity Actions Styles - Horizontal Layout
.activity-history-page {
  .activity-content {
    .activities-list {
      .activity-card {
        .activity-actions {
          padding: 1rem 0 0 0;
          border-top: 1px solid #f0f0f0;
          margin-top: 1rem;

          .ant-space {
            width: 100%;
            justify-content: flex-start;
          }

          .btn {
            border-radius: vars.$border-radius-sm;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 120px;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            &.btn-info {
              border-color: vars.$info-color;
              color: vars.$info-color;

              &:hover {
                background: vars.$info-color;
                color: white;
              }
            }

            &.btn-danger {
              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(245, 34, 45, 0.3);
              }
            }

            &.btn-success {
              background: linear-gradient(135deg, vars.$success-color, #52c41a);
              border: none;

              &:hover {
                background: linear-gradient(135deg,
                    #52c41a,
                    vars.$success-color );
              }
            }
          }

          .cancelled-note {
            font-style: italic;
            align-self: center;
            margin-left: 8px;
          }
        }
      }

      // Cancelled card specific styles
      .activity-card.cancelled {
        .activity-actions {
          .cancelled-note {
            color: #8c8c8c;
            font-size: 0.9rem;
          }
        }
      }
    }
  }
}

// Responsive adjustments for actions
@media (max-width: 768px) {
  .activity-history-page .activity-content {
    .activities-list {
      .activity-card {
        .activity-actions {
          .btn {
            min-width: 100px;
            font-size: 0.85rem;
            padding: 4px 12px;
          }

          .cancelled-note {
            font-size: 0.8rem;
            margin-top: 0.5rem;
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .activity-history-page .activity-content {
    .activities-list {
      .activity-card {
        .activity-actions {
          .ant-space {
            flex-direction: column;
            align-items: stretch;
          }

          .btn {
            min-width: auto;
            width: 100%;
            margin-bottom: 0.5rem;
          }

          .cancelled-note {
            text-align: center;
            margin-top: 0.75rem;
          }
        }
      }
    }
  }
}

// Enhanced Action Buttons Styles
.activity-history-page {
  .activity-content {
    .activities-list {
      .activity-card {
        .activity-actions {
          .btn {
            &.btn-workflow {
              border-color: vars.$primary-color;
              color: vars.$primary-color;

              &:hover {
                background: vars.$primary-color;
                color: vars.$white;
                border-color: vars.$primary-color;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
              }
            }

            &.btn-detail {
              background: linear-gradient(135deg,
                  #1976d2 0%,
                  #2196f3 50%,
                  #03a9f4 100%);
              border: none;
              color: white;
              box-shadow: 0 4px 16px rgba(25, 118, 210, 0.3);

              &:hover {
                background: linear-gradient(135deg,
                    #1565c0 0%,
                    #1976d2 50%,
                    #2196f3 100%);
                transform: translateY(-2px);
                box-shadow: 0 8px 24px rgba(25, 118, 210, 0.4);
              }
            }

            &.btn-success {
              background: linear-gradient(135deg,
                  vars.$success-color,
                  vars.$success-color );
              border: none;
              color: vars.$white;

              &:hover {
                background: linear-gradient(135deg,
                    color.adjust(vars.$success-color, $lightness: -10%),
                    vars.$success-color );
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
              }
            }
          }
        }
      }
    }
  }
}

// Modal responsive styles đã chuyển sang ActivityDetailModal.scss

// Activity Header Layout - Status positioned to the right
.activity-history-page {
  .activity-content {
    .activities-list {
      .activity-card {
        .activity-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 1rem;
          position: relative;

          .activity-info {
            flex: 1;
            min-width: 0; // Prevent flex item from overflowing
          }

          .activity-title-wrapper {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;

            .activity-icon {
              width: 40px;
              height: 40px;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 18px;
              color: white;
              flex-shrink: 0;

              &:has(+ .activity-title-content .activity-title:contains("Hiến máu")) {
                background: linear-gradient(135deg,
                    vars.$secondary-color,
                    color.adjust(vars.$secondary-color, $lightness: 15%));
              }

              &:has(+ .activity-title-content .activity-title:contains("Yêu cầu")) {
                background: linear-gradient(135deg,
                    vars.$info-color,
                    color.adjust(vars.$info-color, $lightness: 15%));
              }
            }

            .activity-title-content {
              flex: 1;
              min-width: 0;

              .activity-title {
                margin: 0 0 0.25rem 0;
                font-size: 1.1rem;
                font-weight: 600;
                color: vars.$text-primary;
                line-height: 1.3;
              }

              .activity-date {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 0.85rem;
                color: vars.$text-secondary;

                .date-icon {
                  font-size: 0.8rem;
                }
              }
            }
          }

          .activity-status {
            margin-left: 1rem;
            flex-shrink: 0;
            align-self: flex-start;

            // Sử dụng StatusBadge component thống nhất
          }
        }

        // Cancelled card specific styling
        &.cancelled {
          .activity-header {
            .activity-status {
              .status-badge {
                background: linear-gradient(135deg,
                    vars.$error-color,
                    color.adjust(vars.$error-color, $lightness: 10%));
                border: none;
                color: vars.$white;
                animation: pulse-error 2s ease-in-out infinite;
              }
            }
          }
        }
      }
    }
  }
}

@keyframes pulse-error {

  0%,
  100% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  50% {
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.4);
  }
}

// Responsive adjustments for activity header
@media (max-width: 768px) {
  .activity-history-page .activity-content {
    .activities-list {
      .activity-card {
        .activity-header {
          .activity-title-wrapper {
            .activity-icon {
              width: 35px;
              height: 35px;
              font-size: 16px;
            }

            .activity-title-content {
              .activity-title {
                font-size: 1rem;
              }

              .activity-date {
                font-size: 0.8rem;
              }
            }
          }

          .activity-status {
            margin-left: 0.75rem;

            .status-badge {
              font-size: 0.7rem;
              padding: 0.2rem 0.6rem;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .activity-history-page .activity-content {
    .activities-list {
      .activity-card {
        .activity-header {
          flex-direction: column;
          align-items: stretch;
          gap: 0.75rem;

          .activity-status {
            margin-left: 0;
            align-self: flex-end;
          }
        }
      }
    }
  }
}

// Modal workflow styles với base colors
.workflow-modal {
  border-radius: 16px;

  .workflow-activity-card {
    margin-bottom: 16px;
    border-radius: 12px;
    background: linear-gradient(135deg,
        vars.$background-main,
        vars.$primary-light );
    border: 1px solid vars.$border-light;
  }

  .workflow-activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: vars.$white;
    font-size: 16px;

    &.donation {
      background: linear-gradient(135deg,
          vars.$secondary-color,
          color.adjust(vars.$secondary-color, $lightness: 10%));
    }

    &.request {
      background: linear-gradient(135deg,
          vars.$primary-color,
          vars.$info-color );
    }
  }

  .workflow-activity-title {
    font-size: 16px;
    color: vars.$text-primary;
  }

  .workflow-activity-date {
    font-size: 13px;
    color: vars.$text-secondary;
  }

  .workflow-close-button {
    border-radius: 8px;
    font-weight: 500;
  }

  .modal-title-icon {
    &.donation {
      color: vars.$secondary-color;
    }

    &.request {
      color: vars.$primary-color;
    }
  }

  .modal-title-text {
    font-size: 18px;
    color: vars.$text-primary;
  }
}