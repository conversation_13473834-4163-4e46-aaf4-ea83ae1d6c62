import React from "react";
import { Typo<PERSON>, <PERSON>, Button } from "antd";
import PropTypes from "prop-types";
import "../../styles/components/PageHeader.scss";

const { Title, Text } = Typography;

const AdminPageHeader = ({
  title,
  description,
  icon,
  actions = [],
  className = "",
  style = {},
  // Legacy props for backward compatibility
  subtitle,
}) => {
  // Use description or subtitle for backward compatibility
  const displayDescription = description || subtitle;

  // Handle both JSX element and component reference
  const renderIcon = () => {
    if (!icon) return null;

    // If it's already a JSX element, render it directly
    if (React.isValidElement(icon)) {
      return <div className="header-icon">{icon}</div>;
    }

    // If it's a component, render it as JSX
    if (typeof icon === 'function') {
      const IconComponent = icon;
      return (
        <div className="header-icon">
          <IconComponent />
        </div>
      );
    }

    return null;
  };

  return (
    <div className={`admin-page-header ${className}`} style={style}>
      <div className="header-info">
        <div className="header-title-section">
          {renderIcon()}
          <div className="header-text">
            <Title level={2} className="header-title">
              {title}
            </Title>
            {displayDescription && (
              <Text className="header-description">{displayDescription}</Text>
            )}
          </div>
        </div>
      </div>

      {actions.length > 0 && (
        <div className="header-actions">
          <Space size="middle" wrap>
            {actions.map((action, index) => (
              <Button
                key={index}
                type={action.type || "default"}
                icon={action.icon}
                onClick={action.onClick}
                loading={action.loading}
                disabled={action.disabled}
                size={action.size || "default"}
                className={action.className || ""}
                style={action.style || {}}
              >
                {action.label}
              </Button>
            ))}
          </Space>
        </div>
      )}
    </div>
  );
};

AdminPageHeader.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string,
  subtitle: PropTypes.string, // Legacy prop
  icon: PropTypes.oneOfType([
    PropTypes.elementType, // Component reference
    PropTypes.element,     // JSX element
  ]),
  actions: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      onClick: PropTypes.func,
      type: PropTypes.string,
      icon: PropTypes.node,
      loading: PropTypes.bool,
      disabled: PropTypes.bool,
      size: PropTypes.string,
      className: PropTypes.string,
      style: PropTypes.object,
    })
  ),
  className: PropTypes.string,
  style: PropTypes.object,
};

export default AdminPageHeader;
