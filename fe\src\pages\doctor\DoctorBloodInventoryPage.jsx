import React from "react";
import { ReloadOutlined, DatabaseOutlined } from "@ant-design/icons";
import DoctorLayout from "../../components/doctor/DoctorLayout";
import PageHeader from "../../components/doctor/PageHeader";
import BloodInventoryViewPage from "../../components/shared/BloodInventoryViewPage";
import "../../styles/components/DoctorPageHeader.scss";
import "../../styles/pages/BloodInventoryViewPage.scss";

/**
 * Doctor Blood Inventory Page
 * Wrapper component cho Doctor (cả khoa huyết học và khoa khác) sử dụng shared BloodInventoryViewPage
 * Chỉ xem kho máu, không có tabs lịch sử và nút nhập/xuất kho
 */
const DoctorBloodInventoryPage = () => {
  // Page Header Component cho Doctor
  const DoctorPageHeader = ({ loadInventory, loading }) => (
    <PageHeader
      title="Kho máu"
      description="Xem thông tin tồn kho máu và các thành phần máu"
      icon={DatabaseOutlined}
      actions={[
        {
          label: "Làm mới",
          icon: <ReloadOutlined />,
          onClick: loadInventory,
          loading: loading,
        },
      ]}
    />
  );

  return (
    <BloodInventoryViewPage
      showManagerFeatures={false} // Doctor không có tính năng Manager
      pageHeaderComponent={DoctorPageHeader}
      layoutComponent={DoctorLayout}
    />
  );
};

export default DoctorBloodInventoryPage;
