import axiosInstance from "./axiosInstance";
import userInfoService from "./userInfoService";
import bloodRequestService from "./bloodRequestService";
import { fetchBloodInventory } from "./bloodInventoryService";

const DASHBOARD_API = import.meta.env.VITE_DASHBOARD_API || "/api/Dashboard";

/**
 * Service for Manager Dashboard API operations
 */
const managerDashboardService = {
  /**
   * Get comprehensive dashboard statistics for manager
   * @returns {Promise} Dashboard data
   */
  getDashboardData: async () => {
    try {
      // Fetch data from multiple APIs in parallel
      const [users, bloodRequests, bloodInventory] = await Promise.all([
        userInfoService.getAllUsers().catch((err) => {
          console.error("Error fetching users:", err);
          return [];
        }),
        bloodRequestService
          .getBloodRequests()
          .then((result) => (result.success ? result.data : []))
          .catch((err) => {
            console.error("Error fetching blood requests:", err);
            return [];
          }),
        fetchBloodInventory().catch((err) => {
          console.error("Error fetching blood inventory:", err);
          return [];
        }),
      ]);

      // Process user statistics
      const userStats = managerDashboardService.processUserStatistics(users);

      // Process blood request statistics
      const requestStats = managerDashboardService.processBloodRequestStatistics(bloodRequests);

      // Process blood inventory statistics
      const inventoryStats = managerDashboardService.processBloodInventoryStatistics(bloodInventory);

      // Generate monthly requests trend
      const monthlyRequestsData = managerDashboardService.generateMonthlyRequestsTrend(bloodRequests);

      return {
        success: true,
        data: {
          // User statistics
          totalDonors: userStats.donors,
          totalRecipients: userStats.members,
          totalUsers: userStats.total,
          activeUsers: userStats.active,

          // Blood request statistics
          totalRequests: requestStats.total,
          pendingRequests: requestStats.pending,
          completedRequests: requestStats.completed,
          rejectedRequests: requestStats.rejected,

          // Blood inventory statistics
          totalBloodUnits: inventoryStats.totalUnits,
          bloodInventory: inventoryStats.inventory,
          bloodGroupData: inventoryStats.bloodGroupData,
          criticalInventory: inventoryStats.critical,
          lowInventory: inventoryStats.low,

          // Trend data
          monthlyRequestsData,

          // Recent data
          recentRequests: bloodRequests.slice(0, 10),
          recentUsers: users.slice(0, 10),
        },
        message: "Lấy dữ liệu dashboard thành công",
      };
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      return {
        success: false,
        error: error.message || "Có lỗi xảy ra khi lấy dữ liệu dashboard",
        data: null,
      };
    }
  },

  /**
   * Process user statistics
   * @param {Array} users - Array of users
   * @returns {Object} User statistics
   */
  processUserStatistics: (users) => {
    const stats = {
      total: users.length,
      active: users.filter((u) => u.status === 1).length,
      inactive: users.filter((u) => u.status === 0).length,
      members: 0,
      doctors: 0,
      managers: 0,
      donors: 0,
      byBloodType: {},
      byGender: { male: 0, female: 0, other: 0 },
    };

    users.forEach((user) => {
      // Count by role
      if (user.roleID === 4) stats.members++;
      else if (user.roleID === 3) stats.doctors++;
      else if (user.roleID === 2) stats.managers++;

      // Count donors (users with donation history or blood type)
      if (user.bloodGroup && user.rhType) {
        stats.donors++;
      }

      // Count by blood type
      if (user.bloodGroup && user.rhType) {
        const bloodType = `${user.bloodGroup}${user.rhType}`;
        stats.byBloodType[bloodType] = (stats.byBloodType[bloodType] || 0) + 1;
      }

      // Count by gender
      if (user.gender) {
        const gender = user.gender.toLowerCase();
        if (stats.byGender[gender] !== undefined) {
          stats.byGender[gender]++;
        } else {
          stats.byGender.other++;
        }
      }
    });

    return stats;
  },

  /**
   * Process blood request statistics
   * @param {Array} requests - Array of blood requests
   * @returns {Object} Request statistics
   */
  processBloodRequestStatistics: (requests) => {
    const pendingRequestsArr = requests.filter((r) => r.status === 1);
    console.log('[ManagerDashboard] Số lượng request cần xuất kho (status=1):', pendingRequestsArr.length, pendingRequestsArr);
    const stats = {
      total: requests.length,
      // Chờ xử lý: các yêu cầu máu đã đến trạng thái cần xuất kho (status === 1)
      pending: pendingRequestsArr.length,
      accepted: pendingRequestsArr.length,
      completed: requests.filter((r) => r.status === 2).length,
      rejected: requests.filter((r) => r.status === 3).length,
      deleted: requests.filter((r) => r.status === 4).length,
      byBloodType: {},
      byMonth: {},
    };

    requests.forEach((request) => {
      // Count by blood type
      if (request.bloodGroup && request.rhType) {
        const bloodType = `${request.bloodGroup}${request.rhType}`;
        stats.byBloodType[bloodType] = (stats.byBloodType[bloodType] || 0) + 1;
      }

      // Count by month
      if (request.createdTime) {
        const month = new Date(request.createdTime).getMonth();
        const monthKey = `month_${month}`;
        stats.byMonth[monthKey] = (stats.byMonth[monthKey] || 0) + 1;
      }
    });

    return stats;
  },

  /**
   * Process blood inventory statistics
   * @param {Array} inventory - Array of blood inventory items
   * @returns {Object} Inventory statistics
   */
  processBloodInventoryStatistics: (inventory) => {
    const stats = {
      totalUnits: 0,
      inventory: [],
      bloodGroupData: [],
      critical: [],
      low: [],
      normal: [],
      high: [],
      byComponent: {},
    };

    const bloodTypeGroups = {};

    inventory.forEach((item) => {
      const quantity = parseInt(item.quantity) || 0;
      stats.totalUnits += quantity;

      // Determine status based on quantity
      let status = "normal";
      if (quantity <= 2) {
        status = "critical";
        stats.critical.push(item);
      } else if (quantity <= 5) {
        status = "low";
        stats.low.push(item);
      } else if (quantity >= 30) {
        status = "high";
        stats.high.push(item);
      } else {
        stats.normal.push(item);
      }

      // Create blood type string
      const bloodType = `${item.bloodGroup}${item.rhType}`;

      // Group by blood type for chart
      if (!bloodTypeGroups[bloodType]) {
        bloodTypeGroups[bloodType] = {
          name: bloodType,
          value: 0,
          status: status,
        };
      }
      bloodTypeGroups[bloodType].value += quantity;

      // Update status to most critical
      if (status === "critical" || bloodTypeGroups[bloodType].status === "normal") {
        bloodTypeGroups[bloodType].status = status;
      }

      // Count by component type
      const component = item.componentType || "Whole";
      stats.byComponent[component] = (stats.byComponent[component] || 0) + quantity;

      // Add processed item to inventory
      stats.inventory.push({
        ...item,
        bloodType,
        status,
        quantity,
      });
    });

    // Convert blood type groups to array for chart
    stats.bloodGroupData = Object.values(bloodTypeGroups);

    return stats;
  },

  /**
   * Generate monthly requests trend data
   * @param {Array} requests - Array of blood requests
   * @returns {Array} Monthly trend data
   */
  generateMonthlyRequestsTrend: (requests) => {
    const monthNames = ["T1", "T2", "T3", "T4", "T5", "T6", "T7", "T8", "T9", "T10", "T11", "T12"];
    const currentYear = new Date().getFullYear();
    const monthlyData = new Array(12).fill(0);

    requests.forEach((request) => {
      if (request.createdTime) {
        const date = new Date(request.createdTime);
        if (date.getFullYear() === currentYear) {
          const month = date.getMonth();
          monthlyData[month]++;
        }
      }
    });

    // Return last 6 months
    const currentMonth = new Date().getMonth();
    const result = [];
    for (let i = 5; i >= 0; i--) {
      const monthIndex = (currentMonth - i + 12) % 12;
      result.push({
        month: monthNames[monthIndex],
        requests: monthlyData[monthIndex],
      });
    }

    return result;
  },

  /**
   * Get dashboard statistics summary
   * @returns {Promise} API response
   */
  getDashboardStats: async () => {
    try {
      const response = await axiosInstance.get(`${DASHBOARD_API}/manager/stats`);
      return {
        success: true,
        data: response.data,
        message: "Lấy thống kê dashboard thành công",
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || "Có lỗi xảy ra khi lấy thống kê dashboard",
        details: error.response?.data,
      };
    }
  },
};

export default managerDashboardService;
