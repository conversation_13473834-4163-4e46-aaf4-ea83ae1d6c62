import React, { useState, useEffect, useRef } from "react";
import { Link, useLocation } from "react-router-dom";
import PropTypes from "prop-types";
import "../../styles/components/NavbarCommon.scss";


const NavbarBase = ({ logoSrc, logoAlt, navItems, actionItems, userInfo }) => {
  
  const location = useLocation();

  
  const [showMobileNav, setShowMobileNav] = useState(false); // Hiển thị mobile nav

  
  const mobileNavRef = useRef(null); // Ref cho mobile nav

  /**
   * Đóng mobile nav khi click bên ngoài
   */
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (mobileNavRef.current && !mobileNavRef.current.contains(event.target)) {
        setShowMobileNav(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  
  const handleMobileNavItemClick = () => {
    setShowMobileNav(false);
  };

  /**
   * Toggle hiển thị mobile nav
   */
  const toggleMobileNav = () => {
    setShowMobileNav(prev => !prev);
  };

  return (
    <header className="navbar">
      <div className="navbar-logo">
        <Link to="/">
          <img src={logoSrc} alt={logoAlt} className="logo-img" />
        </Link>
      </div>

      {/* Desktop Navigation */}
      <nav className="navbar-nav desktop-nav">
        {navItems.map((item) => (
          <Link
            key={item.path}
            to={item.path}
            className={`${location.pathname === item.path ? "active" : ""}`}
          >
            {item.label}
          </Link>
        ))}
      </nav>

      {/* Desktop Actions */}
      <div className="navbar-actions desktop-actions">
        {actionItems.map((item) => (
          <Link key={item.path} to={item.path} className={item.className}>
            {item.label}
          </Link>
        ))}
        {userInfo && (
          <div className="user-info">
            <span className="user-name">{userInfo.name}</span>
            <div className="user-avatar">
              {userInfo.avatar || userInfo.name.charAt(0).toUpperCase()}
            </div>
          </div>
        )}
      </div>

      {/* Mobile Hamburger Button */}
      <button
        className="mobile-menu-toggle"
        onClick={toggleMobileNav}
        aria-label="Toggle mobile menu"
      >
        <span className={`hamburger-line ${showMobileNav ? 'active' : ''}`}></span>
        <span className={`hamburger-line ${showMobileNav ? 'active' : ''}`}></span>
        <span className={`hamburger-line ${showMobileNav ? 'active' : ''}`}></span>
      </button>

      {/* Mobile Navigation Menu */}
      <div
        className={`mobile-nav-overlay ${showMobileNav ? 'active' : ''}`}
        ref={mobileNavRef}
      >
        <nav className="mobile-nav">
          <div className="mobile-nav-items">
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`mobile-nav-item ${location.pathname === item.path ? "active" : ""}`}
                onClick={handleMobileNavItemClick}
              >
                {item.label}
              </Link>
            ))}

            <div className="mobile-nav-divider"></div>

            {actionItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`mobile-nav-item ${item.className}`}
                onClick={handleMobileNavItemClick}
              >
                {item.label}
              </Link>
            ))}
          </div>
        </nav>
      </div>
    </header>
  );
};

NavbarBase.propTypes = {
  logoSrc: PropTypes.string.isRequired,
  logoAlt: PropTypes.string.isRequired,
  navItems: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      path: PropTypes.string.isRequired,
    })
  ).isRequired,
  actionItems: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      path: PropTypes.string.isRequired,
      className: PropTypes.string,
    })
  ).isRequired,
  userInfo: PropTypes.shape({
    name: PropTypes.string,
    avatar: PropTypes.string,
  }),
};

export default NavbarBase;
