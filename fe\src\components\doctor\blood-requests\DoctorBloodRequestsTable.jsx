import { useState, useMemo } from "react";
import { Table, Button, Space, Tooltip } from "antd";
import {
  EyeOutlined,
  CheckOutlined,
  CloseOutlined,
  CalendarOutlined,
} from "@ant-design/icons";
import { useBloodRequestTableData } from "../../../hooks/useBloodRequestTableData";
import {
  getStatusText,
  getStatusColor,
  formatDate,
} from "../../../utils/bloodRequestUtils";
import {
  getBloodComponentName,
  COMPONENT_TYPES_ARRAY,
} from "../../../constants/bloodInventoryConstants";
import { getGenderText } from "../../../utils/genderUtils";
import RejectRequestModal from "./RejectRequestModal";
import "../../../styles/components/RejectRequestModal.scss";
import "../../../styles/components/BloodRequestTable.scss";


const DoctorBloodRequestsTable = ({
  data,
  loading,
  onViewDetails,
  onApprove,
  onReject,
  // onComplete, // Removed - no longer needed for hematology doctors
  isBloodDepartment,
  activeTab,
  // role = "doctor", // Removed - not used
}) => {
  // Enhanced requests with user data using Context API
  const {
    enhancedRequests,
    loading: userDataLoading
  } = useBloodRequestTableData(data || []);

  // Get unique values for filters
  const filterOptions = useMemo(() => {
    const bloodTypes = [...new Set(enhancedRequests.map(req => req.bloodType).filter(Boolean))];
    const statuses = [...new Set(enhancedRequests.map(req => req.status).filter(status => status !== undefined))];
    const patientNames = [...new Set(enhancedRequests.map(req => req.patientInfo?.name).filter(Boolean))];

    return {
      bloodTypes: bloodTypes.map(type => ({ text: type, value: type })),
      statuses: statuses.map(status => ({ text: getStatusText(status), value: status })),
      patientNames: patientNames.map(name => ({ text: name, value: name }))
    };
  }, [enhancedRequests]);
  // State for reject modal
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [selectedRequestForReject, setSelectedRequestForReject] =
    useState(null);
  const [rejectLoading, setRejectLoading] = useState(false);

  // Handle reject button click
  const handleRejectClick = (request) => {
    setSelectedRequestForReject(request);
    setShowRejectModal(true);
  };

  // Handle reject confirmation with reason
  const handleRejectConfirm = async (reason) => {
    if (selectedRequestForReject && onReject) {
      setRejectLoading(true);
      try {
        await onReject(selectedRequestForReject.id, reason);
      } catch (error) {
        console.error("Error rejecting request:", error);
      } finally {
        setRejectLoading(false);
      }
    }
  };

  // Handle reject modal close
  const handleRejectModalClose = () => {
    setShowRejectModal(false);
    setSelectedRequestForReject(null);
  };

  // Table columns configuration
  const columns = [
    {
      title: "Mã yêu cầu",
      dataIndex: "requestID",
      key: "requestID",
      width: 100,
      sorter: (a, b) => a.requestID - b.requestID,
      render: (id) => <span className="request-id">#{id}</span>,
    },
    {
      title: "Bệnh nhân",
      dataIndex: "patientInfo",
      key: "patientInfo",
      width: 150,
      filters: filterOptions.patientNames,
      onFilter: (value, record) => record.patientInfo?.name === value,
      sorter: (a, b) => (a.patientInfo?.name || '').localeCompare(b.patientInfo?.name || ''),
      render: (patientInfo) => (
        <div className="patient-info">
          <div className="patient-name">{patientInfo?.name}</div>
          <div className="patient-details">
            <span className="detail-item">{patientInfo?.age} tuổi</span>
            <span className="detail-item">{getGenderText(patientInfo?.gender)}</span>
          </div>
        </div>
      ),
    },
    {
      title: "Nhóm máu",
      dataIndex: "bloodType",
      key: "bloodType",
      width: 80,
      filters: filterOptions.bloodTypes,
      onFilter: (value, record) => record.bloodType === value,
      sorter: (a, b) => (a.bloodType || '').localeCompare(b.bloodType || ''),
      render: (bloodType) => {
        const isPositive = bloodType?.includes("+");
        return (
          <span
            className={`blood-type-tag ${isPositive ? "positive" : "negative"}`}
          >
            {bloodType}
          </span>
        );
      },
    },
    {
      title: "Số lượng",
      dataIndex: "quantity",
      key: "quantity",
      width: 80,
      sorter: (a, b) => a.quantity - b.quantity,
      render: (quantity) => (
        <span className="quantity-display">
          {quantity}
          <span className="unit">ml</span>
        </span>
      ),
    },
    {
      title: "Thành phần",
      dataIndex: "componentType",
      key: "componentType",
      width: 100,
      filters: COMPONENT_TYPES_ARRAY.map((type) => ({
        text: type,
        value: type,
      })),
      onFilter: (value, record) => {
        const componentName =
          getBloodComponentName(record.componentId) || "Toàn phần";
        return componentName === value;
      },
      render: (_, record) => (
        <span className="component-display">
          {getBloodComponentName(record.componentId) || "Toàn phần"}
        </span>
      ),
    },
    {
      title:
        isBloodDepartment && activeTab === "external"
          ? "Người yêu cầu"
          : "Bác sĩ",
      dataIndex: "doctorInfo",
      key: "doctorInfo",
      width: 120,
      render: (doctorInfo) => (
        <div className="doctor-info">
          <div className="doctor-name">{doctorInfo?.name || "N/A"}</div>
          {doctorInfo?.department && (
            <div className="doctor-department">{doctorInfo.department}</div>
          )}
        </div>
      ),
    },
    {
      title: "Ngày tạo",
      dataIndex: "createdTime",
      key: "createdTime",
      width: 110,
      sorter: (a, b) => new Date(a.createdTime) - new Date(b.createdTime),
      defaultSortOrder: 'descend',
      render: (time) => (
        <div className="date-display">
          <CalendarOutlined className="date-icon" />
          {formatDate(time)}
        </div>
      ),
    },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      width: 100,
      filters: filterOptions.statuses,
      onFilter: (value, record) => record.status === value,
      sorter: (a, b) => a.status - b.status,
      render: (status) => (
        <span className={`status-badge status-${getStatusColor(status)}`}>
          {getStatusText(status)}
        </span>
      ),
    },
    {
      title: "Hành động",
      key: "actions",
      width: 120,
      render: (_, request) => {
        const actionButtonStyle = {
          borderRadius: "8px",
          border: "none",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          transition: "all 0.2s ease",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        };

        const viewButtonStyle = {
          ...actionButtonStyle,
          background: "#e6f7ff",
          color: "#1890ff",
          border: "1px solid #91d5ff",
        };

        const acceptButtonStyle = {
          ...actionButtonStyle,
          background: "#f6ffed",
          color: "#52c41a",
          border: "1px solid #b7eb8f",
        };

        const rejectButtonStyle = {
          ...actionButtonStyle,
          background: "#fff2f0",
          color: "#ff4d4f",
          border: "1px solid #ffccc7",
        };

        return (
          <Space size="small" className="action-buttons">
            {/* Always show Details button */}
            <Tooltip title="Xem chi tiết">
              <Button
                icon={<EyeOutlined />}
                onClick={() => onViewDetails(request)}
                size="small"
                style={viewButtonStyle}
                className="view-btn"
              />
            </Tooltip>

            {/* Only show action buttons for hematology department */}
            {isBloodDepartment && request.status === 0 && (
              <>
                <Tooltip title="Chấp nhận yêu cầu">
                  <Button
                    icon={<CheckOutlined />}
                    onClick={() => onApprove(request.id || request.requestId)}
                    size="small"
                    style={acceptButtonStyle}
                    className="accept-btn"
                  />
                </Tooltip>
                <Tooltip title="Từ chối yêu cầu">
                  <Button
                    icon={<CloseOutlined />}
                    onClick={() => handleRejectClick(request)}
                    size="small"
                    style={rejectButtonStyle}
                    className="reject-btn"
                  />
                </Tooltip>
              </>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <Table
        className="blood-request-table"
        columns={columns}
        dataSource={enhancedRequests}
        rowKey="requestID"
        loading={loading || userDataLoading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} của ${total} yêu cầu`,
        }}
      />

      {/* Reject Request Modal */}
      <RejectRequestModal
        isOpen={showRejectModal}
        onClose={handleRejectModalClose}
        onConfirm={handleRejectConfirm}
        loading={rejectLoading}
        requestInfo={selectedRequestForReject}
      />
    </>
  );
};

export default DoctorBloodRequestsTable;
