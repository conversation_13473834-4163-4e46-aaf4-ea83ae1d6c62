import React from "react";
import { <PERSON><PERSON>crumb, Button, Space, Typography, Spin } from "antd";
import {
  HeartFilled,
  ReloadOutlined,
  HomeOutlined,
  UserOutlined,
  HistoryOutlined,
} from "@ant-design/icons";
import "../../styles/components/ModernPageHeader.scss";

const { Title, Text } = Typography;

/**
 * Modern PageHeader Component
 * Thiết kế hiện đại, đẹp mắt và đồng bộ với hệ thống
 */
const ModernPageHeader = ({
  title = "Lịch sử hoạt động",
  description = "<PERSON> dõi lịch sử hiến máu và yêu cầu máu của bạn",
  icon = <HeartFilled />,
  loading = false,
  onReload,
  variant = "default", // default, gradient, glass
  theme = "light", // light, dark
  breadcrumbItems = [
    {
      title: (
        <Space size={4}>
          <HomeOutlined />
          <span>Trang chủ</span>
        </Space>
      ),
    },
    {
      title: (
        <Space size={4}>
          <UserOutlined />
          <span>Thành viên</span>
        </Space>
      ),
    },
    {
      title: (
        <Space size={4}>
          <HistoryOutlined />
          <span>L<PERSON>ch sử hoạt động</span>
        </Space>
      ),
    },
  ],
  actions = [],
  className = "",
}) => {
  // Default actions nếu không có actions được truyền vào
  const defaultActions = [
    <Button
      key="reload"
      type="primary"
      size="large"
      icon={loading ? <Spin size="small" /> : <ReloadOutlined />}
      onClick={onReload}
      disabled={loading}
      className="reload-button"
    >
      {loading ? "Đang tải..." : "Làm mới"}
    </Button>,
  ];

  const finalActions = actions.length > 0 ? actions : defaultActions;

  return (
    <div className={`modern-page-header ${variant} ${theme} ${className}`}>
      <div className="page-header-container">
        <div className="page-header-content">
          <div className="header-main">
            <div className="header-title-wrapper">
              <div className="title-icon">{icon}</div>
              <div className="title-content">
                <Title level={1} className="page-title">
                  {title}
                </Title>
                <Text className="page-description">{description}</Text>
              </div>
            </div>

            <div className="header-actions">
              <Space size={12}>{finalActions}</Space>
            </div>
          </div>

          <div className="header-breadcrumb">
            <Breadcrumb
              items={breadcrumbItems}
              separator="›"
              className="modern-breadcrumb"
            />
          </div>
        </div>
      </div>

      {/* Decorative elements */}
      <div className="header-decoration">
        <div className="decoration-circle decoration-1"></div>
        <div className="decoration-circle decoration-2"></div>
        <div className="decoration-circle decoration-3"></div>
      </div>
    </div>
  );
};

export default ModernPageHeader;
