import React from "react";
import { Row, Col, Card, Statistic } from "antd";

/**
 * Component for displaying blood request statistics
 */
const BloodRequestStats = ({ requests, externalRequests, activeTab, isBloodDepartment }) => {
  // Determine current requests based on active tab
  const currentRequests =
    isBloodDepartment && activeTab === "external" ? externalRequests : requests;

  const stats = [
    {
      title: "Tổng yêu cầu",
      value: currentRequests.length,
      color: "#1677ff",
    },
    {
      title: "Chờ duyệt",
      value: currentRequests.filter((r) => r.status === 0).length,
      color: "#faad14",
    },
    {
      title: "Đã chấp nhận",
      value: currentRequests.filter((r) => r.status === 1).length,
      color: "#52c41a",
    },
    {
      title: "Hoàn thành",
      value: currentRequests.filter((r) => r.status === 2).length,
      color: "#1890ff",
    },
  ];

  return (
    <Row gutter={16} style={{ marginBottom: 24 }}>
      {stats.map((stat) => (
        <Col xs={24} sm={12} md={6} key={stat.title}>
          <Card>
            <Statistic
              title={stat.title}
              value={stat.value}
              valueStyle={{ color: stat.color, fontWeight: 600 }}
            />
          </Card>
        </Col>
      ))}
    </Row>
  );
};

export default BloodRequestStats;
