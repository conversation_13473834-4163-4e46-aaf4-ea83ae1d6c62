


export const getGenderText = (gender) => {
  if (!gender) return "Không xác định";
  
  const normalizedGender = gender.toLowerCase();
  
  switch (normalizedGender) {
    case 'male':
    case 'nam':
      return 'Nam';
    case 'female':
    case 'nữ':
    case 'nu':
      return 'Nữ';
    case 'other':
    case 'khác':
    case 'khac':
      return 'Khác';
    default:
      return 'Không xác định';
  }
};


export const getGenderValue = (genderText) => {
  if (!genderText) return '';
  
  const normalizedText = genderText.toLowerCase();
  
  switch (normalizedText) {
    case 'nam':
      return 'male';
    case 'nữ':
    case 'nu':
      return 'female';
    case 'khác':
    case 'khac':
      return 'other';
    default:
      return '';
  }
};

/**
 * Gender options for forms
 */
export const GENDER_OPTIONS = [
  { value: 'male', label: 'Nam' },
  { value: 'female', label: 'Nữ' },
  { value: 'other', label: 'Khác' },
];

/**
 * Gender filters for tables
 */
export const GENDER_FILTERS = [
  { text: 'Nam', value: 'male' },
  { text: 'Nữ', value: 'female' },
  { text: 'Khác', value: 'other' },
];

export default {
  getGenderText,
  getGenderValue,
  GENDER_OPTIONS,
  GENDER_FILTERS,
};
